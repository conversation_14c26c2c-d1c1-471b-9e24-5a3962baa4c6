import { Box, Container, Grid, Paper, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import ActivePieChart from 'Components/Charts/ActivePieChart';
import DateFiltersForm from 'Components/DateFilters/DateFiltersForm';
import { PageTitleBar } from 'Components/index';
import OnlineVisitorsWidget from 'Components/Widgets/OnlineVisitorsWidget';
import { RgbColors } from 'Constants/ThemeColors';
import api from 'FirebaseRef/api';
import { langMessages } from 'Lang/index';
import { ArrowDownLeftIcon } from 'lucide-react';
import moment from 'moment';
import React, { useContext, useEffect, useState } from 'react';
import { Helmet } from 'react-helmet';
import { connect } from 'react-redux';
import { useParams, withRouter } from 'react-router-dom';
import { Badge } from 'reactstrap';
import { CALLABLE_FUNCTIONS } from '../../../../firebase';
import { ProtectedPageContext } from '../../../../hooks/ProtectPage';
import { PixelHunterService } from '../../../../services/PixelHunterService';
import { HunterEvents } from '../model/HunterEvents';
import ClickDetails from './components/ClickDetais';
import EventsEvolutionChart from './components/EventsEvolutionChart';
import { FiltersCard } from './components/FiltersCard';
import MetricCard from './components/MetricCard';

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: '40px',
  },
  paper: {
    height: '100%',
    transition: 'transform 0.2s ease-in-out',
    '&:hover': {
      transform: 'translateY(-4px)',
    },
    padding: theme.spacing(2),
  },
  title: {
    padding: theme.spacing(2),
  },
  gridContainer: {
    padding: theme.spacing(2),
  },
  legends: {
    padding: theme.spacing(2),
    overflowY: 'auto',
    height: '150px',
    scrollbarWidth: 'thin',
    '&::-webkit-scrollbar': {
      width: '4px',
    },
    '&::-webkit-scrollbar-track': {
      background: '#f1f1f1',
    },
    '&::-webkit-scrollbar-thumb': {
      background: '#888',
    },
    '&::-webkit-scrollbar-thumb:hover': {
      background: '#555',
    },
  },
}));

const Page = ({ match, history, darkMode, unwrap }) => {
  const { id: pixelId } = useParams();
  const service = new PixelHunterService(pixelId)

  const {
    doc
  } = useContext(ProtectedPageContext)

  const classes = useStyles();
  const [dateRange, setDateRange] = React.useState({
    start: moment().subtract(1, 'month').valueOf(),
    end: moment().valueOf(),
    range: 'last30Days',
  });

  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);

  const routes = logs.reduce((acc, log) => {
    const route = log.metadata?.route || '/';

    if (!acc[route]) {
      acc[route] = {
        value: route,
        label: route,
        logs: [],
      }
    }

    acc[route].logs.push(log);

    return acc
  }, {});

  const [filters, setFilters] = useState({});
  const handleFilterChange = (filterBy, value) => {
    const updateFilters = { ...filters }
    // Se já tem filtro
    if (updateFilters[filterBy]) {
      if (!value) { // Se não tem valor
        delete updateFilters[filterBy]
      } else {
        updateFilters[filterBy].value = value // Atualizar filtro
      }
    } else { // Se nao tem filtro
      if (value) { // Se tem valor
        updateFilters[filterBy] = { value, filterBy } // Adicionar filtro
      }
    }

    setFilters(updateFilters)
  }

  const filteredLogs = logs.filter((log) => {
    for (const [key, { filterBy, value }] of Object.entries(filters)) {
      let current = log

      for (const field of filterBy.split('.')) {
        if (!current[field]) {
          return false
        }

        current = current[field]
      }
      if (current !== value) {
        return false
      }
    }
    return true
  })


  const uniqueQueryKeys = filteredLogs
    .filter((log) => log.query_params)
    .reduce((acc, log) => {
      const filters = log.query_params

      for (const [filter, value] of Object.entries(filters)) {
        if (!acc[filter]) {
          acc[filter] = {
            value: filter,
            label: filter,
            logs: [],
          }
        }
      }

      return acc
    }, {})

  const uniqueQueryValues = filteredLogs
    .filter((log) => log.query_params)
    .reduce((acc, log) => {
      const filters = log.query_params

      for (const [key, value] of Object.entries(filters)) {
        if (!acc[value]) {
          acc[value] = {
            params: key,
            value: value,
            label: value,
            logs: [],
          }
        }
      }

      return acc
    }, {})

  const prepareLogsFilters = (logs) => {
    return logs.map((log) => {
      const queries = log.metadata?.urlQuery
      if (!queries) return log

      const prepared = { ...log }
      let filters = {}

      for (const [query, value] of Object.entries(queries)) {
        filters = {
          ...filters,
          [query]: value
        }
      }
      prepared['query_params'] = filters

      return prepared
    })
  }

  const fetchLogs = async () => {
    if (pixelId && !loading) {
      setLoading(true);

      service.getLogs(dateRange, (logs) => {
        setLogs(prepareLogsFilters(logs));
        setLoading(false);
      });
    }
  }

  const pageEvents = Object.values(HunterEvents)
    .reduce((acc, ev) => {
      if (!acc[ev.key]) {
        acc[ev.key] = 0;
      }
      acc[ev.key] += filteredLogs.filter(log => log.trigger === ev.key).length
      return acc
    }, { ...Object.keys(HunterEvents) })

  useEffect(() => { fetchLogs() }, [pixelId, dateRange]);

  const visitors = filteredLogs
    .filter((log) => log.trigger === HunterEvents.pageview.key)
    .reduce((acc, log) => {
      const visitorIp = log?.data?.remote_addr;

      if (!acc[visitorIp]) {
        acc[visitorIp] = 0;
      }

      acc[visitorIp]++
      return acc
    }, {})
  const visitorsKeys = Object.keys(visitors)

  const randomColor = () => {
    var colors = Object.keys(RgbColors)
    return RgbColors[colors[Math.floor(Math.random() * colors.length)]]
  }
  const origins = filteredLogs.reduce((acc, log) => {
    let origin = log.data?.referrer || log.data?.origin_url || log.metadata?.url;
    if (origin) {
      if (origin.includes('%')) {
        origin = decodeURIComponent(origin)
      }

      origin = String(origin)
      if (origin.includes('://')) {
        origin = origin.split('://')[1]
      }

      if (origin.includes('/')) {
        origin = origin.split('/')[0]
      }

      if (origin.includes('?')) {
        origin = origin.split('?')[0]
      }

      if (!acc[origin]) {
        acc[origin] = {
          value: 0,
          fill: randomColor(),
          name: origin
        }
      }

      acc[origin].value++
    }
    return acc
  }, {})

  const [geoIpData, setGeoIpData] = useState([])
  const [geoUaData, setGeoUaData] = useState([])

  async function getGeoipData() {
    const data = visitorsKeys.map(visitor => { return { ip: visitor } })
    if (!data.length) return
    api(CALLABLE_FUNCTIONS.CALLABLE_GET_GEOIP_DATA, { data })
      .then(response => {
        setGeoIpData(response.filter(({ geo, error }) => !error && geo?.ll));
      })
      .catch(error => {
        console.error(error);
      })
  }

  async function getGeoUaData() {
    const data = filteredLogs.map(log => { return { ua: log?.data?.user_agent } }).filter(({ ua }) => ua)
    if (!data.length) return

    api(CALLABLE_FUNCTIONS.CALLABLE_GET_GEOIP_DATA, { data })
      .then(response => {
        setGeoUaData(response.filter(({ agent, error: error_1 }) => !error_1 && agent?.ua));
      })
      .catch(error => {
        console.error(error);
      })
  }

  useEffect(() => {
    if (logs.length) {
      getGeoipData()
      getGeoUaData()
    }
  }, [logs])

  const geoStats = geoIpData
    .filter((data) => visitors[data.ip])
    .map(({ ip, geo }) => ({ ip, ...geo, count: visitors[ip] }))

  const groupedByLocation = geoStats.reduce((acc, current) => {
    const location = `${current.ll.join(', ')}`;

    if (!acc[location]) {
      acc[location] = {
        ...current,
        count: 0
      };
    }

    acc[location].count += current.count;

    return acc;
  }, {});

  const groupedStats = Object.values(groupedByLocation);

  let icons = {
    desktop: 'f108',
    mobile: 'f10b',
  }
  let iconSizes = {
    desktop: 20,
    mobile: 24,
  }

  const userAgents = geoUaData.reduce((acc, { agent: { os: { name }, device: { type = 'desktop' } } }) => {

    if (!acc[type]) acc[type] = {
      name: type,
      value: 0,
      osName: type,
      legends: [],
      auxLegends: {},
      fill: randomColor(),
      icon: icons[type],
      iconSize: iconSizes[type]
    }
    acc[type].value++

    if (!acc[type].auxLegends[name]) acc[type].auxLegends[name] = { name, value: 0 }

    acc[type].auxLegends[name].value++
    acc[type].legends = Object.values(acc[type].auxLegends)

    return acc
  }, {})

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </Box>
    );
  }

  const height = window.innerWidth / 7

  return (
    <div className="ecom-dashboard-wrapper">
      <Helmet>
        <title>{langMessages["campaigns.hunter.analytics.title"]}</title>
        <meta name="description" content="QIPlus || Analytics" />
      </Helmet>
      <PageTitleBar
        title={langMessages["campaigns.hunter.analytics.title"]}
        subtitle={doc?.name}
        match={match}
        history={history}
        rightComponents={
          <div className="d-flex justify-content-end">
            <DateFiltersForm filters={dateRange} onUpdate={setDateRange} />
          </div>
        }
      />
      <div className="dashboard-wrapper widgets-wrapper mb-4">
        <div className="row">
          <Container maxWidth="xl" className={classes.root}>
            <Grid container spacing={3} className={classes.gridContainer}>
              <Grid item xs={12}>
                <FiltersCard
                  filters={{
                    // page: {
                    //   label: langMessages['campaigns.hunter.analytics.filters.byPage'],
                    //   type: 'select',
                    //   options: Object.values(pages).map(({ id, name }) => ({ value: id, label: name })) || [],
                    //   filterBy: 'metadata.pagetitle',
                    // },
                    rota: {
                      type: 'autocomplete',
                      label: langMessages['campaigns.hunter.analytics.filters.byRoute'],
                      tooltip: langMessages['campaigns.hunter.analytics.filters.byRouteTooltip'],
                      filterBy: 'metadata.route',
                      options: Object.values(routes),
                    },
                    query_param: {
                      type: 'grouped-autocomplete',
                      children: {
                        param: {
                          label: langMessages['campaigns.hunter.analytics.filters.byQueryParamName'],
                          options: Object.values(uniqueQueryKeys),
                        },
                        value: {
                          label: langMessages['campaigns.hunter.analytics.filters.byQueryParamValue'],
                          options: Object.values(uniqueQueryValues),
                        },
                      },
                      filterBy: 'query_params',
                    },
                  }}
                  onFilterChange={handleFilterChange}
                />
              </Grid>

              {Object.values(HunterEvents).reverse().map(({ key, icon }) => (
                <Grid item xs={12} sm={6} md={4} key={key}>
                  <MetricCard
                    title={langMessages[`campaigns.hunter.events.${key}`]}
                    count={key === HunterEvents.pageview.key ? visitorsKeys.length : null}
                    countDescription={key === HunterEvents.pageview.key ? 'visitantes únicos' : null}
                    value={pageEvents[key]}
                    icon={icon}
                    viewMoreChildren={key === HunterEvents.click.key ? <ClickDetails logs={filteredLogs} /> : null}
                  />
                </Grid>
              ))}

              <Grid item xs={12} sm={6} md={4}>
                <MetricCard
                  title={'Origens de tráfego'}
                  value={Object.keys(origins).length}
                  icon={<ArrowDownLeftIcon />}
                />
              </Grid>

              <Grid item xs={12} sm={12} md={12}>
                <EventsEvolutionChart logs={filteredLogs} />
              </Grid>
              <Grid item xs={12} sm={6} md={6}>
                <Paper className={classes.paper}>
                  <Typography className={classes.title} variant="h6" gutterBottom>
                    {langMessages['stats.geoLocation']}
                  </Typography>
                  <OnlineVisitorsWidget
                    geoStats={groupedStats}
                    zoom={true}
                    height={height}
                    unwrap={!!unwrap}
                    background={darkMode ? '#2c3644' : '#fff'}
                  />
                </Paper>
              </Grid>

              <Grid item xs={12} sm={6} md={6}>
                <Paper className={classes.paper}>
                  <Typography className={classes.title} variant="h6" gutterBottom>
                    {langMessages['stats.trafficSources']}
                  </Typography>
                  <div className="d-flex justify-content-between flex-1">
                    <div className="flex-1 d-flex justify-content-center align-items-center">
                      <ActivePieChart
                        height={height}
                        showLegend={false}
                        externalLegends={true}
                        titleSize={12}
                        fontColor={darkMode ? '#fff' : ''}
                        data={Object.values(origins)}
                      />
                    </div>
                    <div className="flex-1 d-flex justify-content-center align-items-center">
                      <ActivePieChart
                        height={height}
                        showLegend={false}
                        externalLegends={true}
                        titleSize={12}
                        fontColor={darkMode ? '#fff' : ''}
                        data={Object.values(userAgents)}
                      />
                    </div>
                  </div>
                  <div className={classes.legends}>
                    {Object.values(origins)
                      .sort((a, b) => b.value - a.value)
                      .map(({ fill, name, value }, k) => (
                        <div key={k} className="w-full d-flex justify-content-between p-5 gap-5 bg-light">
                          <div className="flex-1 d-flex">
                            <Typography className="fs-14">{name}</Typography>
                          </div>
                          <Badge style={{ backgroundColor: fill }} className={'px-4'}>{value}</Badge>
                        </div>
                      ))}
                  </div>
                </Paper>
              </Grid>
            </Grid>
          </Container>
        </div>
      </div>
    </div>
  )
};

// map state to props
const mapStateToProps = ({ authReducer, settings }) => {
  const { user, account, ownerId } = authReducer;
  const { darkMode } = settings;

  return { user, account, accountId: account.ID, ownerId, darkMode };
}

export const PixelHunterAnalytics = withRouter(connect(mapStateToProps, null)(Page));
