/**
 * Events Actions
 */
import { FirestoreRef } from 'FirebaseRef'

// lang strings
import { langMessages } from '../lang'

import {
  GET_EVENT_PARTICIPANTS,
  GET_EVENT_PARTICIPANTS_SUCCESS,
  GET_EVENT_PARTICIPANTS_FAILURE,
  ADD_EVENT_PARTICIPANT,
  ADD_EVENT_PARTICIPANT_SUCCESS,
  ADD_EVENT_PARTICIPANT_FAILURE,
  CONFIRM_EVENT_PARTICIPANT,
  CONFIRM_EVENT_PARTICIPANT_SUCCESS,
  CONFIRM_EVENT_PARTICIPANT_FAILURE,
  DISCONFIRM_EVENT_PARTICIPANT,
  DISCONFIRM_EVENT_PARTICIPANT_SUCCESS,
  DISCONFIRM_EVENT_PARTICIPANT_FAILURE,
  CHECKIN_EVENT_PARTICIPANT,
  CHECKIN_EVENT_PARTICIPANT_SUCCESS,
  CHECKIN_EVENT_PARTICIPANT_FAILURE,
  UNDO_CHECKIN_EVENT_PARTICIPANT,
  UNDO_CHECKIN_EVENT_PARTICIPANT_SUCCESS,
  UNDO_CHECKIN_EVENT_PARTICIPANT_FAILURE,
  DELETE_EVENT_PARTICIPANT,
  DELETE_EVENT_PARTICIPANT_SUCCESS,
  DELETE_EVENT_PARTICIPANT_FAILURE,
} from 'Actions/types'

import moment from 'moment'
import { MOMENT_ISO } from 'Constants'
import { EVENTS_COLLECTION_NAME, PARTICIPANTS_COLLECTION_NAME } from 'Constants'
import participantModel from 'Models/childnodes/participant'

const collectionName = EVENTS_COLLECTION_NAME
const participantsCollection = PARTICIPANTS_COLLECTION_NAME

/**
 * Redux Action Get Participants
 */
export const getParticipants = eventId => (dispatch, getState) => {
  dispatch({ type: GET_EVENT_PARTICIPANTS })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${eventId}`)
      .collection(participantsCollection)
      .get()
      .then(snapshot => {
        const participants = []
        snapshot.forEach(doc => {
          participants.push(doc.data())
        })
        dispatch({ type: GET_EVENT_PARTICIPANTS_SUCCESS, ID: eventId, payload: participants })
        return resolve(participants)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: GET_EVENT_PARTICIPANTS_FAILURE })
      })
  }).catch(err => console.log('err', err) /* ||dispatch({ type: GET_EVENT_PARTICIPANTS_FAILURE }) */)
}

/**
 * Redux Action Add Participant
 */
export const addParticipant = (eventId, participant) => (dispatch, getState) => {
  dispatch({ type: ADD_EVENT_PARTICIPANT })
  return new Promise((resolve, reject) => {
    const { ID } = participant

    const newParticipant = {
      ...participantModel,
      ...participant,
      added: moment().format(MOMENT_ISO),
      modified: moment().format(MOMENT_ISO),
    }

    FirestoreRef.collection(collectionName)
      .doc(`${eventId}`)
      .collection(participantsCollection)
      .doc(`${ID}`)
      .set(newParticipant)
      .then(result => {
        dispatch({ type: ADD_EVENT_PARTICIPANT_SUCCESS, ID: eventId, payload: newParticipant })
        return resolve(newParticipant)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: ADD_EVENT_PARTICIPANT_FAILURE })
      })
  }).catch(err => console.log('err', err) /* ||dispatch({ type: GET_EVENT_PARTICIPANTS_FAILURE }) */)
}

/**
 * Redux Action Add Participant
 */
export const onConfirmCheckin = (eventId, participantId) => (dispatch, getState) => {
  dispatch({ type: CHECKIN_EVENT_PARTICIPANT })
  return new Promise((resolve, reject) => {
    const updatedData = { checkin: true, modified: moment().format(MOMENT_ISO), checkinTime: moment().format(MOMENT_ISO) }
    FirestoreRef.collection(collectionName)
      .doc(`${eventId}`)
      .collection(participantsCollection)
      .doc(`${participantId}`)
      .update(updatedData)
      .then(result => {
        dispatch({ type: CHECKIN_EVENT_PARTICIPANT_SUCCESS, ID: eventId, payload: updatedData, participantId })
        return resolve(participantId)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: CHECKIN_EVENT_PARTICIPANT_FAILURE })
      })
  }).catch(err => console.log('err', err))
}

/**
 * Redux Action Add Participant
 */
export const onDisconfirmCheckin = (eventId, participantId) => (dispatch, getState) => {
  dispatch({ type: UNDO_CHECKIN_EVENT_PARTICIPANT })
  return new Promise((resolve, reject) => {
    const updatedData = { checkin: false, modified: moment().format(MOMENT_ISO), disconfirmCheckinTime: moment().format(MOMENT_ISO) }
    FirestoreRef.collection(collectionName)
      .doc(`${eventId}`)
      .collection(participantsCollection)
      .doc(`${participantId}`)
      .update(updatedData)
      .then(result => {
        dispatch({ type: UNDO_CHECKIN_EVENT_PARTICIPANT_SUCCESS, ID: eventId, payload: updatedData, participantId })
        return resolve(participantId)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: UNDO_CHECKIN_EVENT_PARTICIPANT_FAILURE })
      })
  }).catch(err => console.log('err', err))
}

/**
 * Redux Action Add Participant
 */
export const onConfirmParticipant = (eventId, participantId) => (dispatch, getState) => {
  dispatch({ type: CONFIRM_EVENT_PARTICIPANT })
  return new Promise((resolve, reject) => {
    const updatedData = { confirmed: true, modified: moment().format(MOMENT_ISO), confirmedTime: moment().format(MOMENT_ISO) }
    FirestoreRef.collection(collectionName)
      .doc(`${eventId}`)
      .collection(participantsCollection)
      .doc(`${participantId}`)
      .update(updatedData)
      .then(result => {
        dispatch({ type: CONFIRM_EVENT_PARTICIPANT_SUCCESS, ID: eventId, payload: updatedData, participantId })
        return resolve(participantId)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: CONFIRM_EVENT_PARTICIPANT_FAILURE })
      })
  }).catch(err => console.log('err', err) /* ||dispatch({ type: GET_EVENT_PARTICIPANTS_FAILURE }) */)
}

/**
 * Redux Action Add Participant
 */
export const onDisconfirmParticipant = (eventId, participantId) => (dispatch, getState) => {
  dispatch({ type: DISCONFIRM_EVENT_PARTICIPANT })
  return new Promise((resolve, reject) => {
    const updatedData = { confirmed: false, modified: moment().format(MOMENT_ISO), disconfirmedTime: moment().format(MOMENT_ISO) }
    FirestoreRef.collection(collectionName)
      .doc(`${eventId}`)
      .collection(participantsCollection)
      .doc(`${participantId}`)
      .update(updatedData)
      .then(result => {
        dispatch({ type: DISCONFIRM_EVENT_PARTICIPANT_SUCCESS, ID: eventId, payload: updatedData, participantId })
        return resolve(participantId)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: DISCONFIRM_EVENT_PARTICIPANT_FAILURE })
      })
  }).catch(err => console.log('err', err) /* ||dispatch({ type: GET_EVENT_PARTICIPANTS_FAILURE }) */)
}

/**
 * Redux Action Add Participant
 */
export const onRemoveParticipant = (eventId, participantId) => (dispatch, getState) => {
  dispatch({ type: DELETE_EVENT_PARTICIPANT })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${eventId}`)
      .collection(participantsCollection)
      .doc(`${participantId}`)
      .delete()
      .then(result => {
        dispatch({ type: DELETE_EVENT_PARTICIPANT_SUCCESS, ID: eventId, payload: participantId })
        return resolve(participantId)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: DELETE_EVENT_PARTICIPANT_FAILURE })
      })
  }).catch(err => console.log('err', err) /* ||dispatch({ type: GET_EVENT_PARTICIPANTS_FAILURE }) */)
}
