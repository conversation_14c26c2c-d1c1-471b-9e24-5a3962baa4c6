import {
  Box,
  Button,
  Checkbox,
  List,
  ListItem,
  ListItemText
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { langMessages } from 'Lang/index';
import React from 'react';

const useStyles = makeStyles((theme) => ({
  listContainer: {
    maxHeight: 300,
    overflow: 'auto',
  },
  chip: {
    margin: theme.spacing(0.5),
  },
  actions: {
    marginBottom: theme.spacing(2),
    '& > button': {
      marginRight: theme.spacing(2),
    },
  },
}));

const SegmentationsList = ({
  segmentations,
  selectedSegmentations,
  onSelectSegmentation,
  onSelectAll,
  onClearSelection,
  disabled = false
}) => {

  const classes = useStyles();

  return (
    <>
      <Box className={classes.actions}>
        <Button variant="outlined" color="primary" onClick={onSelectAll} disabled={disabled}>
          {langMessages['shotx.broadcast.selectAll']}
        </Button>
        <Button variant="outlined" onClick={onClearSelection} disabled={disabled}>
          {langMessages['shotx.broadcast.selectNone']}
        </Button>
      </Box>
      <List className={classes.listContainer}>
        {segmentations.map((segmentation) => (
          <ListItem key={segmentation.id} disabled={disabled} button onClick={() => onSelectSegmentation(segmentation.id)}>
            <Checkbox checked={selectedSegmentations.includes(segmentation.id)} />
            <ListItemText
              primary={segmentation.title}
              secondary={
                <Box>
                  {segmentation.goals}
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>
    </>
  );
};

export default SegmentationsList;
