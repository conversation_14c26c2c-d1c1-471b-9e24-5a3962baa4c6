/**
 * Email App Actions
 */

import { FirestoreRef, checkMailboxAvailability } from 'FirebaseRef'

import moment from 'moment'

import { langMessages } from '../lang'

import {
  GET_MAILBOX,
  GET_MAILBOX_SUCCESS,
  GET_MAILBOX_FAILURE,
  GET_<PERSON>ILBOXES,
  GET_<PERSON><PERSON>BOXES_SUCCESS,
  GET_MAILBOXES_FAILURE,
  SAVE_MAILBOX,
  SAVE_MAILBOX_SUCCESS,
  SAVE_<PERSON>ILBOX_FAILURE,
  TRASH_MAILBOX,
  TRASH_MAILBOX_SUCCESS,
  TRASH_MAILBOX_FAILURE,
  GET_EMAIL,
  GET_EMAIL_SUCCESS,
  GET_EMAIL_FAILURE,
  SET_EMAIL_AS_STAR,
  OPEN_EMAIL,
  HIDE_LOADING_INDICATOR,
  ON_SELECT_EMAIL,
  UPDATE_EMAIL_SEARCH,
  SEARCH_EMAIL,
  ON_TRASH_EMAIL,
  ON_TRASH_EMAIL_SUCCESS,
  ON_TRASH_EMAIL_FAILURE,
  ON_DELETE_MAIL_SUCCESS,
  ON_BACK_PRESS_NAVIGATE_TO_EMAIL_LISTING,
  GET_FOLDER_EMAILS,
  GET_FOLDER_EMAILS_SUCCESS,
  GET_FOLDER_EMAILS_FAILURE,
  ON_SELECT_MAILBOX,
  ON_SELECT_FOLDER,
  ON_CHANGE_PAGE,
  ON_EMAIL_MOVE_TO_FOLDER,
  ON_EMAIL_MOVE_TO_FOLDER_SUCCESS,
  ON_EMAIL_MOVE_TO_FOLDER_FAILURE,
  SELECT_ALL_EMAILS,
  UNSELECT_ALL_EMAILS,
  ON_SAVE_EMAIL,
  ON_SAVE_EMAIL_SUCCESS,
  ON_SAVE_EMAIL_FAILURE,
  ON_SEND_EMAIL,
  ON_SEND_EMAIL_SUCCESS,
  ON_SEND_EMAIL_FAILURE,
  EMAIL_SENT_SUCCESSFULLY,
  FILTER_EMAILS_WITH_LABELS,
  RESET_EMAILS_FILTERS,
  RESET_FOLDER_EMAILS,
  ADD_LABELS_INTO_EMAILS,
} from 'Actions/types'

import { localJSON, sessionJSON } from 'Helpers'
import { validateEmail } from 'Helpers/formHelpers'
import { sTrim } from 'Helpers/helpers'

import { OWNER_LEVEL } from 'Constants/UsersRoles'

import { MOMENT_ISO, DEFAULT_LOCALE } from 'Constants'

import emailSettingsModel from 'Models/childnodes/email'
import mailboxModel from 'Routes/mail/model/mailbox'

import COLLECTIONS, { TRASH_COLLECTION_NAME } from '../constants/AppCollections'
import { TRASH_STATUS, qiplusEmailDomains } from '../constants/AppConstants'

const collectionName = COLLECTIONS.MAILBOXES_COLLECTION_NAME
const emailsCollection = COLLECTIONS.MAIL_COLLECTION_NAME

/**
 * Redux Action To Mark As Star Email
 */
export const markAsStarEmail = email => ({
  type: SET_EMAIL_AS_STAR,
  payload: email,
})

/**
 * Redux Action To Read Email
 */
export const onOpenEmail = email => ({
  type: OPEN_EMAIL,
  payload: email,
})

/**
 * Redux Action To Hide Loading Indicator
 */
export const hideLoadingIndicator = () => ({
  type: HIDE_LOADING_INDICATOR,
})

/**
 * Redux Action On Select Email
 */
export const onSelectEmail = email => ({
  type: ON_SELECT_EMAIL,
  payload: email,
})

/**
 * Redux Action To Update Search Email
 */
export const updateSearchEmail = searchText => ({
  type: UPDATE_EMAIL_SEARCH,
  payload: searchText,
})

/**
 * Redux Action To Search Email
 */
export const searchEmail = searchText => ({
  type: SEARCH_EMAIL,
  payload: searchText,
})

/**
 * Redux Action To Trash Email
 */
export const onTrashEmail = (mailboxId, folder, data) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    dispatch({ type: ON_TRASH_EMAIL, folder, mailboxId })

    let { mailId } = data

    if (!mailId) {
      return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: trash 01` })
    }

    FirestoreRef.collection(collectionName)
      .doc(mailboxId)
      .collection(folder)
      .doc(mailId)
      .get()
      .then(doc => {
        let dbData = doc.data()
        let docRef = doc.ref

        FirestoreRef.collection(collectionName)
          .doc(mailboxId)
          .collection('trash')
          .doc(mailId)
          .set(dbData)
          .then(doc => {
            docRef.delete()
            dispatch({ type: ON_TRASH_EMAIL_SUCCESS, folder, mailboxId, mailId })
            return resolve({ mailId })
          })
          .catch(error => {
            dispatch({ type: ON_TRASH_EMAIL_FAILURE, folder, mailboxId, mailId, error })
            reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: trash 02` })
          })
      })
      .catch(error => {
        dispatch({ type: ON_TRASH_EMAIL_FAILURE, folder, mailboxId, mailId, error })
        reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: trash 03` })
      })
  })
}

/**
 * Redux Action To Delete Email
 */
export const onDeleteEmail = (mailboxId, folder, mailId) => (dispatch, getState) => {
  dispatch({ type: ON_DELETE_MAIL_SUCCESS, folder, mailboxId, mailId })
}

/**
 * Redux Action On Back Press To Navigate Email Listing
 */
export const onNavigateToEmailListing = folder => ({
  type: ON_BACK_PRESS_NAVIGATE_TO_EMAIL_LISTING,
  folder,
})

/**
 * Redux Action To Get Inbox
 */
export const getMailboxes = listenerFn => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  dispatch({ type: GET_MAILBOXES })

  let QueryRef = FirestoreRef.collection(collectionName).where('userId', '==', user.ID).where('validated', '==', true)

  return new Promise(res => {
    const refListener = QueryRef.onSnapshot(
      snapshot => {
        // console.groupCollapsed('listenMailboxes > snapshot');
        // console.log('snapshot:',snapshot.docs);
        // console.groupEnd('listenMailboxes > snapshot');

        const mailboxes = []
        snapshot.forEach(doc => {
          mailboxes.push(doc.data())
        })

        dispatch({ type: GET_MAILBOXES_SUCCESS, payload: mailboxes })
        listenerFn && listenerFn({ mailboxes, refListener })
      },
      error => {
        console.error('getMailboxes error', error)
        dispatch({ type: GET_MAILBOXES_FAILURE })
      }
    )
    return res(refListener)
  })
}

/**
 * Redux Action To Get Mailbox
 */
export const getMailbox = mailboxId => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  dispatch({ type: GET_MAILBOX })

  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(mailboxId)
      .get()
      .then(doc => {
        if (doc.exists) {
          const mailboxData = doc.data()

          dispatch({ type: GET_MAILBOX_SUCCESS, payload: mailboxData })
          return resolve(mailboxData)
        } else {
          dispatch({ type: GET_MAILBOX_FAILURE, mailboxId })
          return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: get mailbox 02` })
        }
      })
      .catch(error => {
        console.error('getMailbox error', error)
        dispatch({ type: GET_MAILBOX_FAILURE, mailboxId })
        return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: get mailbox 03` })
      })
  })
}

/**
 * Redux Action To Get Mailbox
 */
export const listenMailbox = (mailboxId, listenerFn) => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  dispatch({ type: GET_MAILBOX })

  let QueryRef = FirestoreRef.collection(collectionName).doc(mailboxId)

  return new Promise(res => {
    const refListener = QueryRef.onSnapshot(
      doc => {
        // console.groupCollapsed('listenMailbox > doc');
        // console.log('doc:',doc.docs);
        // console.groupEnd('listenMailbox > doc');

        const mailboxData = doc.data()

        if (mailboxData) {
          dispatch({ type: GET_MAILBOX_SUCCESS, payload: mailboxData })
          listenerFn && listenerFn({ data: mailboxData, refListener })
        } else {
          dispatch({ type: GET_MAILBOX_FAILURE, mailboxId })
        }
      },
      error => {
        console.error('getMailbox error', error)
        dispatch({ type: GET_MAILBOX_FAILURE, mailboxId })
      }
    )
    return res(refListener)
  })
}

/**
 * Redux Action To Check Mailbox Availability
 */

export const isMailboxAvailable = data => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  let { provider, from, mailboxId } = data

  from = sTrim(from)

  // console.log('componentDidMount', this.props);

  return new Promise((res, rej) => {
    if (!validateEmail(sTrim(from))) {
      return res({ available: false, error: langMessages['errors.invalidEmailAddress'] })
    }

    if (data && mailboxId && data.mailbox === from) {
      return res({ available: true })
    }

    if (qiplusEmailDomains.includes(provider)) {
      return checkMailboxAvailability({ mailbox: from, user, mailboxData: data }).then(({ data: { available, suggestions, error } }) => {
        console.log({ available, suggestions, error })
        let errorMsg = ''
        if (error) {
          if (error.message) {
            error = error.message
          } else if (typeof error !== 'string') {
            error = langMessages['errors.genericErrorMsg']
          }
          errorMsg = error
          switch (error) {
            case 'MODULE_NOT_AVAILABLE':
              errorMsg = langMessages['errors.MODULE_NOT_AVAILABLE']
              break
            case 'MODULE_LIMIT_REACHED':
              errorMsg = langMessages['placeholders.MODULE_LIMIT_REACHED'].replace(
                '[%s]',
                langMessages[`modules.${collectionName}`] || collectionName
              )
              break
            case 'EMAIL_EXISTS_ERROR':
              errorMsg = langMessages['alerts.emailAlreadyExists']
              break
            case 'MISSING_REQUIRED_FIELDS':
            case 'NO_ACCOUNT_FOUND':
              errorMsg = langMessages['errors.genericErrorMsg']
              break
            default:
              break
          }
        }
        return res({ available, suggestions, error: errorMsg })
      })
    }

    return res({ available: true })
  })
}

/**
 * Redux Action To Get Mailbox
 */
export const onSaveMailbox = data => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  // owner
  const currentOwnerID = sessionJSON.get('currentOwnerID', false)
  const ownerId = currentOwnerID || user.owner || (user.level <= OWNER_LEVEL && user.ID) || ''
  const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
  const accountId = data.accountId || currentAccountID || user.accountId
  const owner = data.owner || ownerId
  // end owner

  const { mailboxId } = data

  return new Promise((resolve, reject) => {
    if (!mailboxId) {
      return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: mbx 01` })
    }

    let rejected = false
    let requiredFields = ['provider', 'from', 'fromName']

    requiredFields.forEach(field => {
      if (!rejected && !data[field]) {
        rejected = true
        reject({
          error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages[`email.fields.${field}`]),
        })
      }
    })

    if (rejected) return

    dispatch({ type: SAVE_MAILBOX })

    isMailboxAvailable(data)() // returns dispatcher
      .then(({ available, error, suggestions }) => {
        if (!available) {
          return reject({ error, suggestions })
        }

        FirestoreRef.collection(collectionName)
          .doc(mailboxId)
          .update(data)
          .then(doc => {
            let mailboxData = {
              ...data,
              ID: mailboxId,
            }
            dispatch({ type: SAVE_MAILBOX_SUCCESS, payload: mailboxData })
            return resolve(mailboxData)
          })
          .catch(error => {
            console.error('getMailbox error', error)
            dispatch({ type: SAVE_MAILBOX_FAILURE })
            reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: mbx 02` })
          })
      })
  })
}

/**
 * Redux Action To Get Mailbox
 */
export const onSaveNewMailbox = data => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  // owner
  const currentOwnerID = sessionJSON.get('currentOwnerID', false)
  const ownerId = currentOwnerID || user.owner || (user.level <= OWNER_LEVEL && user.ID) || ''
  const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
  const accountId = data.accountId || currentAccountID || user.accountId
  const owner = data.owner || ownerId
  // end owner

  let newMailbox = {
    ...mailboxModel,
    ...window.jsonClone(data),
    collection: collectionName,
    locale: data.locale || DEFAULT_LOCALE,
    owner,
    accountId,
    userId: `${user.ID}`,
  }

  return new Promise((resolve, reject) => {
    let rejected = false
    let requiredFields = ['provider', 'from', 'fromName']

    requiredFields.forEach(field => {
      if (!rejected && !data[field]) {
        rejected = true
        reject({
          error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages[`email.fields.${field}`]),
        })
      }
    })

    if (rejected) return

    dispatch({ type: SAVE_MAILBOX })

    isMailboxAvailable(newMailbox)() // returns dispatcher
      .then(({ available, error, suggestions }) => {
        if (!available) {
          return reject({ error, suggestions })
        }

        FirestoreRef.collection(collectionName)
          .add(newMailbox)
          .then(doc => {
            let mailboxData = {
              ...newMailbox,
              ID: doc.id,
              id: doc.id,
              mailboxId: doc.id,
            }
            dispatch({ type: SAVE_MAILBOX_SUCCESS, payload: mailboxData })
            return resolve(mailboxData)
          })
          .catch(error => {
            console.error('getMailbox error', error)
            dispatch({ type: SAVE_MAILBOX_FAILURE })
          })
      })
  })
}

export const onTrashMailbox = mailboxId => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    const user = localJSON.get('user', false)

    dispatch({ type: TRASH_MAILBOX, mailboxId })

    FirestoreRef.collection(collectionName)
      .doc(mailboxId)
      .get()
      .then(doc => {
        let dbData = doc.data()
        let docRef = doc.ref

        FirestoreRef.collection(TRASH_COLLECTION_NAME)
          .doc(collectionName)
          .collection(`${user.ID}`)
          .doc(mailboxId)
          .set({ ...dbData, status: TRASH_STATUS })
          .then(doc => {
            docRef.delete()
            dispatch({ type: TRASH_MAILBOX_SUCCESS, mailboxId })
            return resolve({ mailboxId })
          })
          .catch(error => {
            dispatch({ type: TRASH_MAILBOX_FAILURE, mailboxId, error })
            reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: trash 02` })
          })
      })
      .catch(error => {
        dispatch({ type: TRASH_MAILBOX_FAILURE, mailboxId, error })
        reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: trash 03` })
      })
  })
}

/**
 * Redux Action To Get Inbox
 */
export const getEMail = (mailboxId, folder, mailId, listenerFn) => (dispatch, getState) => {
  dispatch({ type: GET_EMAIL, folder })

  let QueryRef = FirestoreRef.collection(collectionName).doc(mailboxId).collection(folder).doc(mailId)

  return new Promise(res => {
    const refListener = QueryRef.onSnapshot(
      doc => {
        // console.groupCollapsed('listenEmail > doc');
        // console.log('doc:',doc.docs);
        // console.groupEnd('listenEmail > doc');

        const email = doc.data()

        if (email) {
          if (Array.isArray(email.attachments)) {
            email.attachments.forEach(a => {
              if (a.attachment_id && a.attachment_url) {
                email.html = email.html.replace(`src="cid:${a.attachment_id}"`, `src="${a.attachment_url}"`)
              }
            })
          }
          dispatch({ type: GET_EMAIL_SUCCESS, payload: email, folder, mailboxId })
          listenerFn && listenerFn({ email, ID: mailId, folder, refListener })
        } else {
          dispatch({ type: GET_EMAIL_FAILURE, ID: mailId, folder })
        }
      },
      error => {
        console.error('getEmail error', error)
        dispatch({ type: GET_EMAIL_FAILURE, ID: mailId, folder })
      }
    )
    return res(refListener)
  })
}

/**
 * Redux Action To Get Inbox
 */
export const getFolderEmails = (mailboxId, folder, listenerFn, queries, limit, lastPos, orderArgs) => (dispatch, getState) => {
  queries = queries || []

  if (!mailboxId) {
    dispatch({ type: GET_FOLDER_EMAILS_FAILURE, folder, error: 'No mailboxId informed' })
    return
  }

  dispatch({ type: GET_FOLDER_EMAILS, folder, query: (queries.length && queries) || null })

  let QueryRef = FirestoreRef.collection(collectionName).doc(mailboxId).collection(folder)
  Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

  let orderBy = ''
  let order = ''

  if (orderArgs) {
    orderBy = typeof orderArgs === 'string' ? orderArgs : orderArgs.orderBy
    order = (orderArgs && orderArgs.order) || 'asc'
  } else if (!queries.length) {
    orderBy = 'createdAt'
    order = 'desc'
  }

  if (orderBy) {
    QueryRef = QueryRef.orderBy(orderBy, order)
    !!lastPos && (QueryRef = QueryRef.startAt(lastPos))
  }

  !isNaN(limit) && limit && (QueryRef = QueryRef.limit(limit))

  return new Promise(res => {
    const refListener = QueryRef.onSnapshot(
      snapshot => {
        // console.groupCollapsed('listenFolderEmails > snapshot');
        // console.log('snapshot:',snapshot.docs);
        // console.groupEnd('listenFolderEmails > snapshot');

        const emails = []
        snapshot.forEach(doc => {
          let email = doc.data()
          if (Array.isArray(email.attachments)) {
            email.attachments.forEach(a => {
              if (a.attachment_id && a.attachment_url) {
                email.html = email.html.replace(`src="cid:${a.attachment_id}"`, `src="${a.attachment_url}"`)
              }
            })
          }
          emails.push(email)
        })

        dispatch({ type: GET_FOLDER_EMAILS_SUCCESS, payload: emails, folder, mailboxId })
        listenerFn && listenerFn({ emails, refListener })
      },
      error => {
        console.error('getFolderEmails error', error)
        dispatch({ type: GET_FOLDER_EMAILS_FAILURE, folder })
      }
    )
    return res(refListener)
  })
}

/**
 * Redux Action To Select Mailbox
 */
export const onSelectMailbox = mailboxData => ({
  type: ON_SELECT_MAILBOX,
  payload: mailboxData,
})

/**
 * Redux Action To Select Folder
 */
export const onSelectFolder = (folder, page) => ({
  type: ON_SELECT_FOLDER,
  folder,
  page: page || 1,
})

/**
 * Redux Action To Select Page
 */
export const selectPage = page => ({
  type: ON_CHANGE_PAGE,
  page,
})

/**
 * Redux Action To Send Draft
 */
export const sendDraft = (mailboxId, data) => (dispatch, getState) => {
  let { mailId } = data
  return moveEmailToFolder(mailboxId, 'draft', 'sent', mailId)(dispatch)
}

/**
 * Redux Action To Move The Emails Into The Selected Folder
 */
export const moveEmailToFolder = (mailboxId, fromFolder, toFolder, mailId) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    if (!mailId) {
      return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: move 01` })
    }

    dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER, fromFolder, toFolder, mailboxId })

    FirestoreRef.collection(collectionName)
      .doc(mailboxId)
      .collection(fromFolder)
      .doc(mailId)
      .get()
      .then(doc => {
        let email = doc.data()
        let docRef = doc.ref

        FirestoreRef.collection(collectionName)
          .doc(mailboxId)
          .collection(toFolder)
          .doc(mailId)
          .set(email)
          .then(doc => {
            docRef.delete()
            dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER_SUCCESS, fromFolder, toFolder, mailboxId, mailId })
            return resolve(email)
          })
          .catch(error => {
            dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER_FAILURE, fromFolder, toFolder, mailboxId, mailId, error })
            reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: move 02` })
          })
      })
      .catch(error => {
        dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER_FAILURE, fromFolder, toFolder, mailboxId, mailId, error })
        reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: move 03` })
      })
  })
}

/**
 * Redux Action On All Email Select
 */
export const selectAllEMails = () => ({
  type: SELECT_ALL_EMAILS,
})

/**
 * Redux Action To Un Select Unselected Emails
 */
export const unselectAllEMails = () => ({
  type: UNSELECT_ALL_EMAILS,
})

/**
 * Redux Action To Send Email
 */
export const sendEmail = (mailboxId, data) => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  // owner
  const currentOwnerID = sessionJSON.get('currentOwnerID', false)
  const ownerId = currentOwnerID || user.owner || (user.level <= OWNER_LEVEL && user.ID) || ''
  const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
  const accountId = data.accountId || currentAccountID || user.accountId
  const owner = data.owner || ownerId
  // end owner

  return new Promise((resolve, reject) => {
    let rejected = false
    let requiredFields = ['fromName', 'subject', 'html', 'to']

    requiredFields.forEach(field => {
      if (!rejected && !data[field]) {
        rejected = true
        reject({
          error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages[`email.fields.${field}`]),
        })
      }
    })

    if (rejected) return

    dispatch({ type: ON_SEND_EMAIL })

    let newEmail = {
      ...emailSettingsModel,
      ...data,
      owner,
      accountId,
      authorId: user.ID,
      date: moment().format(MOMENT_ISO),
      scheduled_date: moment().format(MOMENT_ISO),
    }

    console.log({ mailboxId, data, newEmail })

    FirestoreRef.collection(collectionName)
      .doc(mailboxId)
      .collection('sent')
      .add(newEmail)
      .then(doc => {
        let responseData = {
          ...newEmail,
          ID: doc.id,
          mailId: doc.id,
        }
        dispatch({ type: ON_SEND_EMAIL_SUCCESS })

        FirestoreRef.collection(emailsCollection)
          .doc(doc.id)
          .onSnapshot(emailDoc => {
            let emailData = emailDoc.data()
            // console.log('emailData',emailData);
            if (emailData && emailData.sent) {
              dispatch({ type: EMAIL_SENT_SUCCESSFULLY })
            }
          })

        return resolve({ ...responseData })
      })
      .catch(error => {
        dispatch({ type: ON_SEND_EMAIL_FAILURE, error })
        reject({ error })
      })
  })
}

/**
 * Redux Action To Save Email
 */
export const onSaveEmail = (mailboxId, folder, data) => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  // owner
  const currentOwnerID = sessionJSON.get('currentOwnerID', false)
  const ownerId = currentOwnerID || user.owner || (user.level <= OWNER_LEVEL && user.ID) || ''
  const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
  const accountId = data.accountId || currentAccountID || user.accountId
  const owner = data.owner || ownerId
  // end owner

  return new Promise((resolve, reject) => {
    dispatch({ type: ON_SAVE_EMAIL })

    let newEmail = {
      ...emailSettingsModel,
      ...data,
      accountId,
      owner,
      authorId: user.ID,
      date: moment().format(MOMENT_ISO),
    }

    FirestoreRef.collection(collectionName)
      .doc(mailboxId)
      .collection(folder)
      .add(newEmail)
      .then(doc => {
        let responseData = {
          ...newEmail,
          id: doc.id,
          ID: doc.id,
          mailId: doc.id,
        }
        dispatch({ type: ON_SAVE_EMAIL_SUCCESS, payload: responseData, folder, mailboxId })
        return resolve({ ...responseData })
      })
      .catch(error => {
        console.error('ON_SAVE_EMAIL_FAILURE', error)
        dispatch({ type: ON_SAVE_EMAIL_FAILURE, error })
        reject({ error })
      })
  })
}

/**
 * Redux Action On Email Sent Successfully
 */
export const emailSentSuccessfully = () => ({
  type: EMAIL_SENT_SUCCESSFULLY,
})

/**
 * Redux Action To Filter Emails With Labels
 */
export const filterEmails = label => ({
  type: FILTER_EMAILS_WITH_LABELS,
  payload: label,
})

/**
 * Redux Action To Reset Filters
 */
export const resetFilters = label => ({
  type: RESET_EMAILS_FILTERS,
})

/**
 * Redux Action To Reset Emails In Folder
 */
export const resetFolderEmails = label => ({
  type: RESET_FOLDER_EMAILS,
})

/**
 * Redux Action To Add Labels Into Email
 */
export const addLabelsIntoEmail = label => ({
  type: ADD_LABELS_INTO_EMAILS,
  payload: label,
})
