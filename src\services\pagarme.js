import axios from "axios";

export class PagarmeService {
  apiKey = ''
  baseURL = ''
  constructor() {
    this.apiKey = process.env.PAGARME_API_KEY
    this.baseURL = process.env.PAGARME_API_URL
  }

  getSubscriptions = async ({ start, end, page = 1, count = 100 }) => {
    const params = {
      api_key: this.apiKey,
    };

    if (start && end) {
      params.current_period_start = [`>=${start}`, `<=${end}`]
    }

    try {
      const response = await axios({
        method: 'get',
        url: `${this.baseURL}/subscriptions`,
        params,
        data: {
          count,
          page
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      return [];
    }
  }
}
