import { Loading } from 'Components/Widgets/components/Loading'
import { GridList } from 'Components/index'
import { SHOTX_SNIPERS_COLLECTION_NAME } from 'Constants/AppCollections'
import IntlMessages from 'Util/IntlMessages'
import React, { useContext, useEffect, useState } from 'react'
import { useSelector } from "react-redux"
import { withRouter } from 'react-router-dom'
import { SniperService } from '../../../services/sniperService'
import { SnipersCard } from '../components/SnipersCard'
import { ModuleContext } from '../contexts/ModuleContext'

const collectionName = SHOTX_SNIPERS_COLLECTION_NAME

const Snipers = ({ match, history }) => {
    const {
        attribute,
        setAttribute,
        setOnAddClick
    } = useContext(ModuleContext)

    const { filterText } = attribute

    const { account } = useSelector(state => state.authReducer)
    const service = new SniperService(account.id)
    const [snipers, setSnipers] = useState([])
    const [listenners, setListeners] = useState({})
    const [loading, setLoading] = useState(false)

    // Getters and Setters

    const items = (filterText ? snipers.filter(sniper => sniper.publicSniper.toLowerCase().includes(filterText.toLowerCase())) : snipers) || []
    const title = <IntlMessages id={"shotx.snipers.title"} />
    const badgeItems = items.length ? items.length : null




    // const Employee = (props) => {
    //     const [textInfo, setTextInfo] = useState("");

    //     useEffect(() => {
    //         // Aqui recebemos a informação.
    //         // Acessamos com o mesmo nome que
    //         // setamos no componente anterior
    //         setTextInfo(props.location.state.text);
    //     }, []);

    useEffect(() => {
        setAttribute({ ...attribute, badgeItems })
    }, [filterText])

    useEffect(() => {

        getItems()
        setOnAddClick(() => () => addPostAction())

        return () => {
            if (listenners.items) {
                listenners.items()
            }
        }
    }, [])

    const validUrl = () => {
        const lastIndex = match.url.length - 1
        if (match.url[lastIndex] === '/') {
            return match.url
        } else {
            return `${match.url}/`
        }
    }

    const addPostLink = `${validUrl()}snipers/add`
    const addPostAction = () => {
        history.push(addPostLink)
    }

    const getItems = () => {
        const onSuccess = (snipers) => {
            setSnipers(snipers)
            setLoading(false)

            const badgeItems = snipers.length ? snipers.length : null
            setAttribute({
                title,
                badgeItems,
                filterText: ''
            })
        }

        const onError = (error) => {
            console.error(error)
        }

        setLoading(true)
        const sniperListen = service.listen(onSuccess, onError)
        setListeners({ ...listenners, sniperListen })

    }

    const onEditClick = (item) => {
        history.push(`${validUrl()}snipers/${item.id}`)
    }

    const onDeleteClick = (item) => {
        setLoading(true)
        service.delete(item.id)
            .then(() => {
                setLoading(false)
            })
            .catch((error) => {
                console.error(error)
            })
    }

    return (
        <div className="projects-wrapper">

            <Loading loading={loading}>
                <GridList
                    items={items}
                    collectionName={collectionName}
                    build={(sniper) => {
                        return <SnipersCard
                            collection={collectionName}
                            snipers={sniper}
                            onEdit={onEditClick.bind(this, sniper)}
                            onDelete={onDeleteClick.bind(this, sniper)}
                        />
                    }}
                />
            </Loading>

        </div>
    )
}

export default withRouter(Snipers)
