import { Card, CardContent, Typography } from '@material-ui/core';
import { GoogleMap, InfoWindow, LoadScript, Marker } from '@react-google-maps/api';
import { langMessages } from 'Lang/index';
import React from 'react';

const GeographicDistribution = ({ subscriptions }) => {
  const [selectedLocation, setSelectedLocation] = React.useState(null);

  // Brazilian states coordinates (approximate centers)
  const stateCoordinates = {
    'AC': { lat: -9.0238, lng: -70.812 },
    'AL': { lat: -9.5713, lng: -36.782 },
    'AP': { lat: 0.902, lng: -52.003 },
    'AM': { lat: -3.4168, lng: -65.8561 },
    'BA': { lat: -12.9718, lng: -38.5011 },
    'CE': { lat: -3.7172, lng: -38.5433 },
    'DF': { lat: -15.7975, lng: -47.8919 },
    'ES': { lat: -20.2976, lng: -40.2958 },
    'GO': { lat: -16.6864, lng: -49.2643 },
    'MA': { lat: -2.5297, lng: -44.3028 },
    'MT': { lat: -15.601, lng: -56.0974 },
    'MS': { lat: -20.4428, lng: -54.6464 },
    'MG': { lat: -19.9167, lng: -43.9345 },
    'PA': { lat: -1.4558, lng: -48.4902 },
    'PB': { lat: -7.115, lng: -34.8631 },
    'PR': { lat: -25.4195, lng: -49.2646 },
    'PE': { lat: -8.0476, lng: -34.8770 },
    'PI': { lat: -5.0892, lng: -42.8019 },
    'RJ': { lat: -22.9068, lng: -43.1729 },
    'RN': { lat: -5.7945, lng: -35.2120 },
    'RS': { lat: -30.0346, lng: -51.2177 },
    'RO': { lat: -8.7619, lng: -63.9039 },
    'RR': { lat: 2.8235, lng: -60.6758 },
    'SC': { lat: -27.5954, lng: -48.5480 },
    'SP': { lat: -23.5505, lng: -46.6333 },
    'SE': { lat: -10.9091, lng: -37.0677 },
    'TO': { lat: -10.1753, lng: -48.2982 }
  };

  const stateDistribution = subscriptions.reduce((acc, sub) => {
    const state = sub.address?.state || 'N/A';
    acc[state] = (acc[state] || 0) + 1;
    return acc;
  }, {});

  const mapContainerStyle = {
    width: '100%',
    height: '400px'
  };

  const center = {
    lat: -15.7975,
    lng: -47.8919
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {langMessages['subscription.overview.geographicDistribution']}
        </Typography>
        <LoadScript googleMapsApiKey={process.env.GOOGLE_MAPS_API_KEY}>
          <GoogleMap
            mapContainerStyle={mapContainerStyle}
            center={center}
            zoom={4}
          >
            {Object.entries(stateDistribution).map(([state, count]) => {
              const coordinates = stateCoordinates[state];
              if (!coordinates) return null;

              return (
                <Marker
                  key={state}
                  position={coordinates}
                  label={count.toString()}
                  onClick={() => setSelectedLocation({
                    state,
                    count,
                    ...coordinates
                  })}
                />
              );
            })}

            {selectedLocation && (
              <InfoWindow
                position={selectedLocation}
                onCloseClick={() => setSelectedLocation(null)}
                children={
                  <div className='bg-light p-10'>
                    <Typography className='pb-10 bg-light' >
                      {langMessages['subscription.overview.state']}: {selectedLocation.state}
                    </Typography>
                    <Typography className='pb-10 bg-light'>
                      {langMessages['subscription.overview.count']}: {selectedLocation.count}
                    </Typography>
                  </div>
                }
              >
              </InfoWindow>
            )}
          </GoogleMap>
        </LoadScript>
      </CardContent>
    </Card>
  );
};

export default GeographicDistribution;
