export function insertTextAtCursor(textToInsert, input) {
    if (!input) {
        console.error('Input element not found')
        return
    }
    // Para navegadores modernos
    if (document.selection) {
        // Para navegadores mais antigos como o Internet Explorer
        input.focus();
        var sel = document.selection.createRange();
        sel.text = textToInsert;
    } else if (input.selectionStart || input.selectionStart === 0) {
        // Para navegadores modernos
        var startPos = input.selectionStart;
        var endPos = input.selectionEnd;
        var textBefore = input.value.substring(0, startPos);
        var textAfter = input.value.substring(
            endPos,
            input.value.length
        );

        input.value = textBefore + textToInsert + textAfter;

        // Reposiciona o cursor após o texto inserido
        input.selectionStart = startPos + textToInsert.length;
        input.selectionEnd = startPos + textToInsert.length;
    } else {
        // Fallback caso o navegador não suporte as outras opções
        input.value += textToInsert;
    }

    input.focus();

    return input.value
}
