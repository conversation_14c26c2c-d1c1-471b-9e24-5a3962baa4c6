import { Card, CardContent, Typography } from '@material-ui/core';
import { langMessages } from 'Lang/index';
import React from 'react';
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { formatCurrency } from 'Util/country/money';

const PaymentChart = ({ subscriptions = [] }) => {
  const COLORS = { paid: '#4caf50', unpaid: '#ff9800', canceled: '#f44336' }

  const dailyData = subscriptions.reduce((acc, subscription) => {
    const date = new Date(subscription.date_created).toLocaleDateString();
    const amount = subscription.plan.amount / 100;

    const existingDay = acc.find(d => d.date === date);
    if (existingDay) {
      if (subscription.status === 'paid') {
        existingDay.paid += amount;
      } else {
        existingDay.unpaid += amount;
      }
    } else {
      acc.push({
        date,
        paid: subscription.status === 'paid' ? amount : 0,
        unpaid: subscription.status === 'paid' ? 0 : amount
      });
    }

    return acc;
  }, []);

  return (
    <Card className="h-96 animate-fade-in">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {langMessages['subscription.overview.dailyRevenues']}
        </Typography>
        <ResponsiveContainer width={"100%"} height={300}>
          <BarChart
            data={dailyData.reverse()}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis tickFormatter={formatCurrency} />
            <Tooltip
              contentStyle={{ backgroundColor: 'transparent', border: 'none' }}
              itemStyle={{ backgroundColor: 'transparent', border: 'none' }}
              cursor={{ fill: 'transparent' }}
              formatter={formatCurrency}
            />
            <Bar dataKey="paid" stackId={"a"} name={langMessages['subscription.paid']} fill="#4caf50" />
            <Bar dataKey="unpaid" stackId={"a"} name={langMessages['subscription.unpaid']} fill="#ff9800" />
            <Bar dataKey="canceled" stackId={"a"} name={langMessages['subscription.canceled']} fill="#f44336" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
};

export default PaymentChart;
