/**
 * QIUsers Actions
 */
import { FirestoreRef, createUser } from 'FirebaseRef'

// lang strings
import { langMessages } from '../lang'

import {
  FIRESTORE_ERROR,
  GET_POSTS_SUCCESS,
  UPDATE_POSTS,
  GET_QIUSERS,
  GET_QIUSERS_SUCCESS,
  GET_QIUSERS_FAILURE,
  GET_QIUSERS_KEYWORDS,
  GET_QIUSER,
  GET_QIUSER_SUCCESS,
  GET_QIUSER_FAILURE,
  SAVE_NEW_QIUSER,
  SAVE_NEW_QIUSER_FAILURE,
  SAVE_NEW_QIUSER_SUCCESS,
  UPDATE_QIUSERS,
  UPDATE_QIUSER,
  SAVE_QIUSER,
  SAVE_QIUSER_SUCCESS,
  SAVE_QIUSER_FAILURE,
  DELETE_QIUSER,
  DELETE_QIUSER_SUCCESS,
  DELETE_QIUSER_FAILURE,
  TRASH_QIUSER,
  TRASH_QIUSER_SUCCESS,
  TRASH_QIUSER_FAILURE,
  GET_QIUSER_ID,
  GET_QIUSER_ID_SUCCESS,
  GET_QIUSER_ID_FAILURE,
  GET_CURRENT_QIUSER,
  GET_CURRENT_QIUSER_SUCCESS,
  GET_CURRENT_QIUSER_FAILURE,
  UPDATE_QIUSER_ID,
} from 'Actions/types'

import moment from 'moment'

import { localJSON, sessionJSON, isLoopableObject, currentUserCan, toSearchKeyword } from '../helpers/helpers'

import { DEFAULT_LOCALE, MOMENT_ISO, TRASH_STATUS, DOC_DOESNT_EXIST } from '../constants/AppConstants'

import { QIUSERS_COLLECTION_NAME } from '../constants/AppCollections'

import ERROR_TYPES from '../constants/AppErrors'

import { QIPLUS_ROLES_LEVELS, ADMIN_LEVEL, AFFILIATE_LEVEL, OWNER_LEVEL, OPERATOR_LEVEL, OPERATOR_ROLE } from '../constants/UsersRoles'

import QIUserModel from 'Routes/qiusers/model'

const collectionName = QIUSERS_COLLECTION_NAME

/**
 * Redux Action Get QIUsers
 */
export const getQIUsers = (accountId, where, params, roles, isListener) => (dispatch, getState) => {
  const {
    authReducer: { account },
  } = getState()
  accountId = accountId || account.ID

  dispatch({ type: GET_QIUSERS })

  return new Promise((resolve, reject) => {
    roles && typeof roles === 'string' && (roles = [roles])

    const queries = Array.isArray(where) ? where : []
    const queryObj = isLoopableObject(where) ? where : params || {}

    let QueryRef = FirestoreRef.collection(collectionName)
    if (accountId !== 1) QueryRef = QueryRef.where('accountId', '==', `${accountId}`)

    Array.isArray(roles) && roles.forEach(role => (QueryRef = QueryRef.where('roles', 'array-contains', role)))
    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

    if (queryObj.searchTerm) {
      const keyword = toSearchKeyword(queryObj.searchTerm)
      QueryRef = QueryRef.where('keywords', 'array-contains', `${keyword}`)

      if (!queries.length) {
        const keywords = [keyword]
        dispatch({ type: GET_QIUSERS_KEYWORDS, keywords })
      }
    } else if (Array.isArray(queryObj.keywords)) {
      const keywords = queryObj.keywords.map(w => toSearchKeyword(w))
      QueryRef = QueryRef.where('keywords', 'array-contains-any', keywords)

      if (!queries.length) {
        dispatch({ type: GET_QIUSERS_KEYWORDS, keywords })
      }
    } else {
      if (queryObj.orderBy) QueryRef = QueryRef.orderBy(queryObj.orderBy)
    }

    if (queryObj.limit) QueryRef = QueryRef.limit(queryObj.limit)

    const resolver = snapshot => {
      const posts = snapshot.docs.map(doc => doc.data())
      dispatch({ type: GET_QIUSERS_SUCCESS, payload: posts, query: { where, params, roles } })
      dispatch({ type: GET_POSTS_SUCCESS, collection: collectionName, payload: posts })
      return resolve(posts)
    }

    const onError = error => {
      console.error(error)
      dispatch({ type: GET_QIUSERS_FAILURE })
      dispatch({ type: FIRESTORE_ERROR, error })
      return reject({ message: langMessages['errors.dbErrorMsg'] })
    }

    if (isListener) {
      QueryRef.onSnapshot(resolver, onError)
    } else {
      QueryRef.get().then(resolver).catch(onError)
    }
  })
}

/**
 * Redux Action Get QIUser
 */
export const getQIUser = qiuserId => (dispatch, getState) => {
  dispatch({ type: GET_QIUSER, qiuserId })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${qiuserId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          dispatch({ type: GET_QIUSER_FAILURE, qiuserId })
          return reject({ message: langMessages[`${collectionName}.inexistsMsg`], type: DOC_DOESNT_EXIST })
        }
        dispatch({ type: GET_QIUSER_SUCCESS, qiuserId, payload: doc.data() })
        dispatch({ type: GET_POSTS_SUCCESS, collection: collectionName, payload: [doc.data()] })
        return resolve(doc.data())
      })
      .catch(error => {
        console.error(error)
        dispatch({ type: GET_QIUSER_FAILURE, qiuserId })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

/**
 * Redux Action Get Current QIUser
 */
export const getCurrentQIUser = () => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: GET_CURRENT_QIUSER })
  const userId = user.ID
  return new Promise((resolve, reject) => {
    if (!userId) {
      dispatch({ type: GET_CURRENT_QIUSER_FAILURE })
      return reject({ message: langMessages['errors.genericErrorMsg'] })
    }
    if (user.ID === userId) {
      dispatch({ type: GET_CURRENT_QIUSER_SUCCESS, payload: user })
      return resolve(user)
    }
    FirestoreRef.collection(collectionName)
      .doc(`${userId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          dispatch({ type: GET_CURRENT_QIUSER_FAILURE })
          return reject({ message: langMessages[`${collectionName}.inexistsMsg`], type: DOC_DOESNT_EXIST })
        }
        dispatch({ type: GET_CURRENT_QIUSER_SUCCESS, payload: doc.data() })
        return resolve(doc.data())
      })
      .catch(error => {
        dispatch({ type: GET_CURRENT_QIUSER_FAILURE })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

/**
 * Redux Action Get Current QIUser
 */
export const getCurrentQIUserID = () => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: GET_QIUSER_ID })
  return new Promise((resolve, reject) => {
    const CurrentQIUserID = sessionJSON.get('CurrentQIUserID', false)
    const userId = CurrentQIUserID || user.ID

    if (!userId) {
      dispatch({ type: GET_QIUSER_ID_FAILURE })
      return reject({ message: langMessages['errors.genericErrorMsg'] })
    }

    // dispatch anyway to speed up getting user
    dispatch({ type: GET_QIUSER_ID_SUCCESS, payload: userId })
    resolve(userId)

    if (userId !== user.ID && (!user.level || user.level > ADMIN_LEVEL)) {
      // Check if user exists in database
      FirestoreRef.collection(collectionName)
        .doc(`${userId}`)
        .get()
        .then(doc => {
          if (!doc.exists) {
            dispatch({ type: GET_QIUSER_ID_FAILURE })
            return reject({ message: langMessages[`${collectionName}.inexistsMsg`], type: DOC_DOESNT_EXIST })
          }
        })
        .catch(error => {
          dispatch({ type: GET_QIUSER_ID_FAILURE })
          dispatch({ type: FIRESTORE_ERROR, error })
        })
    }
  })
}

export const saveNewQIUser = data => (dispatch, getState) => {
  dispatch({ type: SAVE_NEW_QIUSER })
  return new Promise((resolve, reject) => {
    const user = localJSON.get('user', false)

    if (!currentUserCan('add', collectionName, user)) {
      let error = langMessages['errors.permissionDenied']
      dispatch({ type: SAVE_NEW_QIUSER_FAILURE, error })
      return reject({ message: error })
    }

    if (!(data.roles || []).length) {
      let error = langMessages['alerts.emptyRoles']
      dispatch({ type: SAVE_NEW_QIUSER_FAILURE, error })
      return reject({ message: error })
    }

    const newQIUser = sanitizeQIUser(data, 'add')

    if (!newQIUser.accountId || !newQIUser.owner) {
      let error = langMessages['errors.genericErrorMsg']
      dispatch({ type: SAVE_NEW_QIUSER_FAILURE, error })
      return reject({ message: error })
    }

    if (!newQIUser.email) {
      let error = langMessages['alerts.emptyEmailAddress']
      dispatch({ type: SAVE_NEW_QIUSER_FAILURE, error })
      return reject({ message: error })
    }

    return createUser(newQIUser)
      .then(response => {
        // console.log('response',response);
        let result = response.data
        if (result.error) {
          let errorMsg = result.errorMsg || result.message || langMessages['errors.genericErrorMsg']
          switch (result.error) {
            case ERROR_TYPES.MODULE_NOT_AVAILABLE:
              errorMsg = langMessages['errors.MODULE_NOT_AVAILABLE']
              break
            case ERROR_TYPES.MODULE_LIMIT_REACHED:
              errorMsg = langMessages['placeholders.MODULE_LIMIT_REACHED'].replace(
                '[%s]',
                langMessages[`modules.${collectionName}`] || collectionName
              )
              break
            case ERROR_TYPES.EMAIL_EXISTS_ERROR:
              errorMsg = langMessages['alerts.emailAlreadyExists']
              break
            case ERROR_TYPES.NEW_AUTH_USER_ERROR:
              if (result.code) {
                switch (result.code) {
                  case 'auth/invalid-phone-number':
                    errorMsg = langMessages['alerts.informFullPhoneNumber']
                    break
                  case 'auth/phone-number-already-exists':
                    errorMsg = langMessages['alerts.phoneAlreadyExists']
                    break
                  case 'auth/weak-password':
                    errorMsg = langMessages['alerts.weakPassword']
                    break
                  case 'auth/wrong-password':
                    errorMsg = langMessages['alerts.wrongPassword']
                    break
                  case 'auth/invalid-password':
                    errorMsg = langMessages['alerts.invalidPassword']
                    break
                  case 'auth/invalid-email':
                    errorMsg = langMessages['errors.invalidEmailAddress']
                    break
                  case 'auth/email-already-in-use':
                    errorMsg = langMessages['alerts.emailAlreadyExists']
                    break
                  default:
                    break
                }
              }
              break
            case ERROR_TYPES.MISSING_REQUIRED_FIELDS:
            case ERROR_TYPES.NO_ACCOUNT_FOUND:
              errorMsg = langMessages['errors.genericErrorMsg']
              break
            default:
              break
          }
          dispatch({ type: SAVE_NEW_QIUSER_FAILURE, error: result.error })
          return reject({ message: errorMsg })
        } else if (result.collection === collectionName && result.ID) {
          const payload = result
          dispatch({ type: SAVE_NEW_QIUSER_SUCCESS, payload })
          return resolve(payload)
        }
      })
      .catch(function (error) {
        console.error(SAVE_NEW_QIUSER_FAILURE, error)
        dispatch({ type: SAVE_NEW_QIUSER_FAILURE, error: error.message })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const saveQIUser = data => (dispatch, getState) => {
  const { ID } = data
  dispatch({ type: SAVE_QIUSER })
  return new Promise((resolve, reject) => {
    if (!ID) {
      dispatch({ type: SAVE_QIUSER_FAILURE })
      return reject({ message: langMessages['errors.dbErrorMsg'] })
    }

    if (!data.email) {
      let error = langMessages['alerts.emptyEmailAddress']
      dispatch({ type: SAVE_NEW_QIUSER_FAILURE, error })
      return reject({ message: error })
    }

    const qiuser = sanitizeQIUser(data, 'update')
    // console.log('saveQIUser > qiuser',qiuser);

    FirestoreRef.collection(collectionName)
      .doc(ID)
      .update(qiuser)
      .then(() => {
        const payload = qiuser
        dispatch({ type: SAVE_QIUSER_SUCCESS, payload })
        return resolve(payload)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: SAVE_QIUSER_FAILURE })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const onTrashQIUser = qiuserId => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  if (user.ID === qiuserId) {
    return false
  }
  dispatch({ type: TRASH_QIUSER })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${qiuserId}`)
      .get()
      .then(doc => {
        if (doc.exists) {
          let qiuser = doc.data()
          if (user.level < qiuser.level) {
            return doc.ref.update({ status: TRASH_STATUS }).then(() => {
              dispatch({ type: TRASH_QIUSER_SUCCESS, ID: qiuserId })
              return resolve(qiuserId)
            })
          }
        }
        dispatch({ type: TRASH_QIUSER_FAILURE, ID: qiuserId })
        return reject({ message: langMessages['errors.genericErrorMsg'] })
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: TRASH_QIUSER_FAILURE, ID: qiuserId })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const deleteQIUser = qiuserId => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  if (user.ID === qiuserId) {
    return false
  }
  dispatch({ type: DELETE_QIUSER })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${qiuserId}`)
      .get()
      .then(doc => {
        if (doc.exists) {
          let qiuser = doc.data()
          if (user.level < qiuser.level) {
            return doc.ref.delete().then(() => {
              dispatch({ type: DELETE_QIUSER_SUCCESS, ID: qiuserId })
              return resolve(qiuserId)
            })
          }
        }
        dispatch({ type: DELETE_QIUSER_FAILURE, ID: qiuserId })
        return reject({ message: langMessages['errors.genericErrorMsg'] })
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: DELETE_QIUSER_FAILURE, ID: qiuserId })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const onUpdateQIUser = (data, ID) => ({
  type: UPDATE_QIUSER,
  payload: data,
})

export const onUpdateQIUsers = (data, query) => (dispatch, getState) => {
  dispatch({ type: UPDATE_QIUSERS, payload: data, query })
  dispatch({ type: UPDATE_POSTS, collection: collectionName, payload: data })
}

export const onUpdateQIUserID = data => {
  sessionJSON.set('CurrentQIUserID', data)
  return {
    type: UPDATE_QIUSER_ID,
    payload: data,
  }
}

export const sanitizeQIUser = (data, prepare) => {
  let { roles } = data

  roles = (Array.isArray(roles) && roles.length && roles) || [OPERATOR_ROLE]

  const user = localJSON.get('user', false)

  // owner
  const currentOwnerID = sessionJSON.get('currentOwnerID', false)
  const ownerId = currentOwnerID || user.owner || (user.level <= OWNER_LEVEL && user.ID) || ''
  const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
  const accountId = data.accountId || currentAccountID || user.accountId
  // end owner

  const seller = data.seller || ''
  const manager = data.manager || ''
  const owner = data.owner || ownerId

  let dbData = {
    ...QIUserModel,
    ...window.jsonClone(data),
    collection: collectionName,
    locale: data.locale || DEFAULT_LOCALE,
    roles,
    seller,
    manager,
    owner,
    accountId,
  }

  if (!data.affiliateId && user.level === AFFILIATE_LEVEL) {
    dbData.affiliateId = user.ID
  }

  switch (prepare) {
    case 'add':
      dbData = {
        ...dbData,
        date: moment().format(MOMENT_ISO),
        modified: moment().format(MOMENT_ISO),
        author: user.ID,
        logs: {
          ...(data.logs || {}),
          added: {
            user: user.ID,
            operator_id: user.ID,
            date: moment().format(MOMENT_ISO),
          },
        },
      }

      break

    case 'update':
    default:
      dbData = {
        ...dbData,
        modified: moment().format(MOMENT_ISO),
        logs: {
          ...(data.logs || {}),
          updated: {
            user: user.ID,
            operator_id: user.ID,
            date: moment().format(MOMENT_ISO),
          },
        },
      }

      break
  }

  return dbData
}

export const createQIUser = userdata => {
  let { roles } = userdata

  roles = (Array.isArray(roles) && roles.length && roles) || [OPERATOR_ROLE]

  // owner
  const currentOwnerID = sessionJSON.get('currentOwnerID', false)
  const owner = userdata.owner || currentOwnerID || ''
  const author = userdata.author || userdata.owner
  // end owner

  let dbData = {
    ...QIUserModel,
    ...userdata,
    collection: collectionName,
    locale: userdata.locale || DEFAULT_LOCALE,
    owner,
    date: moment().format(MOMENT_ISO),
    modified: moment().format(MOMENT_ISO),
    author,
    roles,
    logs: {
      added: {
        user: author,
        operator_id: author,
        date: moment().format(MOMENT_ISO),
      },
    },
  }

  return dbData
}

export const createQIUserFromAuth = (authUser, formData) => {
  let { uid, email, photoURL, phoneNumber } = authUser
  let { roles } = formData

  roles = (Array.isArray(roles) && roles.length && roles) || [OPERATOR_ROLE]
  email = email || formData.email

  let displayName = formData.displayName || authUser.displayName || (email || '').split('@')[0] || ''
  let firstName = formData.firstName || authUser.firstName || authUser.name || displayName

  let newUser = createQIUser(QIUserModel)

  let level = OPERATOR_LEVEL

  Object.keys(QIPLUS_ROLES_LEVELS).forEach((role, i) => {
    if (roles.includes(role)) {
      const l = QIPLUS_ROLES_LEVELS[role]
      if (l < level) {
        level = l
      }
    }
  })

  if (phoneNumber) newUser.mobile = phoneNumber
  if (photoURL) newUser.avatar = photoURL

  let qiuser = {
    ...newUser,
    ...authUser,
    ...formData,
    uid,
    ID: uid,
    id: uid,
    author: uid,
    owner: uid,
    email,
    firstName,
    displayName,
    roles,
    level,
  }

  Object.keys(qiuser).forEach(key => {
    if (!(key in QIUserModel)) {
      delete qiuser[key]
    }
  })

  return new Promise((res, rej) => {
    FirestoreRef.collection(QIUSERS_COLLECTION_NAME)
      .doc(uid)
      .set(qiuser)
      .then(result => {
        return res(qiuser)
      })
      .catch(err => {
        console.error(err)
        return rej(err)
      })
  })
}
