import styled from 'styled-components'

export const ChatItem = styled.div`
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  width: 100%;
`
export const Message = styled.div`
  max-width: 80%;
  color: #111;
  padding: 8px;
  font-size: 0.9rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 15px;
  position: relative;
  border-radius: 5px;
  background: ${({ theme }) => theme.messageReceived} !important;
  box-shadow: 0 0 0 0 #888;

  ${({ sender, theme }) =>
    sender === 'true' &&
    `
    margin-left: auto !important;
    margin-right: 20px !important;
    background: ${theme.messageSent} !important;

    &:after {
      content: "";
      position: absolute;
      bottom: 0;
      width: 15px;
      height: 15px;
      clip-path: polygon(100% 50%, 0 0, 0 100%);
      right: -10px;
      background: ${theme.messageSent} !important;
    }
  `}

  ${({ receiver, theme }) =>
    receiver === 'true' &&
    `
    &:before {
      content: "";
      position: absolute;
      bottom: 0;
      width: 15px;
      height: 15px;
      clip-path: polygon(100% 0, 0 50%, 100% 100%);
      left: -10px;
      background: ${theme.messageReceived} !important;
    }
  `}
`
