import { FirebaseRepository } from "FirebaseRef/repository"
import moment from "moment"

export class InteractionService {
    constructor(accountId, instanceId) {
        this.accountId = accountId
        this.instanceId = instanceId
        this.repository = new FirebaseRepository()
        this.path = `shotx/${accountId}/instances/${instanceId}/contacts`
        this.orderBy = 'created_at'
        this.order = "desc"
    }

    getFirstPage = (onSuccess, onError, pageLimit) => {
        return this.repository.listenCollectionPaginated({
            path: this.path,
            callback: onSuccess,
            onError: onError,
            limit: pageLimit,
            orderBy: this.orderBy,
            order: this.order,
        })
    }

    getNextPage = (onSuccess, onError, pageLimit, lastDocFromLastPage) => {
        return this.repository.listenCollectionPaginated({
            path: this.path,
            callback: onSuccess,
            onError: onError,
            start: lastDocFromLastPage[this.orderBy],
            limit: pageLimit,
            orderBy: this.orderBy,
            order: this.order,
        })
    }

    getPreviousPage = (onSuccess, onError, pageLimit, lastDocFromCurrentPage) => {
        return this.repository.listenCollectionPaginated({
            path: this.path,
            callback: onSuccess,
            onError: onError,
            end: lastDocFromCurrentPage[this.orderBy],
            limit: pageLimit,
            orderBy: this.orderBy,
            order: this.order,
        })
    }

    getLeads = () => {
        const path = `leads`
        const where = [
            [
                'ID',
                '!=',
                ''
            ],
            [
                'accountId',
                '==',
                this.accountId
            ],
            [
                'status',
                '==',
                'publish'
            ]
        ]
        return this.repository.getDocs(path, where)
    }

    getLeadsFromIds = async (leadIds) => {
        const path = `leads`
        const where = [
            [
                'ID',
                'in',
                leadIds
            ],
            [
                'accountId',
                '==',
                this.accountId
            ],
            [
                'status',
                '==',
                'publish'
            ]
        ]

        return await this.repository.getDocs(path, where)
    }

    getTotalPages = (onSuccess, onError) => {
        return this.repository.count(this.path)
    }

    vinculateLeadOnIg_Sid = async (lead_id, ig_sid) => {
        if (!lead_id | !ig_sid) {
            console.error('leadId and ig_sid is required')
            return
        }

        return this.repository.setDoc(`${this.path}/${ig_sid}`, { lead_id })
    }

    // IMPORTANT: As alterações aqui refletem diretamente na função de atualizar ou criar lead
    // no final do fluxo do sniper, o ig_sids é usado para encontrar o lead que foi vinculado
    vinculateIg_SidOnLead = async (lead, contact, instanceId) => {
        const ig_contacts = lead.ig_contacts || []

        const { ig_sid, username, ig_sid_owner = null } = contact
        ig_contacts.push({
            ig_sid: ig_sid,
            ig_username: username,
            instanceId,
            vinculated_at: moment().unix(),
            ig_sid_owner,
        })
        const ig_sids = ig_contacts.map(c => `${c.ig_sid}-${instanceId}`)
        await this.repository.setDoc(`leads/${lead.id}`, { ig_contacts, ig_sids })

        return {
            ...lead,
            ig_contacts,
            ig_sids
        }
    }

    unvinculateLeadOnIg_Sid = async (ig_sid) => {
        if (!ig_sid) {
            console.error('ig_sid is required')
            return
        }

        return this.repository.setDoc(`${this.path}/${ig_sid}`, { lead_id: null })
    }

    unvinculateIg_SidOnLead = async (lead, contact) => {
        if (!lead) {
            console.error('lead is required')
            return
        }

        let ig_contacts = lead?.ig_contacts || []
        if (ig_contacts.length === 0) {
            console.warn('ig_contacts is empty')
            return
        }
        ig_contacts = ig_contacts.filter(c => c.ig_sid !== contact.ig_sid)
        const ig_sids = ig_contacts.map(c => `${c.ig_sid}-${instanceId}`)
        await this.repository.setDoc(`leads/${lead.ID || lead.id}`, { ig_contacts, ig_sids })

        return {
            ...lead,
            ig_contacts,
            ig_sids
        }
    }

    delete = async (id) => {
        const deleted = this.repository.deleteDoc(`${this.path}/${id}`)
        console.log('deleted', deleted)
        return deleted
    }

    sanitizeUndefined = (obj) => {
        Object.keys(obj).forEach(key => obj[key] === undefined && delete obj[key])
        return obj
    }

    getInstance = async () => {
        const path = `shotx/${this.accountId}/instances/${this.instanceId}`
        return await this.repository.getDoc(path)
    }
}
