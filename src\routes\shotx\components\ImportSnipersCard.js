import { Alert } from '@material-ui/lab'
import { GridCard, GridCardRow } from 'Components/index'
import { langMessages } from 'Lang/index'
import React from 'react'
import { Badge } from 'reactstrap'

export const ImportSnipersCard = ({ snipersKeys, collection, onImportClick }) => {
    const { description, token, workspaces } = snipersKeys
    const renderFlow = (workspace) => {
        if (workspace.snipers.length === 0) {
            const retorno = []
            retorno.push(
                <div className="projects-wrapper">
                    <Alert className="mb-15" severity="warning" >
                        {langMessages['shotx.snipers.noSniper']}
                    </Alert>
                </div>
            )
            return retorno
        } else {
            return workspace.snipers.map(sniper => {
                const hasPublishId = sniper?.publishedSniperId ? true : false
                return (

                    <GridCard
                        id={sniper.id}
                        title={sniper.name} // vai ser o public id
                        contents={[
                            <GridCardRow
                                childs={[
                                    {
                                        title: langMessages['texts.status'],
                                        subtitle: (
                                            <Badge color={hasPublishId ? 'success' : 'danger'}>
                                                {hasPublishId ? langMessages[`statuses.publish`] : langMessages[`statuses.draft`]}
                                            </Badge>
                                        ),
                                    }
                                ]}
                            />,

                        ]}
                        footer={{
                            title: "footer",
                            collection,
                            actions: [
                                {
                                    name: langMessages['button.import'],
                                    // icon: 'fa fa-pencil', // optional
                                    onClick: () => onImportClick({ sniper, token }),
                                    hidden: !hasPublishId
                                }
                            ],
                        }}
                    />
                )
            })
        }
    }

    const works = workspaces.map((workspace) => {
        return (

            <GridCard
                id={workspace.id}
                title={workspace.name}
                full={true}
                isRow={true}
                contents={
                    renderFlow(workspace)
                }
            />
        )
    })

    return <GridCard
        id={token}
        title={description}
        full={true}
        contents={
            works
        }
    />
}
