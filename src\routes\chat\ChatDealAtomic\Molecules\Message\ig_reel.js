import { langMessages } from "Lang/index";
import React from "react";
import { TextMessage } from "./text";
import { VideoFrame } from './video.style';

const ReelMessage = ({ message, channel }) => {
  const { ig_reel } = message

  if (ig_reel?.mediaUrl) {
    return (
      <div className="bg-transparent">
        <VideoFrame height="400px">
          <video src={ig_reel.mediaUrl} controls className='w-100 h-100' />
        </VideoFrame>
        {ig_reel?.caption && <TextMessage text={ig_reel.caption} />}
      </div>
    )
  }

  return (
    <p>{langMessages['shotx.chat.videoNotAvailable']}</p>
  )
}

export default ReelMessage;
