import { Card, CardContent, Typography } from '@material-ui/core';
import { langMessages } from 'Lang/index';
import React from 'react';
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { formatCurrency } from 'Util/country/money';

const SubscriptionChart = ({ subscriptions = [] }) => {

  const monthlyData = subscriptions.reduce((acc, sub) => {
    const date = new Date(sub.date_created);
    const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;
    const amount = sub.plan.amount / 100;

    if (!acc[monthYear]) {
      acc[monthYear] = { month: monthYear, paid: 0, unpaid: 0, canceled: 0 };
    }

    acc[monthYear][sub.status] += amount;

    return acc;
  }, {});

  const chartData = Object.values(monthlyData).reverse();

  return (
    <Card className="h-96">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {langMessages['subscription.overview.subscriptionsEvolution']}
        </Typography>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <YAxis tickFormatter={formatCurrency} />
            <XAxis dataKey="month" />
            <Tooltip
              contentStyle={{ backgroundColor: 'transparent', border: 'none' }}
              itemStyle={{ backgroundColor: 'transparent', border: 'none' }}
              cursor={{ fill: 'transparent' }}
              formatter={formatCurrency}
            />
            <Legend />
            <Bar dataKey="paid" stackId={"a"} name={langMessages['subscription.paid']} fill="#4caf50" />
            <Bar dataKey="unpaid" stackId={"a"} name={langMessages['subscription.unpaid']} fill="#ff9800" />
            <Bar dataKey="canceled" stackId={"a"} name={langMessages['subscription.canceled']} fill="#f44336" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default SubscriptionChart;
