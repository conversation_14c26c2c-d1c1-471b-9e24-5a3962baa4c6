import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog, <PERSON>alogA<PERSON>, Dialog<PERSON>ontent, DialogTitle, TextField } from '@material-ui/core'
import { Autocomplete } from '@material-ui/lab'
import { ListBuilder } from 'Components/ListCard/ListBuilder'
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'
import { Loading } from 'Components/Widgets/components/Loading'
import { PageTitleBar } from 'Components/index'
import { LEADS_COLLECTION_NAME, SHOTX_INTERACTIONS_COLLECTION_NAME } from 'Constants/AppCollections'
import { langMessages } from 'Lang/index'
import SearchBar from 'Routes/components/SearchBar'
import React, { useEffect, useState } from 'react'
import { Helmet } from 'react-helmet'
import { useSelector } from "react-redux"
import { useParams, withRouter } from 'react-router-dom'
import AppModules from '../../../constants/AppModules'
import { InteractionService } from '../../../services/interactionService'
import { ShotXInteractionCard } from '../components/InteractionCard'

const collectionName = SHOTX_INTERACTIONS_COLLECTION_NAME

const Interactions = ({ match, history }) => {
    let { id: InstanceId } = useParams();
    const { account } = useSelector(state => state.authReducer)
    const service = new InteractionService(account.id, InstanceId)

    const [instance, setInstance] = useState(null)
    const [openDialog, setOpenDialog] = useState(false)
    const [loading, setLoading] = useState(true)
    const [listenners, setListeners] = useState({})
    const [filterText, setFilterText] = useState('')

    const [leads, setLeads] = useState([])
    const [vinculatedLeads, setVinculatedLeads] = useState([])
    const [interactions, setInteractions] = useState({ 1: [] })
    const [interactionsSelected, setInteractionsSelected] = useState([])

    // Pagination states
    const [totalItems, setTotalItems] = useState(0)
    const [currentPage, setCurrentPage] = useState(1)
    const [pageLimit, setPageLimit] = useState(10)
    const [goToAction, setGoToAction] = useState(null)

    const [selectedContact, setSelectedContact] = useState(null)
    const [selectedLead, setSelectedLead] = useState(null)

    // Getters and Setters
    const isFiltered = !!filterText
    const items = (filterText ? interactions[currentPage].filter(item => `${item.name} ${item.username}`.toLowerCase().includes(filterText.toLowerCase())) : interactions[currentPage]) || []

    const startLoading = () => setLoading(true)
    const stopLoading = () => setLoading(false)

    useEffect(() => {
        startLoading()
        getItems()
    }, [currentPage])

    // Get Leads
    useEffect(() => {
        service.getInstance().then(setInstance)
        getTotalPages()
        getLeads()
        return () => {
            for (const key in listenners) {
                listenners[key]()
            }
        }
    }, [])

    const getTotalPages = () => {
        service
            .getTotalPages()
            .then((data) => {
                setTotalItems(data)
            })
            .catch((err) => {
                console.log(err)
            })
            .finally(() => loading && setLoading(false))
    }

    const getItems = async () => {
        startLoading()

        const onSuccess = async (contacts) => {
            const leadsIds = contacts.filter(item => !!item.lead_id).map((item) => item.lead_id)
            const leadsFromContacts = leadsIds.length ? await service.getLeadsFromIds(leadsIds) : []
            setVinculatedLeads(leadsFromContacts)


            // const sanitizedItems = contacts.map((item) => {
            //     const { ig_sid, username, event_type, lead_id, created_at } = item

            //     const lead = leadsFromContacts.find(l => l.id === lead_id)
            //     const leadName = lead ? lead.displayName : 'Não vinculado'

            //     let dataReturn = {
            //         ...item,
            //         id: ig_sid,
            //         username,
            //         lead_id,
            //         hasLead: !!lead_id,
            //         eventType: event_type?.join(', '),
            //         badgeTitle: lead_id ? leadName : 'Não vinculado',
            //         badgeColor: lead_id ? 'success' : 'warning',
            //         data_time: moment.unix(created_at).format('DD/MM/YY HH:mm'),
            //         showLink: !!lead_id,
            //         showUnlink: !lead_id,
            //         detailLink: lead_id ? `/${getCollectionPath(LEADS_COLLECTION_NAME)}/${lead_id}` : false,
            //         onVinculateClick: (e) => handleLink(e)
            //     }
            //     return dataReturn
            // })

            setInteractions({ ...interactions, [currentPage]: contacts })
            stopLoading()
        }

        const onError = (error) => {
            console.error('getItems onError', error)
            setLoading(false)
        }


        if (interactions[currentPage] && interactions[currentPage].length > 0) {
            stopLoading()
            return
        }

        let unsubscribe
        switch (goToAction) {
            case 'next':
                const nextPageListenner = listenners[currentPage]
                if (nextPageListenner && typeof nextPageListenner === 'function') {
                    nextPageListenner()
                }
                const lastDocFromLastPage = interactions[currentPage - 1][interactions[currentPage - 1].length - 1]
                unsubscribe = service.getNextPage(onSuccess, onError, pageLimit, lastDocFromLastPage)
                break
            case 'prev':
                const prevPageListenner = listenners[currentPage]
                if (prevPageListenner && typeof prevPageListenner === 'function') {
                    // console.log(`Already listenning ${currentPage}`)
                    // stopLoading()
                    // return
                    prevPageListenner()
                }
                const lastDocFromCurrentPage = interactions[currentPage + 1][0]
                unsubscribe = service.getPreviousPage(onSuccess, onError, pageLimit, lastDocFromCurrentPage)
                break
            default:
                const firstPageListenner = listenners[currentPage]
                if (firstPageListenner && typeof firstPageListenner === 'function') {
                    // console.log(`Already listenning ${currentPage}`)
                    // stopLoading()
                    // return
                    firstPageListenner()
                }
                unsubscribe = service.getFirstPage(onSuccess, onError, pageLimit)
        }
        // const items = service.listen(onSuccess, onError, pageLimit, currentPage)
        setListeners({ ...listenners, [currentPage]: unsubscribe })
    }

    const getLeads = () => {
        if (leads.length > 0) return

        const onSuccess = (items) => {
            setLeads(items)
            stopLoading()
        }

        const onError = (error) => {
            console.error('getItems onError', error)
            stopLoading()
        }
        service.getLeads(onSuccess, onError)
            .then((leads) => onSuccess(leads))
            .catch((error) => onError(error))
    }

    const handleLink = (contact) => {
        setSelectedContact(contact)
        setOpenDialog(!openDialog)
    }

    const handleUnlink = async (contact) => {
        service.unvinculateLeadOnIg_Sid(contact.ig_sid)
        const lead = leads.find(function (lead, index, arr) {
            return (lead.id || lead.ID) === contact.lead_id
        })
        const unvinculatedLead = await service.unvinculateIg_SidOnLead(lead, contact)
        const index = leads.findIndex(l => l.id === unvinculatedLead.id)

        let updatedLeads = leads;
        updatedLeads[index] = unvinculatedLead
        setLeads(updatedLeads)
        setSelectedContact(null)
    }

    const handleSave = async () => {
        service.vinculateLeadOnIg_Sid(selectedLead.id, selectedContact.ig_sid)
        const vinculatedLead = await service.vinculateIg_SidOnLead(selectedLead, selectedContact, InstanceId)
        const index = leads.findIndex(l => l.id === vinculatedLead.id)

        let updatedLeads = leads;
        updatedLeads[index] = vinculatedLead
        setLeads(updatedLeads)
        setSelectedContact(null)
        setSelectedLead(null)
    }

    const goToPage = (pageTarget) => {
        if (pageTarget > currentPage) {
            setGoToAction('next')
        }

        if (pageTarget < currentPage) {
            setGoToAction('prev')
        }

        setCurrentPage(pageTarget)
    }

    return (
        <div className="projects-wrapper">
            <Helmet>
                <title>{langMessages['app.appName'] + ' | ' + langMessages['interactions.module.title']}</title>
                <meta name="description" content="QIPlus" />
            </Helmet>
            <PageTitleBar
                title={
                    <div>
                        <Badge badgeContent={totalItems} color="error">
                            {!instance && langMessages["shotx.instance.interactions"]}
                            {instance && langMessages["shotx.instance.interactions.title"].replace('[%instance]', instance?.title || '')}
                        </Badge>
                    </div>
                }
                match={match}
                history={history}
                rightComponents={
                    <SearchBar
                        unWrap
                        reverse
                        filterText={filterText}
                        onChangeFilter={setFilterText}
                    />
                }
            />
            <div className="main-content">
                <Loading title={langMessages['app.loading']} loading={loading}>
                    <RctCollapsibleCard fullBlock>
                        <ListBuilder
                            items={items}
                            collectionName={collectionName}
                            addPostAction={e => setOpenDialog(true)}
                            cols={[
                                {
                                    field: 'username',
                                    label: 'Usuário'
                                },
                                {
                                    field: 'event_type',
                                    label: 'Tipo de Evento'
                                },
                                {
                                    field: 'created_at',
                                    label: 'Data'
                                },
                                {
                                    field: 'lead_id',
                                    label: 'Lead Vinculado'
                                },
                                {
                                    field: 'actions',
                                    label: 'Ações'
                                }
                            ]}
                            build={(interaction) => {
                                const lead = vinculatedLeads.find(l => l.id === interaction.lead_id)
                                return <ShotXInteractionCard
                                    interaction={interaction}
                                    lead={lead}
                                    onVinculate={handleLink.bind(this, interaction)}
                                    onUnvinculate={handleUnlink.bind(this, interaction)}
                                />
                            }}
                            totalItems={totalItems}
                            currentPage={currentPage}
                            rowsPerPage={pageLimit}
                            onPageChange={goToPage}
                        // onRowsPerPageChange={setPageLimit}
                        />
                    </RctCollapsibleCard>
                </Loading>
            </div>
            {
                openDialog && (
                    <Dialog
                        open={true}
                        maxWidth="sm"
                        onClose={() => setOpenDialog(!openDialog)}
                        aria-labelledby="alert-dialog-title"
                        aria-describedby="alert-dialog-description"
                        classes={{ paper: 'w-80' }}
                    >
                        <DialogTitle id="alert-dialog-title">{langMessages['shotx.instagram.selectLead']}</DialogTitle>
                        <DialogContent>
                            <div className="py-30" style={{ minHeight: 200 }}>
                                <Autocomplete
                                    id="leads"
                                    options={leads.filter(lead => !vinculatedLeads.find(l => l.id === lead.id))}
                                    getOptionLabel={(lead) => lead.displayName}
                                    renderInput={(params) => <TextField {...params} label="Leads" variant="outlined" />}
                                    onChange={(_, value) => {
                                        console.log(value)
                                        setSelectedLead(value)
                                    }}
                                />
                            </div>
                        </DialogContent>
                        <DialogActions>
                            <div className='mx-15 d-flex flex-row w-100 mt-10 align-items-center justify-content-between'>
                                <Button
                                    onClick={e => {
                                        setOpenDialog(!openDialog)
                                        window.open(`/app/${AppModules[LEADS_COLLECTION_NAME].route}/add`)
                                    }}
                                    color="primary" className=""
                                >
                                    <i className="icon-plus mr-5"></i>
                                    {langMessages[`${LEADS_COLLECTION_NAME}.create`]}
                                </Button>

                                <Button
                                    className="btn-primary text-white"
                                    autoFocus
                                    onClick={() => {
                                        handleSave()
                                        setOpenDialog(!openDialog)
                                    }}
                                >
                                    {langMessages['button.associate']}
                                </Button>
                            </div>
                        </DialogActions>
                    </Dialog>
                )
            }

        </div>
    )
}

export default withRouter(Interactions)
