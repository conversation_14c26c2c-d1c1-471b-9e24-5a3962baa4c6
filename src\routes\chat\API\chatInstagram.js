import { CHAT_APP_ENDPOINTS } from 'Constants'
import { getCurrentUserToken } from 'FirebaseRef'
import { langMessages } from "Lang/index"
import { axiosChatService } from "./axiosChatService"

axiosChatService.interceptors.request.use(async config => {
    const token = await getCurrentUserToken()
    config.headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    }
    return config
})

export const createInstance = async ({ title, userId, platform, accountId, subscriptions }) => {
    if (!title || !userId || !platform) return
    try {
        const url = '/instance/create'
        const body = {
            title,
            userId,
            platform,
            accountId,
            subscriptions
        }
        const response = await axiosChatService.post(url, body)

        if (response.status === 201) {
            return response?.data?.data
        }

        return response
    } catch (error) {
        console.log('error:', error)
        alert(langMessages['shotx.whatsapp.request.createinstance.fail'])
    }
}
export const updateInstance = async (instance) => {
    if (!instance) return
    try {
        const url = '/instance/update'
        const response = await axiosChatService.put(url, { instance })

        if (response.status === 201) {
            return response?.data?.data
        }

        return response
    } catch (error) {
        console.log('error:', error)
        alert(langMessages['shotx.whatsapp.request.updateinstance.fail'])
    }
}
export const sendMessage = async ({ instance, ig_id, message }) => {
    if (!instance || !ig_id || !message) return
    const { platform } = instance
    try {
        const url = `${platform}/sendmessage`
        const body = {
            message,
            ig_id,
            instance
        }
        const response = await axiosChatService.post(url, body)

        return response?.data

    } catch (error) {
        console.log('error:', error)
        alert('Falha no Envio da Mensagem')
    }
}
export const sendAudioMessage = async ({ instance, phone, audio }) => {
    if (!instance || !phone || !audio) return
    const { id, accountId, userId, auth: { jwt } } = instance
    try {
        const url = `/${platform}/audio/send/${id}`
        const body = {
            accountId,
            userId,
            phone,
            audio,
        }

        const response = await axiosChatService.post(url, body)

        if (response.status === 201) {
            return response?.data?.data
        }

        return response
    } catch (error) {
        console.log('error:', error)
        alert('Tamanho maximo do arquivo excedido (3MB)')
    }
}

export const sendMediaFile = async ({ instance, ig_sid, file, type = 'audio' }) => {
    if (!instance || !ig_sid || !file) return
    const { platform } = instance
    try {
        const url = `/${platform}/media/send`
        const formData = new FormData()
        formData.append('file', file)
        formData.append('ig_sid', ig_sid)
        formData.append('type', type)
        formData.append('mimetype', type)
        formData.append('instance', JSON.stringify(instance))

        const response = await axiosChatService.post(url, formData)

        if (response.status === 201) {
            return response?.data?.data
        }

        return response
    } catch (error) {
        console.log('error:', error)
        alert('Tamanho maximo do arquivo excedido (3MB)')
    }
}
export const resendMessage = async ({ instance, phone, messageId }) => {
    if (!instance) return
    const { id, accountId, userId, auth: { jwt } } = instance
    try {
        const url = `/whatsapp/message/${messageId}/resend/${id}`
        const body = {
            accountId,
            userId,
            phone,
        }
        const response = await axiosChatService.post(url, body, { headers: { Authorization: `Bearer ${jwt}` } })
        return response
    } catch (error) {
        console.log(error)
    }
}
export const markAsRead = async (instance, readMessages) => {
    if (!instance || !readMessages || readMessages.length === 0) return
    const { id, accountId, userId, auth: { jwt } } = instance
    try {
        const url = `/whatsapp/message/read/${id}`
        const body = {
            accountId,
            userId,
            readMessages,
        }
        const response = await axiosChatService.post(url, body, { headers: { Authorization: `Bearer ${jwt}` } })
        return response?.data?.data
    } catch (error) {
        console.log(error)
    }
}

export const getMediaFile = async ({ instance, messageId }) => {
    if (!instance) return
    const { id, accountId, userId, auth: { jwt } } = instance

    const body = {
        accountId,
        userId,
        messageId,
    }

    return await fetch(`${CHAT_APP_ENDPOINTS.codechat}/chat/retrieverMediaMessage/${accountId}|${id}`, {
        method: 'POST',
        headers: {
            Authorization: `Bearer ${jwt}`,
            'Content-type': "application/json;charset=UTF-8"
        },
        body: JSON.stringify({ keyId: messageId })
    })
        .then(async (response) => {
            if (response.status == 200) {
                const blob = await response.blob()
                const imageUrl = URL.createObjectURL(blob);
                return imageUrl
            } else {
                alert("Falha ao obter mídia, verifique a mídia no seu telefone")
            }
        })
}

const setSniper = async (instance) => {
    const { accountId, id, auth: { jwt }, sniperId, sniperEnabled } = instance
    const body = { accountId, instanceId: id, jwt, enabled: sniperEnabled, publicSniperId: sniperId }

    return await axiosChatService.post(`/whatsapp/sniper/connect/${id}`, body)
}

const updateSniperSession = async ({
    action,
    accountId,
    contactId,
    instanceId }) => {

    const body = { accountId, instanceId, action, phone: contactId }

    return await axiosChatService.put(`/whatsapp/sniper/change_session/${instanceId}`, body)
}

const unsubscribeIgAccount = async ({ access_token, ig_id }) => {
    if (!access_token) {
        console.log('access_token not found')
        return
    }

    return await axiosChatService.delete(`/instagram/unsubscribe`, { data: { access_token, ig_id } })
}

const logoutIgAccount = async (instance) => {
    if (!instance) {
        console.log('instance not found')
        return
    }
    return await axiosChatService.delete(`/instagram/auth/logout`, { data: { instance } })
}

export const ChatAPI = {
    createInstance,
    updateInstance,
    sendMessage,
    sendAudioMessage,
    sendMediaFile,
    resendMessage,
    markAsRead,
    getMediaFile,
    setSniper,
    updateSniperSession,
    unsubscribeIgAccount,
    logoutIgAccount
}
