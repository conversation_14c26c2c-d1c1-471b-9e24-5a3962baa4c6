import { FormControl, OutlinedInput } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import React, { useState } from 'react';

const useStyles = makeStyles((theme) => ({
  formControl: {
    marginBottom: theme.spacing(1),
    width: '100%',
  },
}));

const props = {
  label: '',
  list: [],
  searchFields: [],
  renderList: (list) => React.ReactNode,
  disabled: false
}

const SearchableList = ({
  label,
  list,
  searchFields,
  renderList,
  disabled,
} = props) => {

  const classes = useStyles();
  const [filter, setFilter] = useState('');

  const filteredList = list.filter((item) => {
    return searchFields.some((field) => (item[field] || '').toLowerCase().includes(filter.toLowerCase()));
  });

  return (
    <>
      <FormControl className={classes.formControl} >
        <OutlinedInput
          id={`searchable-list`}
          type={'text'}
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          disabled={disabled}
          placeholder={label}
        />
      </FormControl>
      {renderList(filteredList)}
    </>
  );
};

export default SearchableList;
