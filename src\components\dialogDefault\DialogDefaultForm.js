import {
  Button,
  ButtonGroup,
  Checkbox,
  DialogContent,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  InputAdornment,
  InputLabel,
  makeStyles,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
  Tooltip,
  Typography
} from "@material-ui/core";
import React from 'react';
import { Label } from "reactstrap";

const useStyles = makeStyles((theme) => ({
  label: {
    margin: theme.spacing(1),
    justifyContent: 'center',
    alignItems: 'center',
  },
  formControl: {
    margin: theme.spacing(1),
    width: '90%',
  },
  selectEmpty: {
    marginTop: theme.spacing(2),
  },
}));

export const DialogDefaultForm = ({ inputs, title, description, buttons, children, alignH = 'center', alignV = 'flex-start', subtitle }) => {
  const classes = useStyles();

  const getField = (input, index) => {
    const { type, name, label, value, onChange, checked = false, tooltips, onChecked, options, disabled, prefix, checkTooltip = 'Enable', values, onClick } = input
    const tooltipWidth = checkTooltip.length * 10
    const key = `input-${index}`
    switch (type) {
      case 'text':
        return (
          <TextField
            key={key}
            // margin='normal'
            variant='outlined'
            fullWidth
            autoFocus
            id={name}
            placeholder={label}
            label={label}
            defaultValue={value}
            onChange={onChange}
            className={classes.formControl}
          />
        );
      case 'textArea':
        return (
          <TextField
            key={key}
            // margin='normal'
            variant='outlined'
            fullWidth
            autoFocus
            multiline
            minRows={10}
            maxRows={14}
            id={name}
            placeholder={label}
            label={label}
            defaultValue={value}
            onChange={onChange}
            className={classes.formControl}
          />

          //   <TextField
          //   id="standard-multiline-static"
          //   label="Multiline"
          //   multiline
          //   rows={4}
          //   defaultValue="Default Value"
          //   variant="standard"
          // />
        );
      case 'select':
        return (
          <FormControl variant="outlined" className={classes.formControl} key={key} disabled={disabled}>
            <InputLabel id={`label-${name}`}>{label}</InputLabel>
            <Select
              labelId={`label-${name}`}
              id={name}
              value={value || ''}
              onChange={disabled ? null : onChange}
              disabled={disabled}
              label={label}
            >
              {options.map((option, index) => (
                <MenuItem key={`${name}-option-${index}`} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )
      case 'input-checkbox':
        return (
          <FormControl className={classes.formControl} key={key} variant="outlined" disabled={disabled}>
            <InputLabel htmlFor={`label-${name}`}>{label}</InputLabel>
            <OutlinedInput
              id={`label-${name}`}
              type={'text'}
              value={value || ''}
              onChange={onChange}
              disabled={disabled}
              startAdornment={!!prefix ? <InputAdornment position="start">{prefix}</InputAdornment> : null}
              endAdornment={
                <InputAdornment position="end">
                  <FormControlLabel
                    className={classes.label}
                    control={
                      <Checkbox
                        checked={checked}
                        onChange={disabled ? null : onChecked}
                        name={`${name}Enable`}
                        disabled={disabled}
                      />
                    }
                    label={checkTooltip}
                  />
                </InputAdornment>
              }
              labelWidth={tooltipWidth}
            />
          </FormControl>
        )
      case 'checkbox':
        return (
          <FormControl className={classes.formControl} key={key} variant="outlined" disabled={disabled}>
            <FormControlLabel
              key={key}
              control={
                <Checkbox
                  checked={checked}
                  onChange={disabled ? null : onChecked}
                  name={name}
                  disabled={disabled}
                />
              }
              label={label}
            />
          </FormControl >
        )
      case 'checkboxGroup':
        return (
          <FormControl className={classes.formControl} key={key} variant="outlined" disabled={disabled}>
            <FormLabel component="legend">{label}</FormLabel>
            <FormGroup className={`d-flex flex-row w-100 align-items-center justify-content-flex-start `}>

              {
                values?.map(({ label, disabled = false, checked, onChecked, tooltips }, index) => (
                  <Tooltip
                    key={`tooltip-${label}-${index}`}
                    title={tooltips || ''}
                    placement="top-start"
                  >
                    <FormControlLabel
                      key={key}
                      control={
                        <Checkbox
                          checked={checked}
                          onChange={disabled ? null : onChecked}
                          name={name}
                          disabled={disabled}
                        />
                      }
                      label={label}
                    />
                  </Tooltip>
                ))

              }
            </FormGroup>

          </FormControl>
        )
      case 'buttons':
        return (
          <FormControl className={classes.formControl} key={key} variant="outlined" disabled={disabled}>
            <Label for={`label-${name}`}>{label}</Label>
            <ButtonGroup aria-label={`outlined button group"`} disabled={disabled}>
              {values.map(({ label, value, disabled = false }, index) => (
                <Button
                  key={`button-${label}-${index}`}
                  variant="outlined"
                  onClick={() => onClick(value)}
                  disabled={disabled}
                >
                  {label}
                </Button>
              ))}
            </ButtonGroup>
          </FormControl>
        )
      default:
        console.warn(`Invalid input type: ${type}`)
        return null;
    }
  }

  return (
    <DialogContent dividers className={`d-flex flex-column align-items-${alignH} justify-content-${alignV} `}>
      {title && (
        <Typography gutterBottom variant='h5'>
          {title}
        </Typography>
      )}

      {description && (
        <Typography gutterBottom variant='body1'>
          {description}
        </Typography>
      )}

      {inputs && <div className="d-flex flex-row w-100">
        {children && children}

        <div className={`d-flex flex-column w-100 align-items-${alignH} justify-content-${alignV} `}>
          {inputs && inputs.map((getField))}
        </div>
      </div>}

      {!inputs && children}

      {subtitle && (
        <Typography gutterBottom variant='body1'>
          {subtitle}
        </Typography>
      )}

      {buttons && buttons.filter((button) => !button.hidden).map((button, index) => (
        <Button
          key={`button-${index}`}
          variant="contained"
          color="primary"
          onClick={button.onClick}
          disabled={button.disabled}
        >
          {button.text}
        </Button>
      ))}

    </DialogContent>
  );
}
