/**
 * Notification Component
 */
import { MOMENT_SHORT } from 'Constants'
import moment from 'moment'
import React from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { Badge } from 'reactstrap'

// intl messages
import IntlMessages from 'Util/IntlMessages'
import { langMessages } from '../../lang'

const DeskNotificationsPopper = ({
  deskNotifications,
  updateDeskNotifications,
  onToggleDeskNotifications,
  notificationsToggle,
}) => {
  const newNotifications = deskNotifications.filter(n => !n.viewed)

  return !notificationsToggle ? null : (
    <div className="desk-notifications">
      <div className="notification-menu">
        <div className="notification-content">
          <div className="notification-top d-flex justify-content-between rounded-top bg-secondary">
            <span className="text-white font-weight-bold">
              {!!newNotifications.length && (
                <Badge color="danger" className="badge-xs position-absolute" style={{ top: 10, left: 8 }}>
                  {newNotifications.length}
                </Badge>
              )}
              <i className={'zmdi zmdi-notifications font-lg mr-5'} />
              <IntlMessages id="sidebar.alerts" />
            </span>
            <span
              className="close"
              onClick={e => {
                onToggleDeskNotifications(false)
                !!newNotifications.length && updateDeskNotifications(newNotifications, { viewed: true })
              }}
            >
              <i className="fa fa-minus font-sm text-white" />
            </span>
          </div>
          <Scrollbars className="rct-scroll" autoHeight autoHeightMin={100} autoHeightMax={280}>
            <ul className="list-unstyled notification-list">
              {!deskNotifications.length && (
                <li>
                  <div className="media">
                    <div className="media-body py-20">
                      <span className="text-muted fs-12 d-block">{langMessages['notifications.emptyMsg']}</span>
                    </div>
                  </div>
                </li>
              )}
              {!!deskNotifications.length &&
                deskNotifications.map((n, key) => (
                  <li key={key} className={`${n.viewed ? '' : 'new'}`}>
                    <div className="media">
                      {!!n.thumbnail && n.thumbnail.indexOf('http') >= 0 && (
                        <div className="mr-10">
                          <img src={n.thumbnail} alt="user profile" className="media-object rounded-circle" width="50" height="50" />
                        </div>
                      )}
                      <div className="media-body pt-5">
                        <div className="d-flex justify-content-between">
                          <h5 className="mb-5 text-primary">{n.title}</h5>
                          <span className="text-muted fs-12">{moment(n.scheduled_date).format(MOMENT_SHORT)}</span>
                        </div>
                        <span className="text-muted fs-12 d-block">{n.message}</span>
                      </div>
                    </div>
                  </li>
                ))}
            </ul>
          </Scrollbars>
        </div>
        {/*
				<div className="notification-foot p-2 bg-white rounded-bottom">
					<Button
						variant="raised"
						color="primary"
						className="mr-10 btn-xs text-white bg-secondary"
					>
						<IntlMessages id="button.viewAll" />
					</Button>
				</div>
				*/}
      </div>
    </div>
  )
}

export default DeskNotificationsPopper
