/**
 * Auth Actions
 * Auth Action With Google, Facebook, Twitter and Github
 */
import {
  FirebaseAuth,
  FirestoreRef,
  facebookAuthProvider,
  getAuthUser,
  githubAuthProvider,
  googleAuthProvider,
  twitterAuth<PERSON>rovider,
} from '../firebase'

import pagarm<PERSON><PERSON><PERSON> from '../lib/pagarme'

import {
  AUTH_USER_FAILURE,
  AUTH_USER_SUCCESS,
  GET_ACCOUNT,
  GET_ACCOUNT_FAILURE,
  GET_ACCOUNT_OWNER,
  GET_ACCOUNT_OWNER_FAILURE,
  GET_ACCOUNT_OWNER_SUCCESS,
  GET_ACCOUNT_SUCCESS,
  GET_AUTH_USER,
  GET_AUTH_USER_ERROR,
  GET_AUTH_USER_SUCCESS,
  GET_PENDING_UPDATES_SUCCESS,
  GET_SUBSCRIPTION_SUCCESS,
  INACTIVE_ACCOUNT_ALERT,
  LOGIN_USER,
  LOGIN_USER_FAILURE,
  LOGIN_USER_SUCCESS,
  LOGOUT_USER,
  PASSWORD_RESET,
  PASSWORD_RESET_FAILURE,
  PASSWORD_RESET_SUCCESS,
  RESET_ERROR_MSG,
  RESET_FIRESTORE_ERROR,
  RESET_SUCCESS_MSG,
  SAVE_ACCOUNT,
  SAVE_ACCOUNT_END,
  SAVE_ACCOUNT_FAILURE,
  SAVE_ACCOUNT_SUCCESS,
  SAVE_NEW_ACCOUNT,
  SAVE_NEW_ACCOUNT_FAILURE,
  SAVE_NEW_ACCOUNT_SUCCESS,
  SIGNUP_USER,
  SIGNUP_USER_FAILURE,
  SIGNUP_USER_SUCCESS,
  UPDATE_ACCOUNT_OWNER,
  UPDATE_USER,
  VERIFY_ACCOUNT,
  VERIFY_ACCOUNT_FAILURE,
  VERIFY_ACCOUNT_SUCCESS,
  VERIFY_USER,
  VERIFY_USER_FAILURE,
  VERIFY_USER_SUCCESS,
} from './types'

import moment from 'moment'

// AppModules
import AppModules from 'Constants/AppModules'

// AppActions
import AppActions from 'Constants/AppActions'

import { ACTIVE_STATUS, DOC_DOESNT_EXIST, MOMENT_ISO } from '../constants/AppConstants'

import { NotificationManager } from 'react-notifications'

import { createQIUserFromAuth } from './QIUsersActions'

import { APP_ENVIROINMENT } from 'Constants'

import { langMessages } from '../lang'

import { localJSON, replaceUndefined, sessionJSON } from '../helpers/helpers'

import { ACCOUNTS_COLLECTION_NAME, QIUSERS_COLLECTION_NAME, UPDATES_SUBCOLLECTION_NAME } from '../constants/AppCollections'

import { ORPHANS_ACCOUNT, OWNER_FIELD, URL_PARAMS_AFFILIATE_ID, URL_PARAMS_PARENT_ID } from '../constants/AppConstants'

import { ADMIN_ROLE, AFFILIATE_ROLE, MANAGER_ROLE, OWNER_ROLE, QIPLUS_ROLES_LEVELS } from '../constants/UsersRoles'

import accountModel from '../routes/accounts/model'

import { SubscriptionRepository } from "Services/subscription.repository"
import { savePostFields } from '../firebase/functions'

const bindingRoles = [AFFILIATE_ROLE, OWNER_ROLE, MANAGER_ROLE]

const errorCodes = {
  'auth/popup-closed-by-user': '',
  'auth/cancelled-popup-request': '',
  'auth/user-not-found': langMessages['alerts.userNotRegistered'],
  'auth/email-already-in-use': langMessages['alerts.emailAlreadyExists'],
  'auth/phone-number-already-exists': langMessages['alerts.phoneAlreadyExists'],
  'auth/weak-password': langMessages['alerts.weakPassword'],
  'auth/wrong-password': langMessages['alerts.wrongPassword'],
  'auth/invalid-password': langMessages['alerts.invalidPassword'],
  'auth/argument-error': langMessages['alerts.missingLoginData'],
  'auth/invalid-email': langMessages['errors.invalidEmailAddress'],
  'auth/network-request-failed': langMessages['errors.dbErrorMsg'],
  'auth/too-many-requests': langMessages['alerts.tooManyRequests'],
  'auth/invalid-custom-token': langMessages['errors.invalidCustomToken'],
}

const queryString = window.location.search
const urlParams = new URLSearchParams(queryString)
const parentId = urlParams.get(URL_PARAMS_PARENT_ID)
const affiliateId = urlParams.get(URL_PARAMS_AFFILIATE_ID)

for (let p of urlParams) {
  if (p) localJSON.set('loadedParams', queryString)
}

if (parentId) {
  localJSON.set(URL_PARAMS_PARENT_ID, parentId)
}
if (affiliateId) {
  localJSON.set(URL_PARAMS_AFFILIATE_ID, affiliateId)
}

const isDev = APP_ENVIROINMENT === 'development'
// console.log('out > parentId',parentId);
// console.log('out > affiliateId',affiliateId);
// console.log('validateCPF',validateCPF);

export const singinSetupCb = (qiuser, account, dispatch) => {
  if ((qiuser || {}).status === ACTIVE_STATUS && (account || {}).active === true) {
    localJSON.set('lastValidatedAt', new Date().getTime())
    localJSON.set('account', account)
    dispatch({ type: LOGIN_USER_SUCCESS, payload: qiuser })
    dispatch({ type: VERIFY_USER_SUCCESS, payload: qiuser })
    dispatch({ type: UPDATE_ACCOUNT_OWNER, payload: account })
    localJSON.remove(URL_PARAMS_AFFILIATE_ID)
    localJSON.remove(URL_PARAMS_PARENT_ID)
  } else if ((qiuser || {}).status === ACTIVE_STATUS) {
    localJSON.remove('lastValidatedAt')
    dispatch({ type: UPDATE_USER, payload: qiuser })
  } else {
    dispatch({ type: LOGIN_USER_FAILURE })
    localJSON.remove('lastValidatedAt')
  }
}

export const singupCallback = (authUser, qiuser, history) => (dispatch, getState) => {
  return singinQIPlusCb(authUser, qiuser, history, dispatch)
}

export const singinQIPlusCb = (authUser, qiuser, history, dispatch) => {
  const { pathname } = window.location

  let updateData = {}

  if (qiuser.status !== ACTIVE_STATUS) {
    dispatch({ type: LOGIN_USER_FAILURE })
    logoutUserFromFirebase(true)(dispatch)
    NotificationManager.error(langMessages['alerts.userIsNotActive'])
    return
  }

  if (authUser.uid && authUser.uid !== qiuser.uid) {
    updateData.uid = authUser.uid
  }
  if (authUser.phoneNumber && !qiuser.mobile) {
    updateData.mobile = authUser.phoneNumber
  }
  if (authUser.photoURL && !qiuser.avatar) {
    updateData.avatar = authUser.photoURL
  }

  // console.log('singinQIPlusCb qiuser', qiuser);
  // console.log('singinQIPlusCb authUser', authUser);
  dispatch({ type: AUTH_USER_SUCCESS, payload: authUser })

  let accountFn
  if (qiuser.accountId && qiuser.accountId !== ORPHANS_ACCOUNT) {
    accountFn = unconnectedGetAccount(qiuser.accountId, dispatch)
  } else {
    accountFn = unconnectedGetAccountByOwner(qiuser.owner, dispatch)
  }
  accountFn
    .then(async account => {
      // console.log('singinQIPlusCb account', account)

      let { config, payment_status } = account
      const { status, gateway } = payment_status || {}

      let { subscription } = (gateway ? account?.[gateway] : account.pagarme) || {}
      // Ver se tem o novo campo de status
      if (!subscription) {
        const subscriptionRepository = new SubscriptionRepository(account.ID)
        subscription = await subscriptionRepository.findActive()
      }

      let validation = {}

      const billing = config && config.billing === true

      if (billing && !payment_status) {
        validation = await pagarmeApi.subscription.validate(account)
        subscription = validation.subscription
        account.active = account.active && validation.active
        account.pagarme.subscription = validation.subscription
        dispatch({ type: GET_SUBSCRIPTION_SUCCESS, payload: subscription })
      }

      if (billing && payment_status) {
        account.active = status === 'paid'
        if (account.active) {
          subscription.status = status
          dispatch({ type: GET_SUBSCRIPTION_SUCCESS, payload: subscription })
        }
      }

      subscription = subscription || {}

      singinSetupCb(qiuser, account, dispatch)

      if (account && account.active === true) {
        NotificationManager.success(`${langMessages['texts.userWelcome']}, ${qiuser.firstName || qiuser.displayName}`)
        setTimeout(() => history.push('/'), 1000)
      } else if (account && account.active !== true) {
        try {
          if (config.billing === true) {
            if (!subscription.id) {
              NotificationManager.error(`${langMessages['payment.billingAccountNotFound']}, ${qiuser.firstName || qiuser.displayName}`)
              return
            } else if (subscription.status === 'trialing' && validation.pending) {
              NotificationManager.success(
                `${langMessages['payment.yourAccountWillBeReadyOnPaymentConfirm']}, ${qiuser.firstName || qiuser.displayName}`
              )
              return
            } else if (subscription.status === 'unpaid' || subscription.status === 'pending_payment') {
              NotificationManager.success(
                `${langMessages['payment.yourAccountWillBeReadyOnPaymentConfirm']}, ${qiuser.firstName || qiuser.displayName}. ${langMessages['payment.pleaseCheckYourEmail']}`
              )
              return
            }

            const accountId = account.ID
            const updatesSnap = await FirestoreRef.collection(ACCOUNTS_COLLECTION_NAME)
              .doc(`${accountId}`)
              .collection(UPDATES_SUBCOLLECTION_NAME)
              .get()

            if (updatesSnap.size) {
              updatesSnap.forEach(doc => {
                let update = doc.data()
                if (((update.pagarme || {}).subscription || {}).status === 'unpaid') {
                  NotificationManager.success(
                    `${langMessages['payment.updatesWillBeReadyOnPaymentConfirm']}, ${qiuser.firstName || qiuser.displayName}`
                  )
                }
              })
            } else {
              NotificationManager.error(`${langMessages['payment.accountIsNotActive']}, ${qiuser.firstName || qiuser.displayName}`)
              pathname.indexOf('plans') === -1 && history.push('/plans')
              return
            }
          } else {
            NotificationManager.error(`${langMessages['payment.accountIsNotActive']}, ${qiuser.firstName || qiuser.displayName}`)
            pathname.indexOf('plans') === -1 && history.push('/plans')
            return
          }
        } catch (error) {
          NotificationManager.error(`${langMessages['payment.accountIsNotActive']}, ${qiuser.firstName || qiuser.displayName}`)
          pathname.indexOf('login') === -1 && history.push('/login')
          return
        }
      } else {
        pathname.indexOf('plans') === -1 && history.push('/plans')
      }
    })
    .catch(error => {
      console.error(error)
      NotificationManager.error(langMessages['errors.dbErrorMsg'])
    })

  if (Object.keys(updateData).length && qiuser.ID) {
    FirestoreRef.collection(QIUSERS_COLLECTION_NAME).doc(qiuser.ID).update(updateData)
  }
}

export const singinQIPlus = (authUser, history, dispatch) => {
  FirestoreRef.collection(QIUSERS_COLLECTION_NAME)
    .where('uid', '==', `${authUser.uid}`)
    .get()
    .then(snapshot => {
      // console.log('snapshot',snapshot);
      if (snapshot.size === 1) {
        snapshot.forEach(doc => {
          const qiuser = doc.data()
          singinQIPlusCb(authUser, qiuser, history, dispatch)
        })
      } else {
        dispatch({ type: SIGNUP_USER_FAILURE })
        history.push('/register')
        // NotificationManager.error(langMessages["errors.genericErrorMsg"]);
      }
    })
    .catch(error => {
      console.log('error', error)
      dispatch({ type: LOGIN_USER_FAILURE })
      if (error && error.code && error.code in errorCodes) {
        errorCodes[error.code] && NotificationManager.error(errorCodes[error.code])
      } else {
        NotificationManager.error(langMessages['errors.dbErrorMsg'])
      }
    })
}

export const singupQIPlus = (formData, account) => (dispatch, getState) => {
  dispatch({ type: SIGNUP_USER })

  return new Promise((res, rej) => {
    FirestoreRef.collection(QIUSERS_COLLECTION_NAME)
      .where('email', '==', `${formData.email}`)
      .get()
      .then(async snapshot => {
        if (snapshot.size) {
          return rej({ code: 'auth/email-already-in-use', message: 'alerts.emailAlreadyExists' })
        }
        let authUser = FirebaseAuth.currentUser
        console.log('FirebaseAuth.currentUser', authUser)
        if (!authUser || !authUser.uid) {
          const newAuth = await FirebaseAuth.createUserWithEmailAndPassword(formData.email, formData.password)
          authUser = newAuth.user
        }
        return authUser
      })
      .then(authUser => {
        if (formData.password) {
          delete formData.password
        }

        let queryString = localJSON.get('loadedParams', '')
        let urlParams = new URLSearchParams(queryString)
        let role = urlParams.get('role')
        let level = !!role && QIPLUS_ROLES_LEVELS[role]

        if (!level && isNaN(Number(level)) && Number(level) > QIPLUS_ROLES_LEVELS[ADMIN_ROLE]) {
          formData.roles = [role]
          formData.level = level
        } else {
          formData.roles = [OWNER_ROLE]
          formData.level = QIPLUS_ROLES_LEVELS[OWNER_ROLE]
        }

        bindingRoles.forEach(role => {
          if (urlParams.get(role)) {
            formData[role] = urlParams.get(role).toString()
          }
        })

        return createQIUserFromAuth(authUser, formData)
      })
      .then(newUser => {
        localJSON.remove('loadedParams')

        dispatch({ type: SIGNUP_USER_SUCCESS })
        dispatch({ type: UPDATE_USER, payload: newUser })

        singinSetupCb(newUser, account, dispatch)

        return res(newUser)
      })
      .catch(rej)
  }).catch(error => {
    console.error(error)
    dispatch({ type: SIGNUP_USER_FAILURE })
    if (error && error.code && error.code in errorCodes) {
      errorCodes[error.code] && NotificationManager.error(errorCodes[error.code])
    } else {
      NotificationManager.error(langMessages[error.message] || error.message || langMessages['errors.dbErrorMsg'])
    }
  })
}

/**
 * Redux Action To Signup User In Firebase
 */
export const signupUserInFirebase = (formData, history) => (dispatch, getState) => {
  dispatch({ type: LOGIN_USER })
  FirebaseAuth.createUserWithEmailAndPassword(formData.email, formData.password)
    .then(result => {
      // console.log('signupUserInFirebase > result',result);
      const authUser = result.user
      singinQIPlus(authUser, history, dispatch)
    })
    .catch(function (error) {
      console.error(error)
      dispatch({ type: LOGIN_USER_FAILURE })
      if (error && error.code && error.code in errorCodes) {
        errorCodes[error.code] && NotificationManager.error(errorCodes[error.code])
      } else {
        NotificationManager.error(langMessages[error.message] || error.message)
      }
    })
}

/**
 * Redux Action To Sigin User With Firebase
 */
export const signinUserInFirebase = (formData, history) => (dispatch, getState) => {
  dispatch({ type: LOGIN_USER })
  FirebaseAuth.signInWithEmailAndPassword(formData.email, formData.password)
    .then(result => {
      // console.log('signinUserInFirebase > result', result);
      const authUser = result.user
      singinQIPlus(authUser, history, dispatch)
    })
    .catch(error => {
      console.error(error)
      dispatch({ type: LOGIN_USER_FAILURE })
      if (error && error.code && error.code in errorCodes) {
        errorCodes[error.code] && NotificationManager.error(errorCodes[error.code])
      } else {
        NotificationManager.error(langMessages[error.message] || error.message)
      }
    })
}

/**
 * Redux Action To Signin User In Firebase With Google
 */
export const signinUserWithGoogle = history => (dispatch, getState) => {
  dispatch({ type: LOGIN_USER })
  FirebaseAuth.signInWithPopup(googleAuthProvider)
    .then(result => {
      // console.log('signinUserWithGoogle > result',result);
      const authUser = result.user
      singinQIPlus(authUser, history, dispatch)
      // console.log('signinUserWithGoogle : authUser',authUser);
      // console.log('signinUserWithGoogle : currentUser',FirebaseAuth.currentUser);
    })
    .catch(function (error) {
      console.error(error)
      dispatch({ type: LOGIN_USER_FAILURE })
      if (error && error.code && error.code in errorCodes) {
        errorCodes[error.code] && NotificationManager.error(errorCodes[error.code])
      } else {
        NotificationManager.error(langMessages[error.message] || error.message)
      }
    })
}

/**
 * Redux Action To Signin User In Firebase With Facebook
 */
export const signinUserWithFacebook = history => (dispatch, getState) => {
  dispatch({ type: LOGIN_USER })
  FirebaseAuth.signInWithPopup(facebookAuthProvider)
    .then(result => {
      // console.log('signinUserWithFacebook > result',result);
      const authUser = result.user
      singinQIPlus(authUser, history, dispatch)
    })
    .catch(function (error) {
      console.error(error)
      dispatch({ type: LOGIN_USER_FAILURE })
      if (error && error.code && error.code in errorCodes) {
        errorCodes[error.code] && NotificationManager.error(errorCodes[error.code])
      } else {
        NotificationManager.error(langMessages[error.message] || error.message)
      }
    })
}

/**
 * Redux Action To Signin User In Firebase With Github
 */
export const signinUserWithGithub = history => (dispatch, getState) => {
  dispatch({ type: LOGIN_USER })
  FirebaseAuth.signInWithPopup(githubAuthProvider)
    .then(result => {
      // console.log('signinUserWithGithub > result',result);
      const authUser = result.user
      singinQIPlus(authUser, history, dispatch)
    })
    .catch(function (error) {
      console.error(error)
      dispatch({ type: LOGIN_USER_FAILURE })
      if (error && error.code && error.code in errorCodes) {
        errorCodes[error.code] && NotificationManager.error(errorCodes[error.code])
      } else {
        NotificationManager.error(langMessages[error.message] || error.message)
      }
    })
}

/**
 * Redux Action To Signin User In Firebase With Custom Token
 */
export const signinUserWithCustomToken = (customToken, history) => (dispatch, getState) => {
  dispatch({ type: LOGIN_USER })
  return FirebaseAuth.signInWithCustomToken(customToken)
    .then(result => {
      const authUser = result.user
      singinQIPlus(authUser, history, dispatch)
      return authUser
    })
    .catch(error => {
      console.error(error)
      dispatch({ type: LOGIN_USER_FAILURE })
      if (error && error.code && error.code in errorCodes) {
        errorCodes[error.code] && NotificationManager.error(errorCodes[error.code])
      } else {
        NotificationManager.error(langMessages[error.message] || error.message)
      }
      return null
    })
}

/**
 * Redux Action To Signin User In Firebase With Twitter
 */
export const signinUserWithTwitter = history => (dispatch, getState) => {
  dispatch({ type: LOGIN_USER })
  FirebaseAuth.signInWithPopup(twitterAuthProvider)
    .then(result => {
      // console.log('result',result);
      // singinQIPlus(authUser, history, dispatch)
    })
    .catch(function (error) {
      console.error(error)
      dispatch({ type: LOGIN_USER_FAILURE })
      if (error && error.code && error.code in errorCodes) {
        errorCodes[error.code] && NotificationManager.error(errorCodes[error.code])
      } else {
        NotificationManager.error(langMessages[error.message] || error.message)
      }
    })
}

/**
 * Redux Action To Signout User From  Firebase
 */
export const logoutUserFromFirebase = programatically => (dispatch, getState) => {
  return FirebaseAuth.signOut()
    .then(() => {
      dispatch({ type: LOGOUT_USER })
      programatically !== true && NotificationManager.success(langMessages['texts.userGoodbye'])
    })
    .catch(error => {
      console.error(error)
      NotificationManager.error(langMessages[error.message] || error.message)
    })
}

/**
 * Redux Action To Reset Password From  Firebase
 */
export const resetFirebasetPassword = user => (dispatch, getState) => {
  dispatch({ type: PASSWORD_RESET })
  FirebaseAuth.sendPasswordResetEmail(user.email)
    .then(() => {
      dispatch({ type: PASSWORD_RESET_SUCCESS, payload: langMessages['texts.passwordResetEmailSent'] })
    })
    .catch(error => {
      console.error(error)
      let errorMsg = ''
      if (error && error.code && error.code in errorCodes) {
        errorCodes[error.code] && (errorMsg = errorCodes[error.code])
      } else if (error && error.message) {
        errorMsg = error.message
      } else {
        errorMsg = langMessages['errors.genericErrorMsg']
      }
      dispatch({ type: PASSWORD_RESET_FAILURE, payload: errorMsg /* langMessages["texts.passwordResetEmailSent"] */ })
    })
}

/**
 * get auth user
 */
export const getCurrentAuthUser = () => (dispatch, getState) => {
  FirebaseAuth.onAuthStateChanged(async authUser => {
    // console.log('onAuthStateChanged',authUser);
    if (authUser && authUser.uid) {
      dispatch({ type: AUTH_USER_SUCCESS, payload: authUser })

      FirestoreRef.collection(QIUSERS_COLLECTION_NAME)
        .where('uid', '==', `${authUser.uid}`)
        .limit(1)
        .get()
        .then(async snapshot => {
          let qiuser, account
          if (snapshot.size)
            snapshot.forEach(doc => {
              qiuser = doc.data()
            })
          const currentAccountID = sessionJSON.get('account', {}).ID || (qiuser || {}).accountId
          if (currentAccountID) {
            account = await unconnectedGetAccount(currentAccountID, dispatch)
          }
          return { qiuser, account }
        })
        .then(({ qiuser, account }) => {
          singinSetupCb(qiuser, account, dispatch)
        })
        .catch(error => {
          console.log('error', error)
          dispatch({ type: LOGIN_USER_FAILURE })
        })
    } else {
      dispatch({ type: AUTH_USER_FAILURE })
    }
  })
}

/**
 * Verify QIUser
 */
export const validateUser = (userId, history) => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  const currentOwnerID = sessionJSON.get('currentOwnerID', false)
  const currentAccountID = sessionJSON.get('account', {}).ID || user.accountId

  dispatch({ type: VERIFY_USER })

  return new Promise((res, rej) => {
    let errorMsg = ''
    FirestoreRef.collection(QIUSERS_COLLECTION_NAME)
      .doc(`${userId}`)
      .onSnapshot(
        async doc => {
          if (doc.exists) {
            const qiuser = doc.data()

            if (qiuser.status !== ACTIVE_STATUS) {
              return rej(langMessages['alerts.userIsNotActive'])
            }

            dispatch({ type: VERIFY_USER_SUCCESS, payload: qiuser })

            // ------------------------------------------------
            // Se está navegando em outra conta não verificar
            // ------------------------------------------------
            // console.log('currentAccountID',currentAccountID);
            // console.log('user.accountId',user.accountId);
            // console.log('user.owner',user.owner);
            // console.log('currentOwnerID',currentOwnerID);

            if (user.owner !== currentOwnerID || user.accountId !== currentAccountID) {
              return
            }
            // ------------------------------------------------

            dispatch({ type: VERIFY_ACCOUNT })

            let account
            if (qiuser.accountId && qiuser.accountId !== ORPHANS_ACCOUNT) {
              account = await unconnectedGetAccount(qiuser.accountId, dispatch)
            } else {
              account = await unconnectedGetAccountByOwner(qiuser.owner, dispatch)
            }

            if (account && account.ID) {
              if ((account.config || {}).billing === true) {
                if (!account.payment_status) {
                  let { subscription, active } = await pagarmeApi.subscription.validate(account)
                  account.active = account.active && active
                  account.pagarme.subscription = subscription
                  dispatch({ type: GET_SUBSCRIPTION_SUCCESS, payload: subscription })
                  savePostFields(ACCOUNTS_COLLECTION_NAME, account.ID, { pagarme: account.pagarme })
                } else {
                  const subscriptionRepository = new SubscriptionRepository(account.ID)
                  const subscription = await subscriptionRepository.findActive()
                  dispatch({ type: GET_SUBSCRIPTION_SUCCESS, payload: subscription })
                }
              }

              singinSetupCb(qiuser, account, dispatch)

              if (account && account.active === true) {
                dispatch({ type: VERIFY_ACCOUNT_SUCCESS, payload: account })
                return res(qiuser)
              } else if (account && account.active !== true) {
                dispatch({ type: INACTIVE_ACCOUNT_ALERT })
                errorMsg = `${langMessages['payment.accountIsNotActive']}, ${qiuser.firstName || qiuser.displayName}`
              } else {
                errorMsg = `${langMessages['payment.accountDoesNotExist']}, ${qiuser.firstName || qiuser.displayName}`
              }

              return rej(errorMsg)
            }
          } else {
            NotificationManager.error(langMessages['errors.dbErrorMsg'])
            console.error({ doc, err: langMessages['errors.userHasBeenRemoved'] })
            return null
            // return errorCb(langMessages["errors.userHasBeenRemoved"])
          }
        },
        error => {
          console.error(error)
        }
      )
  }).catch(errorMsg => {
    dispatch({ type: VERIFY_USER_FAILURE })
    dispatch({ type: VERIFY_ACCOUNT_FAILURE })
    !!errorMsg && NotificationManager.error(errorMsg)
    history.push('/login')
  })
}

/**
 * Get Account
 */
export const getAccount = accountId => dispatch => unconnectedGetAccount(accountId, dispatch)
export const unconnectedGetAccount = (accountId, dispatch) => {
  if (!dispatch) dispatch = () => { }
  dispatch({ type: GET_ACCOUNT })
  return new Promise((res, rej) => {
    FirestoreRef.collection(ACCOUNTS_COLLECTION_NAME)
      .doc(`${accountId}`)
      .onSnapshot(
        doc => {
          if (doc.exists) {
            const account = doc.data()
            dispatch({ type: GET_ACCOUNT_SUCCESS, payload: account })
            // look for updates
            const docRef = doc.ref
            docRef
              .collection(UPDATES_SUBCOLLECTION_NAME)
              .get()
              .then(s => {
                if (s.size) {
                  s.forEach(d => {
                    let update = d.data()
                    dispatch({ type: GET_PENDING_UPDATES_SUCCESS, payload: update })
                  })
                }
              })
            return res(account)
          }
          dispatch({ type: GET_ACCOUNT_FAILURE })
          return res(false)
        },
        error => {
          console.error(error)
          dispatch({ type: GET_ACCOUNT_FAILURE })
          return rej(null)
        }
      )
  })
}

export const getAccountByOwner = ownerId => dispatch => unconnectedGetAccountByOwner(ownerId, dispatch)
export const unconnectedGetAccountByOwner = (ownerId, dispatch) => {
  if (!dispatch) dispatch = () => { }
  dispatch({ type: GET_ACCOUNT })
  return new Promise((res, rej) => {
    FirestoreRef.collection(ACCOUNTS_COLLECTION_NAME)
      .where(OWNER_FIELD, '==', `${ownerId}`)
      .limit(1)
      .get()
      .then(
        snapshot => {
          let account = {}
          if (snapshot.size) {
            snapshot.forEach(doc => {
              account = doc.data()
              // look for updates
              const docRef = doc.ref
              docRef
                .collection(UPDATES_SUBCOLLECTION_NAME)
                .get()
                .then(s => {
                  if (s.size) {
                    s.forEach(d => {
                      let update = d.data()
                      dispatch({ type: GET_PENDING_UPDATES_SUCCESS, payload: update })
                    })
                  }
                })
            })
          }
          if (account.ID) {
            dispatch({ type: GET_ACCOUNT_SUCCESS, payload: account })
            return res(account)
          }
          dispatch({ type: GET_ACCOUNT_FAILURE })
          return res(false)
        },
        error => {
          console.error(error)
          dispatch({ type: GET_ACCOUNT_FAILURE })
          return rej(null)
        }
      )
  })
}

export const saveNewAccount = (data, isSignup) => (dispatch, getState) => {
  isSignup = isSignup !== false

  const { pagarme, accountOwner, qiplusPlan, planOption, planConfig } = data

  dispatch({ type: SAVE_NEW_ACCOUNT })

  return new Promise((resolve, reject) => {
    let parentId = data.parentId || localJSON.get(URL_PARAMS_PARENT_ID, '')
    let affiliateId = data.affiliateId || localJSON.get(URL_PARAMS_AFFILIATE_ID, '')
    // console.log('in > parentId',parentId);
    // console.log('in > affiliateId',affiliateId);

    return createAccount({ accountOwner, qiplusPlan, planOption, planConfig, parentId, affiliateId })
      .then(async ({ account }) => {
        accountOwner.accountId = account.ID
        try {
          await FirestoreRef.collection(QIUSERS_COLLECTION_NAME).doc(accountOwner.ID).update({ accountId: account.ID })
        } catch (error) {
          console.error('saveNewAccount > updateUser > error', error)
        }
        return { account, accountOwner }
      })
      .then(({ account, accountOwner }) => {
        return createAccountBilling({ pagarme, account, accountOwner })
      })
      .then(({ account, accountOwner, paid }) => {
        // console.log('account',account);
        // console.log('accountOwner',accountOwner);
        if (isSignup) {
          singinSetupCb(accountOwner, account, dispatch)
          localJSON.set('account', account);
          dispatch({ type: SAVE_NEW_ACCOUNT_SUCCESS, payload: account })
        } else {
          dispatch({ type: SAVE_ACCOUNT_END, payload: account })
        }
        localJSON.remove(URL_PARAMS_PARENT_ID)
        localJSON.remove(URL_PARAMS_AFFILIATE_ID)
        return resolve({ account, savedAccount: account, accountOwner, paid })
      })
      .catch(function (error) {
        dispatch({ type: SAVE_NEW_ACCOUNT_FAILURE })
        return reject(error)
      })
  })
}

export const saveAccount = (data, isSignup) => (dispatch, getState) => {
  isSignup = isSignup !== false

  const { pagarme, account, accountOwner, qiplusPlan, planOption, planConfig } = data

  dispatch({ type: SAVE_ACCOUNT })

  return new Promise((resolve, reject) => {
    if (!account.ID) {
      dispatch({ type: SAVE_ACCOUNT_FAILURE, error: 'No Account ID' })
      return reject('No Account ID')
    }

    const updatedAccount = updateAccountPlan({ account, accountOwner, qiplusPlan, planOption, planConfig })

    return updateAccountBilling({ pagarme, account: updatedAccount, accountOwner })
      .then(({ account, savedAccount, paid, accountOwner }) => {
        if (isSignup) {
          singinSetupCb(accountOwner, account, dispatch)
          dispatch({ type: SAVE_ACCOUNT_SUCCESS, payload: account })
        } else {
          dispatch({ type: SAVE_ACCOUNT_END, payload: account })
        }
        return resolve({ account, savedAccount, paid, accountOwner })
      })
      .catch(function (error) {
        dispatch({ type: SAVE_ACCOUNT_FAILURE, error })
        return reject(error)
      })
  })
}

export const createAccount = ({ accountOwner, qiplusPlan, planOption, planConfig, parentId, affiliateId }) => {
  return new Promise((resolve, reject) => {
    const accountRef = FirestoreRef.collection(ACCOUNTS_COLLECTION_NAME).doc()
    const accountId = accountRef.id

    const newAccount = replaceUndefined({
      ...accountModel,
      active: false,
      id: accountId,
      ID: accountId,
      thumbnail: accountOwner.avatar || '',

      date: moment().format(MOMENT_ISO),
      modified: moment().format(MOMENT_ISO),

      uid: accountOwner.uid,
      owner: accountOwner.ID,

      parentId,
      affiliateId,
    })

    console.log('createAccount > newAccount', { account: newAccount, accountOwner, qiplusPlan, planOption, planConfig })

    const updatedAccount = updateAccountPlan({ account: newAccount, accountOwner, qiplusPlan, planOption, planConfig })

    return accountRef
      .set(updatedAccount)
      .then(doc => resolve({ account: updatedAccount }))
      .catch(error => reject(error))
  })
}

export const updateAccountPlan = ({ account, accountOwner, qiplusPlan, planOption, planConfig }) => {
  const accountPlan = window.jsonClone(qiplusPlan)

  const config = { ...accountModel.config, ...account.config }
  const actions = { ...accountModel.actions, ...account.actions }

  Object.keys(config).forEach((key, i) => {
    if (key in accountPlan.config) {
      config[key] = accountPlan.config[key]
    }
  })

  Object.keys(AppModules)
    .filter(moduleKey => planConfig?.[moduleKey] || planConfig?.[`${moduleKey}_included`])
    .forEach(moduleKey => {
      let included = accountPlan.config?.[`${moduleKey}_included`] || AppModules[moduleKey].included || 0
      let selected = planConfig[moduleKey] || planConfig[`${moduleKey}_included`]
      config[`${moduleKey}_included`] = Math.max(included, selected)
      if (selected !== included) {
        config.custom_plan = true
      }
    })

  Object.keys(AppActions)
    .filter(moduleKey => !AppActions[moduleKey].hidden)
    .forEach(moduleKey => {
      actions[moduleKey] = Boolean((accountPlan.actions || {})[moduleKey])
    })

  const { contacts_min, contacts_max, monthly_value, yearly_value } = planOption

  let titleFields = []
  accountOwner && titleFields.push(accountOwner.displayName || accountOwner.firstName)
  titleFields.push(accountPlan.title)

  const updatedAccount = {
    ...account,
    title: titleFields.join(' - '),
    modified: moment().format(MOMENT_ISO),

    planId: accountPlan.ID,
    plan: accountPlan,

    config,
    actions,
    data: {
      ...accountModel.data,
      ...account.data,
      contacts: contacts_max,
      contacts_min,
      contacts_max,
      monthly_value,
      yearly_value,
    },
    values: {
      ...accountModel.values,
      ...account.values,
      ...accountPlan.values,
    },
    modules: {
      ...accountModel.modules,
      ...account.modules,
      ...accountPlan.modules,
    },
    levels: {
      ...accountModel.levels,
      ...account.levels,
      ...accountPlan.levels,
    },
  }

  if (planConfig?.shotx === 1) {
    updatedAccount.modules.shotx = true
  }

  return updatedAccount
}

export const updateBillingInfo = ({ pagarme, pagarmeResults, account, accountOwner }) => {
  const { plan, implementation, subscription } = pagarmeResults
  const { parcelas, recurrency } = pagarme
  const { customer } = pagarme.subscription
  const { billing_data } = account

  if ('phone' in subscription && (subscription.phone || {}).number) {
    const { phone } = subscription
    billing_data.phone = {
      ...billing_data.phone,
      ...phone,
    }
  } else {
    let { phone_numbers } = customer
    let fullNumber = (phone_numbers || [])[0] || ''
    billing_data.phone = {
      ddi: fullNumber.replace(/\D/g, '').substr(0, 2),
      ddd: fullNumber.replace(/\D/g, '').substr(2, 2),
      number: fullNumber.replace(/\D/g, '').substr(4, fullNumber.length - 4),
    }
  }

  if ('address' in subscription) {
    const { address } = subscription
    billing_data.address = {
      ...billing_data.address,
      ...address,
    }
  } else if ('address' in customer) {
    billing_data.address = {
      ...billing_data.address,
      ...customer.address,
    }
  }

  if ('customer' in subscription) {
    const {
      customer: { name, email, external_id, document_type, document_number },
    } = subscription
    // customer
    billing_data.customer.email = email || accountOwner.email
    billing_data.customer.external_id = external_id || accountOwner.uid || accountOwner.ID

    const isCPF = document_type === 'cpf'
    if (isCPF) {
      billing_data.customer.name = name || accountOwner.displayName
      billing_data.customer.type = 'individual'
      billing_data.customer.document_type = 'cpf'
      billing_data.customer.document_number = document_number || accountOwner.cpf || ''
    } else {
      billing_data.customer.name = name || accountOwner.companyName || accountOwner.displayName
      billing_data.customer.type = 'corporation'
      billing_data.customer.document_type = 'cnpj'
      billing_data.customer.document_number = document_number || accountOwner.cnpj || ''
    }
  }

  const updatedAccount = {
    ...account,
    data: {
      ...account.data,
      price: Number(plan.amount / 100).toFixed(2),
    },
    config: {
      ...account.config,
      billing: true,
    },
    billing_data,
    pagarme: {
      ...account.pagarme,
      implementation: implementation || account.pagarme.implementation || null,
      plan_id: plan.id,
      subscription,
      subscription_id: subscription.id,
      status: subscription.status,
      days: plan.days,
      parcelas,
      installments: plan.installments || null,
      payment_method: subscription.payment_method || null,
      recurrency,
    },
  }

  return updatedAccount
}

export const createAccountBilling = ({ pagarme, account, accountOwner }) => {
  return new Promise((resolve, reject) => {
    if (!account.ID || !account.owner) {
      return reject('Missing Account Data')
    }

    const accountId = account.ID
    const { affiliateId } = account

    return pagarmeApi
      .subscribe(pagarme, account, affiliateId)
      .then(async pagarmeResults => {
        console.log('pagarmeApi.subscribe > then > pagarmeResults', pagarmeResults)

        pagarmeResults = replaceUndefined(pagarmeResults)

        const updatedAccount = updateBillingInfo({ pagarme, pagarmeResults, account, accountOwner })
        const { paid, active } = await pagarmeApi.subscription.validate(updatedAccount)
        updatedAccount.active = active
        // console.log('saveNewAccount > updatedAccount',updatedAccount);

        FirestoreRef.collection(ACCOUNTS_COLLECTION_NAME)
          .doc(`${accountId}`)
          .update(updatedAccount)
          .then(doc => {
            return resolve({ account: updatedAccount, paid })
          })
      })
      .catch(function (error) {
        return reject(error)
      })
  })
}

export const updateAccountBilling = ({ pagarme, account, accountOwner }) => {
  return new Promise((resolve, reject) => {
    pagarmeApi
      .upgrade(pagarme, account)
      .then(async pagarmeResults => {
        pagarmeResults = replaceUndefined(pagarmeResults)

        const savedAccount = updateBillingInfo({ pagarme, pagarmeResults, account, accountOwner })

        const { subscriptionId } = pagarmeApi.subscription.validateSync(account)
        const { paid, active } = await pagarmeApi.subscription.validate(savedAccount)
        savedAccount.active = active
        // console.log('saveAccount > savedAccount',savedAccount);

        let updateFn
        let accountRef = FirestoreRef.collection(ACCOUNTS_COLLECTION_NAME).doc(`${account.ID}`)

        if (!subscriptionId || !account.active || paid) {
          updateFn = accountRef.update(savedAccount)
          accountRef
            .collection(UPDATES_SUBCOLLECTION_NAME)
            .get()
            .then(snap => {
              snap.docs.forEach(d => d.ref.delete())
            })
        } else {
          savedAccount.pagarme.previous_subscription_id = Number(account.pagarme.subscription_id)
          updateFn = accountRef.collection(UPDATES_SUBCOLLECTION_NAME).add(savedAccount)
        }

        return updateFn.then(doc => {
          return resolve({ account: paid ? savedAccount : account, savedAccount, paid, accountOwner })
        })
      })
      .catch(function (error) {
        return reject(error)
      })
  })
}

export const cancelAccountBilling = account => {
  return new Promise((resolve, reject) => {
    const { subscriptionId } = pagarmeApi.subscription.validateSync(account)

    if (!subscriptionId) {
      return reject()
    }

    pagarmeApi.subscription
      .cancel(subscriptionId, account)
      .then(async subscription => {
        const updateObj = {
          modified: moment().format(MOMENT_ISO),
          pagarme: {
            ...account.pagarme,
            subscription,
            subscription_id: subscription.id,
            status: subscription.status,
            payment_method: null,
            installments: null,
            plan_id: null,
            days: null,
            parcelas: null,
          },
        }

        const savedAccount = {
          ...account,
          ...updateObj,
        }

        const { active, currentSubscription } = await pagarmeApi.subscription.validate(savedAccount)
        if (!active && account.config.billing) {
          updateObj.active = false
          savedAccount.active = false
        }
        // console.log('saveAccount > savedAccount',savedAccount);

        let accountRef = FirestoreRef.collection(ACCOUNTS_COLLECTION_NAME).doc(`${account.ID}`)
        return accountRef.update(updateObj).then(doc => {
          return resolve({ account, savedAccount, subscription, currentSubscription })
        })
      })
      .catch(function (error) {
        return reject(error)
      })
  })
}

export const cancelAccountUpdates = account => {
  return new Promise(async (resolve, reject) => {
    const accountId = account.ID
    const updatesSnap = await FirestoreRef.collection(ACCOUNTS_COLLECTION_NAME).doc(`${accountId}`).collection(UPDATES_SUBCOLLECTION_NAME).get()
    const promises = []
    if (updatesSnap.size) {
      updatesSnap.forEach(doc => {
        let update = doc.data()
        if (((update.pagarme || {}).subscription || {}).id && update.pagarme.subscription.status !== 'canceled') {
          promises.push(
            pagarmeApi.subscription
              .cancel(update.pagarme.subscription.id, account)
              .then(r => doc.ref.delete())
              .catch(err => console.error(err))
          )
        } else {
          promises.push(doc.ref.delete())
        }
      })
    }
    Promise.all(promises)
      .then(r => resolve())
      .catch(err => {
        console.error(err)
        return reject()
      })
  })
}

/**
 * Redux Action Get Current Owner
 */
export const getAccountOwner = () => (dispatch, getState) => {
  dispatch({ type: GET_ACCOUNT_OWNER })

  const user = localJSON.get('user', false)
  const ownerId = user.owner

  return new Promise((resolve, reject) => {
    if (!ownerId) {
      dispatch({ type: GET_ACCOUNT_OWNER_FAILURE })
      return reject()
    }
    if (user.ID === ownerId) {
      dispatch({ type: GET_ACCOUNT_OWNER_SUCCESS, payload: user })
      return resolve(user)
    }
    FirestoreRef.collection(QIUSERS_COLLECTION_NAME)
      .doc(`${ownerId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          dispatch({ type: GET_ACCOUNT_OWNER_FAILURE })
          return reject({ message: langMessages[`${QIUSERS_COLLECTION_NAME}.inexistsMsg`], type: DOC_DOESNT_EXIST })
        }
        dispatch({ type: GET_ACCOUNT_OWNER_SUCCESS, payload: doc.data() })
        return resolve(doc.data())
      })
      .catch(error => {
        dispatch({ type: GET_ACCOUNT_OWNER_FAILURE })
        return reject()
      })
  })
}

export const fetchtAuthUser = data => dispatch => unconnectedFetchtAuthUser(data, dispatch)
export const unconnectedFetchtAuthUser = (data, dispatch) => {
  dispatch({ type: GET_AUTH_USER })
  return new Promise((resolve, reject) => {
    getAuthUser(data)
      .then(response => {
        // console.log('response',response);
        const result = response.data
        if (result.error) {
          const { error, code } = result
          // console.log('Error fetching user data:', error);
          dispatch({ type: GET_AUTH_USER_ERROR, error })
          return reject({ message: errorCodes[code] || error.message || langMessages['errors.dbErrorMsg'], error })
        } else if (result.authUser) {
          dispatch({ type: GET_AUTH_USER_SUCCESS, payload: result.authUser })
          return resolve(result.authUser)
        }
      })
      .catch(error => {
        // console.log('Error fetching user data:', error);
        dispatch({ type: GET_AUTH_USER_ERROR, error })
        return reject({ message: errorCodes[error.code] || error.message || langMessages['errors.dbErrorMsg'], error })
      })
  })
}

export const updateAccountOwner = account => {
  return {
    type: UPDATE_ACCOUNT_OWNER,
    payload: account,
  }
}

export const updateCurrentUser = user => {
  return {
    type: UPDATE_USER,
    payload: user,
  }
}

export const resetSuccessMsg = () => {
  return { type: RESET_SUCCESS_MSG }
}

export const resetErrorMsg = () => {
  return { type: RESET_ERROR_MSG }
}

export const resetFirestoreError = () => {
  return { type: RESET_FIRESTORE_ERROR }
}

/*
brand: "visa"
country: "BRAZIL"
customer: {object: "customer", id: 3030449, external_id: null, type: null, country: null, …}
date_created: "2020-05-28T20:39:34.928Z"
date_updated: "2020-05-28T20:39:35.494Z"
expiration_date: "1021"
fingerprint: "ck9wcui0o23ze0k09cy0egeqy"
first_digits: "498453"
holder_name: "MARCELO V A LIMA"
id: "card_ckar8qpe804iq7x6da1crcamf"
last_digits: "1771"
object: "card"
valid: true
*/
