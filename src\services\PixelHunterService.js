import COLLECTIONS from "Constants/AppCollections";
import { FirebaseRepository } from "FirebaseRef/repository";

export class PixelHunterService {
    collectionName = COLLECTIONS.HUNTERS_COLLECTION_NAME;

    constructor(pixelId) {
        this.pixelId = pixelId
        this.repository = new FirebaseRepository()
        this.path = `${this.collectionName}`
    }

    getHunter = (onSuccess, onError) => {
        return this.repository.getDoc(`${this.path}/${this.pixelId}`, onSuccess, onError)
    }

    getLogs = (dateRange, onSuccess, onError) => {
        const where = dateRange ? [
            ['createdAt', '>=', dateRange.start],
            ['createdAt', '<=', dateRange.end]
        ] : null

        return this.repository.getCollection(`${this.path}/${this.pixelId}/logs`, onSuccess, onError, '', '', where)
    }
}
