import { Box, Paper, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { TrendingDown, TrendingUp } from 'lucide-react';
import React from 'react';

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(3),
    height: '100%',
    transition: 'transform 0.2s ease-in-out',
    '&:hover': {
      transform: 'translateY(-4px)',
    },
  },
  icon: {
    color: theme.palette.primary.main,
    marginBottom: theme.spacing(2),
  },
  trendPositive: {
    color: theme.palette.success.main,
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(0.5),
  },
  trendNegative: {
    color: theme.palette.error.main,
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(0.5),
  },
  badge: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.background.paper,
    borderRadius: '50%',
    width: theme.spacing(3),
    height: theme.spacing(3),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: theme.typography.body2.fontSize,
  }
}));

const MetricCard = ({ title, value, icon, trend, count, countDescription, viewMore, viewMoreLabel, viewMoreChildren }) => {
  const classes = useStyles();
  return (
    <Paper className={classes.root}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h6">
          {value}
        </Typography>
        <Box className={classes.icon}>{icon}</Box>
      </Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        {count &&
          <Box sx={{ display: 'flex', justifyContent: 'start' }}>
            <Typography className={classes.badge} color="textSecondary" gutterBottom>
              {count}
            </Typography>
            {countDescription &&
              <Typography className='ml-2' color="textSecondary" gutterBottom>
                {countDescription}
              </Typography>
            }
          </Box>
        }

        <Typography color="textSecondary" gutterBottom>
          {title}
        </Typography>

        {viewMoreChildren && viewMoreChildren}
        {viewMore && (
          <Box className='btn btn-link text-qiplus'>
            <Typography color="textSecondary" gutterBottom onClick={viewMore}>
              {viewMoreLabel}
            </Typography>
          </Box>
        )}
      </Box>
      {trend && (
        <Box mt={1} className={trend > 0 ? classes.trendPositive : classes.trendNegative}>
          {trend > 0 ? <TrendingUp size={16} /> : <TrendingDown size={16} />}
          <Typography variant="body2">{trend}</Typography>
        </Box>
      )}
    </Paper>
  );
};

export default MetricCard;
