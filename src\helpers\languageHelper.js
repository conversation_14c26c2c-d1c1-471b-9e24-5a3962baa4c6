/**
 * Language Helper Functions
 */
import LanguageModules from '../constants/LanguageModules';

/**
 * Get the current language code from browser or localStorage
 * @returns {string} Language code (en, pt, es)
 */
export const getCurrentLanguage = () => {
  const userLocale = localStorage.getItem('config.locale') || navigator?.language?.split('-')?.[0] || 'en';
  return ['pt', 'en', 'es'].includes(userLocale) ? userLocale : 'en';
};

/**
 * Get translated module labels for the current language
 * @param {string} moduleKey - The module key
 * @param {string} property - The property to get (label, singular, shortname)
 * @param {string} defaultValue - Default value if translation not found
 * @returns {string} Translated label
 */
export const getModuleTranslation = (moduleKey, property = 'label', defaultValue = '') => {
  const lang = getCurrentLanguage();
  const langModules = LanguageModules[lang] || LanguageModules.en;
  
  if (langModules && langModules[moduleKey] && langModules[moduleKey][property]) {
    return langModules[moduleKey][property];
  }
  
  return defaultValue;
};
