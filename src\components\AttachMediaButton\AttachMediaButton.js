import { Dialog, DialogActions, IconButton, Tooltip } from '@material-ui/core'
import { AttachFileOutlined } from '@material-ui/icons'
import { ShotxDialogTitle } from 'Components/dialog/ShotxDialogTitle'
import { langMessages } from 'Lang/index'
import IntlMessages from 'Util/IntlMessages'
import React, { useState } from 'react'
import { Button } from 'reactstrap'
import { AttachMediaList } from './AttachMediaList'
import { AttachMediaPreview } from './AttachMediaPreview'


export const AttachMediaButton = ({
  onSubmit,
  height = '10vh',
  width = '20vw',
  buttonActiveColor = 'bg-qiplus text-white',
  options = [],
}) => {
  const [showDialog, setShowDialog] = useState(false)
  const [selectedFile, setSelectedFile] = useState(null)

  const onClose = () => {
    setSelectedFile(null)
    setShowDialog(false)
  }

  const onSelect = ({ file, option }) => {
    setSelectedFile({
      file,
      option,
    })
  }

  const submit = () => {
    onSubmit(selectedFile)
    setShowDialog(false)
    setSelectedFile(null)
  }

  const style = {
    style: {
      minHeight: height,
      // maxHeight: height,
      minWidth: width,
      // maxWidth: width,
    },
  }

  return (
    <>

      <Tooltip title={langMessages['shotx.chat.sendMedia']} placement="top">
        <IconButton>
          <AttachFileOutlined onClick={() => setShowDialog(true)} />
        </IconButton>
      </Tooltip>

      <Dialog
        onClose={onClose}
        aria-labelledby="attach-media-dialog"
        open={showDialog}
        // fullWidth
        maxWidth={'md'}
        scroll="paper"
        PaperProps={style}
      >
        <ShotxDialogTitle onClose={onClose} title={langMessages['shotx.chat.sendMedia']} />
        {
          selectedFile &&
          <AttachMediaPreview
            selectedFile={selectedFile}
            onSubmit={submit}
            onDiscard={() => setSelectedFile(null)}
          />
        }
        {
          !selectedFile && <AttachMediaList options={options} onSelect={onSelect} />
        }

        {selectedFile && <DialogActions>
          <Button
            variant="contained"
            onClick={() => setSelectedFile(null)}
            className={buttonActiveColor}
          >
            <IntlMessages id='shotx.chat.discard' />
          </Button>
          <Button
            variant="contained"
            onClick={submit}
            className={buttonActiveColor}
          >
            <IntlMessages id='shotx.chat.send' />
          </Button>
        </DialogActions>}
      </Dialog>
    </>

  )
}
