import { GridList } from 'Components/index'
import React, { useContext, useEffect, useState } from 'react'

// rct section loader
import { Snackbar } from '@material-ui/core'
import { Alert } from '@material-ui/lab'
import { ShotxDialog } from 'Components/dialog/ShotxDialog'
import { langMessages } from 'Lang/index'
import { withRouter } from 'react-router-dom'
import { ShotXInstanceCard } from './components/ShotXInstanceCard'
import { ModuleContext } from './contexts/ModuleContext'
import { connectInstance, createInstance, finish } from './steps'
const InstancePage = (({ history }) => {
  const {
    module,
    items,
    snipers,
    platforms,
    getPlatform,
    openDialog,
    setOpenDialog,
    dependences,

  } = useContext(ModuleContext)

  const defaultError = {
    error: null,
    message: null,
  }

  const [editing, setEditing] = useState(false)
  const [instanceSelected, setinstanceSelected] = useState(null)
  const [socket, setSocket] = useState(null)
  const [step, setStep] = useState(0)
  const [stepResult, setStepResult] = useState(null)
  const [stepError, setStepError] = useState(defaultError)
  const [title, setTitle] = useState(langMessages[`${module}.instance.create`])
  const [values, setValues] = useState({})
  const [canBack, setCanBack] = useState(false)
  const platform = values.platform
  const [connecting, setConnecting] = useState(false)
  const [disconnecting, setDisconnecting] = useState(false)
  const [initialInstance, setInitialInstance] = useState(null)
  const [snackBarMessage, setSnackBarMessage] = useState(null)
  const [currentPlatform, setCurrentPlatform] = useState(null)


  const create = createInstance({
    dependences: { platforms, ...dependences, snipers },
    values,
    onChange: setValues,
    onForward: () => {
      if (!platform) return

      startLoading(true)

      if (editing) {
        if (!platform.update) return
        const instance = values.instance

        if (instance !== instanceSelected) {
          platform
            .update(instance)
            .then(() => {
              setinstanceSelected(instance)
              setStepError({ error: false, message: langMessages[`${module}.instance.saveChangesSuccessfully`] })
              setStep(2)
              stopLoading()
              onDialogClose(2000)
            })
            .catch((error) => {
              console.log('🚀 - platform.update - error:', error)
              stopLoading()
            })

          return
        }
        stopLoading()
      } else {
        if (!platform.create) return
        platform
          .create({ values, dependences })
          .then((instanceDoc) => {
            setStepResult(instanceDoc)
            setStep(1)
          })
          .catch((error) => {
            console.log('🚀 - platform.create - error:', error)
            setStepError({ error: true, message: error.message })
            stopLoading()
          })
      }
    },
    editing,
    onClick: platform?.connect,
  })

  const connect = connectInstance({
    values,
    onLoad: () => handleInstanceConnection(stepResult),
  })

  const finishStep = finish({
    result: stepError,
    canBack,
    onBack: () => {
      setCanBack(false)
      setStepError(defaultError)
      setStep(1)
    },
    onLoad: () => {
      if (socket && socket.connected) {
        socket.disconnect()
      }
    },
    onForward: () => onDialogClose(),
  })

  const onDisconnecting = (eventData, socket) => {

    if (eventData.error) {
      setStepError({ error: true, message: eventData.message })
      setStep(2)
      stopLoading()

      if (socket && socket.connected) {
        socket.disconnect()
      }
      return
    }

    switch (eventData.state) {
      case 'close':
        setStepError({ error: false, message: langMessages[`${module}.instance.disconnectedSuccessfully`] })
        setStep(2)
        stopLoading()
        onDialogClose(2000)
        break
      default:
        break
    }
  }
  const onConnecting = (eventData, socket) => {
    const error = eventData?.error
    const code = eventData?.code
    const status = eventData?.state
    if (error) {
      setStepError({ error, message: error.message })
      setStep(2)
      stopLoading()

      if (socket && socket.connected) {
        socket.disconnect()
      }
      return
    }

    switch (status) {
      case 'open':
        setStepError({ error: false, message: langMessages['shotx.instance.connectedSuccessfully'] })
        setStep(2)
        stopLoading()
        onDialogClose(2000)
        break
      case 'refused':
        setCanBack(true)
        setConnecting(false)
        setStepError({ error: true, message: langMessages[`${module}.instance.refused`] })
        setStep(2)
        stopLoading()
        break
      default:
        setValues({ ...values, loading: false, qrCode: code })
        break
    }
  }

  const handleInstanceConnection = async (instance) => {
    setStepResult(instance)
    const platform = getPlatform(instance.platform)
    setCurrentPlatform(platform)
    const instanceStatus = instance?.status?.state
    const { id } = instance

    // pode ser chamado pelo botão (conectar) ou pelo botão (desconectar)
    if (step !== 1) {
      setStep(1)
    }
    startLoading()

    if (instanceStatus !== 'open') {
      setInitialInstance(instance)
      setOpenDialog(platform.showModalOnDisconnect)
      setTitle(langMessages[`${module}.instance.connecting`])
      setValues({ ...values, qrCode: null })

      if (connecting) return
      setConnecting(true)
      platform
        .connect(instance)
        .then((socket) => {
          if (platform.useSocket) {
            setSocket(socket)
            socket.on(id, (eventData) => {
              onConnecting(eventData, socket)
            })
          } else {
            onConnecting(socket, null)
          }
        })
        .catch((error) => {
          console.log('ERROR', error)
          setStepError({ error: true, message: error.message })
          stopLoading()
        })
    } else {
      const instanceOpen = {
        ...instance,
        status: {
          ...instance.status,
          state: 'open',
        },
      }
      setInitialInstance(instanceOpen)
      setOpenDialog(platform.showModalOnDisconnect)
      setTitle(langMessages[`${module}.instance.disconnecting`])
      if (disconnecting) return
      setDisconnecting(true)
      platform
        .disconnect(instance)
        .then((socket) => {
          if (platform.useSocket) {
            setSocket(socket)
            socket.on(id, (eventData) => {
              onDisconnecting(eventData, socket)
            })
          } else {
            onDisconnecting(socket, null)
          }
        })
        .catch((error) => {
          setStepError({ error: true, message: error.message })
          stopLoading()
        })
    }
  }

  const onDialogClose = (timeout) => {
    if (timeout) {
      setTimeout(() => {
        onDialogClose()
      }, timeout)

      return
    }

    setOpenDialog(false)

    // Delay limpar depois de fechado
    setTimeout(() => {
      setEditing(false)
      setinstanceSelected(null)
      setSocket(null)
      setStep(0)
      setStepResult(null)
      setTitle(langMessages[`${module}.instance.create`])
      stopLoading()
      setValues({})
      setStepError(defaultError)
      setConnecting(false)
      setDisconnecting(false)
    }, 500)

    if (socket) {
      socket.disconnect()
    }
  }

  const edit = (instance) => {
    setEditing(true)
    setinstanceSelected(instance)
    const platform = getPlatform(instance.platform)

    setValues({ ...values, instance, platform })
    setStep(0)
    setOpenDialog(true)
  }

  const interaction = (instance) => {
    setinstanceSelected(instance)
    history.push(`/shotx/interactions/${instance.id}`)
  }

  // Getters and Setters
  const steps = [create, connect, finishStep]
  const startLoading = () => setValues({ ...values, loading: true })
  const stopLoading = () => setValues({ ...values, loading: false })
  const currentStep = steps[step]



  const isFirst = step === 0
  const isLast = step === steps.length - 1

  // Atualiza o titulo apos editar
  useEffect(() => {
    if (editing) {
      setTitle(langMessages[`${module}.instance.edit`].replace('[%instance]', instanceSelected.title))
      return
    }
  }, [instanceSelected])



  useEffect(() => {
    const currentInstance = items.find(instance => instance?.id === initialInstance?.id)

    if (currentPlatform?.messages && currentInstance?.status?.state !== initialInstance?.status?.state) {
      switch (currentInstance?.status?.state) {
        case 'open':
          setSnackBarMessage({
            message: langMessages[currentPlatform?.messages?.onConnect],
            severity: 'success',
          })
          break
        case 'refused':
          setSnackBarMessage({
            message: langMessages[currentPlatform?.messages?.onRefused],
            severity: 'error',
          })
          break
        case 'close':
          setSnackBarMessage({
            message: langMessages[currentPlatform?.messages?.onDisconnect],
            severity: 'warning',
          })
          break
        default:
          setSnackBarMessage(null)
          break
      }
    }
  }, [items])
  // Atualiza o titulo quando esta editando
  useEffect(() => {
    if (editing) {
      setTitle(langMessages[`${module}.instance.edit`].replace('[%instance]', instanceSelected.title))
      return
    }

    setTitle(langMessages[`${module}.instance.create`])
  }, [editing])

  useEffect(() => {
    if (currentStep && currentStep.onLoad && stepResult) currentStep.onLoad()
  }, [step])

  return (
    <>
      <div>
        <Snackbar
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          open={!!snackBarMessage}
          onClose={() => setSnackBarMessage(null)}
          autoHideDuration={5000}
        >
          <Alert onClose={() => setSnackBarMessage(null)} severity={snackBarMessage?.severity}>
            {snackBarMessage?.message}
          </Alert>
        </Snackbar>
      </div>

      <GridList
        items={items}
        collectionName={module}
        addPostAction={e => setOpenDialog(true)}
        build={(instance) => {
          return <ShotXInstanceCard
            platform={getPlatform(instance.platform)}
            collection={module}
            instance={instance}
            handleConnection={handleInstanceConnection}
            onEdit={edit.bind(this, instance)}
            onInteraction={interaction.bind(this, instance)}
          // onDelete={onDeleteClick.bind(this, instance)}
          />
        }}
      />
      <ShotxDialog
        open={openDialog}
        title={title}
        content={currentStep.content}
        isLoading={values.loading}
        onBack={canBack ? currentStep.onBack : null}
        disablePrevious={isFirst}
        onNext={currentStep.onForward}
        disableNext={currentStep.disableNext}
        onClose={onDialogClose}
        nextText={currentStep.buttonText}
        backText={currentStep.backText}
      />

    </>
  )
})

export const Instance = withRouter(InstancePage)
