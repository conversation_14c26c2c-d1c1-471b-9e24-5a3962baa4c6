/**
 * Email App Actions
 */

import FirebaseRef, { FirestoreRef } from '../firebase'

import moment from 'moment'

import { langMessages } from '../lang'

import {
  GAPI_CLIENT_READY,
  GAPI_CLIENT_ERROR,
  GAPI_AUTH2_READY,
  ON_GMAIL_AUTH_SUCCESS,
  ON_GMAIL_AUTH_ERROR,
  ON_GMAIL_SIGN_OUT,
  LIST_GMAIL_FOLDER_SUCCESS,
  ON_EMAIL_MOVE_TO_FOLDER,
  ON_EMAIL_MOVE_TO_FOLDER_SUCCESS,
  ON_EMAIL_MOVE_TO_FOLDER_FAILURE,
  GET_MAILBOX_LABELS,
  GET_MAILBOX_LABELS_SUCCESS,
  GET_MAILBOX_LABELS_FAILURE,
  SAVE_GMAIL_DRAFT,
  SAVE_GMAIL_DRAFT_SUCCESS,
  SAVE_GMAIL_DRAFT_FAILURE,
  GET_EMAIL,
  GET_EMAIL_SUCCESS,
  GET_EMAIL_FAILURE,
  ON_TRASH_EMAIL,
  ON_TRASH_EMAIL_SUCCESS,
  ON_TRASH_EMAIL_FAILURE,
  ON_DELETE_MAIL,
  ON_DELETE_MAIL_SUCCESS,
  ON_DELETE_MAIL_FAILURE,
  SET_EMAIL_AS_STAR,
  SEARCH_EMAIL,
  ON_SEND_EMAIL,
  ON_SEND_EMAIL_SUCCESS,
  EMAIL_SENT_SUCCESSFULLY,
  ON_SEND_EMAIL_FAILURE,
  GET_FOLDER_EMAILS,
  GET_FOLDER_EMAILS_SUCCESS,
  GET_FOLDER_EMAILS_FAILURE,
  ADD_LABELS_INTO_EMAILS,
} from 'Actions/types'

import { insertScripts, b64DecodeUnicode, encodeMessage } from 'Helpers/helpers'
import { validateEmail, extractEmailAddress } from 'Helpers/formHelpers'

import {
  GOOGLE_APIS_CLIENT_SRC,
  GOOGLE_APIS_PLATFORM_SRC,
  GMAIL_APP_DISCOVERY_DOCS,
  GMAIL_APP_SCOPES,
  GOOGLE_APIS_CLIENT_ID,
} from 'Constants/AppConstants'

import { MOMENT_ISO } from 'Constants'

// folders
import folders from 'Routes/mail/model/folders'
import emailModel from 'Routes/mail/model/email'

import COLLECTIONS from '../constants/AppCollections'

/**
 * Redux Action To Mark As Star Email
 */
export const gmailMarkAsStarEmail = email => ({
  type: SET_EMAIL_AS_STAR,
  payload: email,
})

/**
 * Redux Action To Search Email
 */
export const gmailSearchEmail = searchText => ({
  type: SEARCH_EMAIL,
  payload: searchText,
})

/**
 * Redux Action To Add Labels Into Email
 */
export const gmailAddLabelsIntoEmail = label => ({
  type: ADD_LABELS_INTO_EMAILS,
  payload: label,
})

let gapi, gapiLoaded, gapiLoadTimeout

export const initGapiScripts = () => (dispatch, getState) => {
  return new Promise((res, rej) => {
    let { gapi } = window

    if (gapi && gapi.auth2 && (gapi.client || {}).gmail) {
      return res(gapi)
    }

    if (gapiLoaded === true) {
      let intVal = setInterval(() => {
        if (gapi && gapi.auth2 && (gapi.client || {}).gmail) {
          clearInterval(intVal)
          res(gapi)
        }
      }, 500)
      return null
    }

    gapiLoaded = true

    clearTimeout(gapiLoadTimeout)
    gapiLoadTimeout = setTimeout(() => {
      insertScripts([GOOGLE_APIS_CLIENT_SRC, GOOGLE_APIS_PLATFORM_SRC], () => {
        // console.log('window.gapi',window.gapi);

        gapi = window.gapi

        gapi.load('client:auth2', function () {
          gapi.client
            .init({
              apiKey: FirebaseRef.config.apiKey,
              // clientId and scope are optional if auth is not required.
              clientId: GOOGLE_APIS_CLIENT_ID,
              discoveryDocs: GMAIL_APP_DISCOVERY_DOCS,
              scope: GMAIL_APP_SCOPES,
            })
            .then(
              function () {
                dispatch({ type: GAPI_CLIENT_READY, payload: gapi.client })
                return res(gapi)
              },
              function (err) {
                dispatch({ type: GAPI_CLIENT_ERROR, error: err.error })
              }
            )

          // Retrieve the singleton for the GoogleAuth library and set up the client.
          let googleAuth = gapi.auth2.init({
            client_id: GOOGLE_APIS_CLIENT_ID,
            cookiepolicy: 'single_host_origin',
            discoveryDocs: GMAIL_APP_DISCOVERY_DOCS,
            scope: GMAIL_APP_SCOPES,
          })

          window.auth2 = googleAuth

          gapi.auth2.getAuthInstance().isSignedIn.listen(signinStatus => {
            console.log('signinStatus 2', signinStatus)
            if (signinStatus) signinGoogleUser()(dispatch)
          })

          // googleAuth.then(()=>{
          //     let signinStatus = gapi.auth2.getAuthInstance().isSignedIn.get();
          //     console.log('signinStatus',signinStatus);
          //     if ( signinStatus ) signinGoogleUser()(dispatch)
          // })

          dispatch({ type: GAPI_AUTH2_READY, authInstance: googleAuth })
        })
      })
    }, 500)
  })
}

const getUserFromGoogleProfile = profile => {
  let user = {}
  if (profile && profile.getEmail) {
    user = {
      id: profile.getId(),
      email: profile.getEmail(),
      firstName: profile.getGivenName(),
      lastName: profile.getFamilyName(),
      displayName: profile.getName(),
      name_format: '',
      picture: profile.getImageUrl(),
      short_name: profile.getGivenName(),
      fromName: profile.getName(),
      from: profile.getEmail(),
    }
  }
  // console.log('user', user);
  return user
}

export const signinGoogleUser = () => (dispatch, getState) => {
  // console.log('signinGoogleUser >>> ')

  initGapiScripts()(dispatch).then(gapi => {
    let signinStatus = gapi.auth2.getAuthInstance().isSignedIn
    let profile = signinStatus && window.auth2.currentUser.get().getBasicProfile()
    let googleUser = getUserFromGoogleProfile(profile)

    // console.log('authUser',window.auth2.currentUser);
    // console.log('profile',profile);
    // console.log('googleUser',googleUser);

    dispatch({ type: ON_GMAIL_AUTH_SUCCESS, payload: googleUser })
  })
}

export const signoutGoogleUser = () => (dispatch, getState) => {
  // console.log('signoutGoogleUser >>> ')

  initGapiScripts()(dispatch).then(gapi => {
    let signinStatus = gapi.auth2.getAuthInstance().isSignedIn
    if (signinStatus) gapi.auth2.getAuthInstance().signOut()

    dispatch({ type: ON_GMAIL_SIGN_OUT })
  })
}

export const attachGmailSignin = element => (dispatch, getState) => {
  initGapiScripts()(dispatch).then(gapi => {
    if (element && (window.auth2 || {}).attachClickHandler) {
      window.auth2.attachClickHandler(
        element,
        {},
        function (authUser) {
          signinGoogleUser()(dispatch)
        },
        function (err) {
          console.error(err)
          dispatch({ type: ON_GMAIL_AUTH_ERROR, error: err.error })
        }
      )
    }
  })
}

export const getGmailAttachments = (userId, message) => (dispatch, getState) => {
  return new Promise((res, rej) => {
    let parts = (message.payload || {}).parts || []
    let attachmentsParts = parts.filter(p => p.filename && p.filename.length > 0)
    let attachments = []

    if (!attachmentsParts.length) {
      return res([])
    }

    initGapiScripts()(dispatch).then(gapi => {
      attachmentsParts.forEach((part, attIndex) => {
        if (part.filename && part.filename.length > 0) {
          let messageId = message.id
          let attachId = part.body.attachmentId

          let request = gapi.client.gmail.users.messages.attachments.get({
            userId,
            messageId,
            id: attachId,
          })

          request.execute(attachment => {
            let ik_value = ''
            let attachment_index = attIndex + 1
            let attachment_url = `https://mail.google.com/mail/u/${attachment_index}/?ui=2&ik=${ik_value}&view=att&th=${messageId}&attid=0.${attachment_index}&disp=safe&zw`
            let preview_url = `https://mail.google.com/mail/u/${attachment_index}/?ui=2&ik=${ik_value}&view=att&th=${messageId}&attid=0.${attachment_index}&disp=inline&zw`

            attachments.push({
              attachment_url,
              preview_url,
              attachment_id: attachId,
              name: part.filename,
              mimeType: part.mimeType,
              attachment,
            })

            console.log('attachments', attachments)
            if (attachments.length === attachmentsParts.length) {
              res(attachments)
            }
          })
        }
      })
    })
  })
}

export const getGmailMessage = (mailboxId, folder, userId, messageId, useStore) => (dispatch, getState) => {
  useStore = useStore !== false

  useStore && dispatch({ type: GET_EMAIL, folder })

  return new Promise((res, rej) => {
    initGapiScripts()(dispatch).then(gapi => {
      let targetApi = folder === 'draft' ? 'drafts' : 'messages'

      let request = gapi.client.gmail.users[targetApi].get({
        userId: userId,
        id: messageId,
      })

      request.execute(
        response => {
          console.log('getGmailMessage > response', response)

          let message = response.message || response.result

          if ((message || {}).payload) {
            let email, html
            let { id, labelIds, internalDate } = message
            let { headers, body, parts } = message.payload
            let attachmentsParts = (parts || []).filter(p => p.filename && p.filename.length > 0)

            if (Array.isArray(parts) && parts[0].parts) {
              let part = parts[0].parts.find(p => p.mimeType === 'text/html')
              let encodedMsg = ((part || {}).body || {}).data
              if (encodedMsg) html = b64DecodeUnicode(encodedMsg)
            }
            if (!html && Array.isArray(parts)) {
              let part = parts.find(p => p.mimeType === 'text/html')
              let encodedMsg = ((part || {}).body || {}).data
              if (encodedMsg) html = b64DecodeUnicode(encodedMsg)
            }
            if (!html && (body || {}).data) {
              let encodedMsg = body.data
              if (encodedMsg) html = b64DecodeUnicode(encodedMsg)
            }

            if (html && html.match(/<body(.*)>/)) {
              let match = (html.match(/<body(.*)>/) || [])[0]
              let content = html.split(match)[1].split('</body>')[0]
              html = `${match.replace('body', 'div')}${content}${'</div>'}`
            } else if (html && html.indexOf('<body') !== -1) {
              let tempHtml = html.split('<body')[1].split('</body>')[0]
              html = tempHtml.replace(tempHtml.split('>')[0] + '>', '')
            }

            if (!html) html = ''

            email = {
              ...emailModel,
              html,
              id,
              ID: id,
              mailId: id,
              resourceId: response.id,
              date: moment(Number(internalDate)).format(MOMENT_ISO),
              modified: moment(Number(internalDate)).format(MOMENT_ISO),
              createdAt: Number(internalDate),
              updatedAt: Number(internalDate),
              labels: labelIds,
              subject: (headers.find(h => h.name === 'Subject') || {}).value || '',
              to: (headers.find(h => h.name === 'To') || {}).value || '',
              fromName: ((headers.find(h => h.name === 'From') || {}).value || '').split('<')[0].replace(/"/g, ''),
              from: (
                ((headers.find(h => h.name === 'From') || {}).value || '').split('<')[1] ||
                (headers.find(h => h.name === 'From') || {}).value ||
                ''
              ).split('>')[0],
            }

            if (attachmentsParts.length) {
              getGmailAttachments(
                userId,
                message
              )(dispatch).then(attachments => {
                email.attachments = attachments
                useStore && dispatch({ type: GET_EMAIL_SUCCESS, payload: email, folder, mailboxId })
                return res({ email, response })
              })
            } else {
              useStore && dispatch({ type: GET_EMAIL_SUCCESS, payload: email, folder, mailboxId })
              return res({ email, response })
            }
          }
        },
        err => {
          console.error('error', err)
          useStore && dispatch({ type: GET_EMAIL_FAILURE, ID: messageId, folder })
          return rej(err.error)
        }
      )

      // gapi.client.request({
      //     'path': `https://www.googleapis.com/gmail/v1/users/${userId}/messages/${messageId}`,
      // })
      // .then(response=>{
      //     // console.log('response',response);
      // })
      // .catch(err=>{
      //     console.error('error',err);
      //     useStore && dispatch({ type: GET_EMAIL_FAILURE, ID: messageId, folder });
      //     return rej(err.error);
      // });
    })
  })
}

export const fetchGmailMessages = (mailboxId, folder, userId, query) => (dispatch, getState) => {
  dispatch({ type: GET_FOLDER_EMAILS, folder, query })

  return new Promise((res, rej) => {
    initGapiScripts()(dispatch).then(gapi => {
      // return gapi.client.request({
      //     'path': `https://www.googleapis.com/gmail/v1/users/${userId}/messages`,
      // })
      // .then(response=>{
      //     // console.log('response',response);
      //     if ((response.result||{}).messages) {
      //     }
      // })
      // .catch(error=>{
      //     console.error('error',error);
      //     dispatch({ type: GET_FOLDER_EMAILS_FAILURE, folder });
      // });

      let targetApi = folder === 'draft' ? 'drafts' : 'messages'

      const getPageOfMessages = (request, messages) => {
        request.execute(function (response) {
          console.log('getPageOfMessages > response', response)
          // console.log('getPageOfMessages > messages',messages);

          // dispatch({ type: LIST_GMAIL_FOLDER_SUCCESS, payload: response, folder, mailboxId })

          if (!response[targetApi] && !messages.length) {
            dispatch({ type: GET_FOLDER_EMAILS_SUCCESS, payload: [], folder, mailboxId })
            return res([])
          }

          response[targetApi] && (messages = messages.concat(response[targetApi]))

          let promises = messages.filter((m, k) => k < 20).map(msg => getGmailMessage(mailboxId, folder, userId, msg.id, false)(dispatch))

          Promise.all(promises)
            .then(results => {
              // console.log('results',results);
              let emails = results.map(r => r.email)
              dispatch({ type: GET_FOLDER_EMAILS_SUCCESS, payload: emails, folder, mailboxId })
              return res(emails)
            })
            .catch(err => {
              dispatch({ type: GET_FOLDER_EMAILS_FAILURE, folder })
              return rej('Erro na integração com gmail')
            })

          // let nextPageToken = response.nextPageToken;
          // if (nextPageToken) {
          //     request = gapi.client.gmail.users[targetApi].list({
          //         userId,
          //         'pageToken': nextPageToken,
          //         'q': query
          //     });
          //     getPageOfMessages(request, messages);
          // } else {
          //     let emails = messages;
          //     dispatch({ type: GET_FOLDER_EMAILS_SUCCESS, payload: emails, folder, mailboxId })
          // }
        })
      }

      let initialRequest
      let queryStr = query || `in:${folder}`
      if (folder === 'draft') {
        initialRequest = gapi.client.gmail.users.drafts.list({
          userId,
          q: queryStr,
        })
      } else {
        initialRequest = gapi.client.gmail.users.messages.list({
          userId,
          q: queryStr,
        })
      }

      // dispatch({ type: GET_FOLDER_EMAILS_FAILURE, folder });
      getPageOfMessages(initialRequest, [])
    })
  })
}

export const saveGmailDraft = (mailboxId, data, userId) => (dispatch, getState) => {
  console.log('saveGmailDraft > data', data)

  dispatch({ type: SAVE_GMAIL_DRAFT })

  return new Promise((resolve, reject) => {
    const { id, resourceId, from, fromName, subject, html, to, cc, bcc } = data
    const isNewDraft = !resourceId

    let onError = err => {
      const message = err.message || err.error || err
      dispatch({ type: SAVE_GMAIL_DRAFT_FAILURE, error: message })
      reject({ error: langMessages[message] || message })
    }

    try {
      initGapiScripts()(dispatch).then(gapi => {
        let fromMkp = `${fromName} <${extractEmailAddress(from)}>`

        let message =
          'Content-Type: text/html; charset="UTF-8"\r\n' +
          'MIME-Version: 1.0\r\n' +
          'Content-Transfer-Encoding: 7bit\r\n' +
          'Subject: ' +
          subject +
          '\r\n' +
          'From: ' +
          fromMkp +
          '\r\n'

        if (bcc) message += 'Bcc: ' + bcc + '\r\n'
        if (cc) message += 'Cc: ' + cc + '\r\n'

        message += 'To: ' + to + '\r\n\r\n'

        let encodedMessage = encodeMessage(message + html)

        let request

        if (isNewDraft) {
          request = gapi.client.gmail.users.drafts.create({
            userId,
            resource: {
              message: {
                raw: encodedMessage,
              },
            },
          })
        } else {
          request = gapi.client.gmail.users.drafts.update({
            userId,
            id: resourceId,
            resource: {
              id: resourceId,
              message: {
                raw: encodedMessage,
              },
            },
            message: {
              raw: encodedMessage,
            },
            send: false,
          })
        }

        // console.log('request',request);

        request.then(response => {
          console.log('response', response)

          if (response.result && response.status === 200) {
            getGmailMessage(
              mailboxId,
              'draft',
              userId,
              response.result.id,
              false
            )(dispatch).then(draftMsg => {
              dispatch({ type: SAVE_GMAIL_DRAFT_SUCCESS, payload: draftMsg.email, folder: 'draft', prevId: id })
              return resolve(draftMsg.email)
            })
          } else {
            onError(response.error)
          }
        })
      })
    } catch (error) {
      onError(error)
    }
  })
}

export const sendGmailMessage = (mailboxId, data, userId) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    let rejected = false
    let requiredFields = ['fromName', 'subject', 'html', 'to']

    requiredFields.forEach(field => {
      if (!rejected && !data[field]) {
        rejected = true
        reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages[`email.fields.${field}`]) })
      }
    })

    const { from, fromName, subject, html, to, cc, bcc } = data

    console.log('sendGmailMessage > data', data)

    to.split(',').forEach(e => {
      if (!validateEmail(extractEmailAddress(e))) {
        rejected = true
        return reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages['email.fields.to']) })
      }
    })

    if (rejected) return

    dispatch({ type: ON_SEND_EMAIL })

    let onError = err => {
      const message = err.message || err.error || err
      dispatch({ type: ON_SEND_EMAIL_FAILURE, error: message })
      reject({ error: langMessages[message] || message })
    }

    try {
      initGapiScripts()(dispatch).then(gapi => {
        let fromMkp = `${fromName} <${extractEmailAddress(from)}>`
        let message =
          'Content-Type: text/html; charset="UTF-8"\r\n' +
          'MIME-Version: 1.0\r\n' +
          'Content-Transfer-Encoding: 7bit\r\n' +
          'Subject: ' +
          subject +
          '\r\n' +
          'From: ' +
          fromMkp +
          '\r\n'

        if (bcc) message += 'Bcc: ' + bcc + '\r\n'
        if (cc) message += 'Cc: ' + cc + '\r\n'

        message += 'To: ' + to + '\r\n\r\n'

        let encodedMessage = encodeMessage(message + html)

        let request = gapi.client.gmail.users.messages.send({
          userId,
          resource: {
            raw: encodedMessage,
          },
        })

        request.execute(result => {
          // console.log('result',result);

          if (result && result.id) {
            dispatch({ type: ON_SEND_EMAIL_SUCCESS })
            dispatch({ type: EMAIL_SENT_SUCCESSFULLY })

            resolve(result)
          } else {
            onError(result.error)
          }
        })
      })
    } catch (error) {
      onError(error)
    }
  })
}

export const sendGmailDraft = (mailboxId, data, userId) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    let rejected = false
    let requiredFields = ['fromName', 'subject', 'html', 'to']

    requiredFields.forEach(field => {
      if (!rejected && !data[field]) {
        rejected = true
        reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages[`email.fields.${field}`]) })
      }
    })

    const { id, resourceId, from, fromName, subject, html, to, cc, bcc } = data
    const isDraft = !!resourceId && resourceId !== id

    console.log('sendGmailDraft > data', data)

    to.split(',').forEach(e => {
      if (!validateEmail(extractEmailAddress(e))) {
        rejected = true
        return reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages['email.fields.to']) })
      }
    })

    if (rejected) return

    getGmailMessage(
      mailboxId,
      'draft',
      userId,
      resourceId,
      false
    )(dispatch).then(draftMsg => {
      console.log('draftMsg', draftMsg)

      dispatch({ type: ON_SEND_EMAIL })

      let onError = err => {
        const message = err.message || err.error || err
        dispatch({ type: ON_SEND_EMAIL_FAILURE, error: message })
        reject({ error: langMessages[message] || message })
      }

      try {
        initGapiScripts()(dispatch).then(gapi => {
          let fromMkp = `${fromName} <${extractEmailAddress(from)}>`
          let message =
            'Content-Type: text/html; charset="UTF-8"\r\n' +
            'MIME-Version: 1.0\r\n' +
            'Content-Transfer-Encoding: 7bit\r\n' +
            'Subject: ' +
            subject +
            '\r\n' +
            'From: ' +
            fromMkp +
            '\r\n'

          if (bcc) message += 'Bcc: ' + bcc + '\r\n'
          if (cc) message += 'Cc: ' + cc + '\r\n'

          message += 'To: ' + to + '\r\n\r\n'

          let encodedMessage = encodeMessage(message + html)

          let request

          if (isDraft) {
            request = gapi.client.gmail.users.drafts.send({
              userId,
              id: resourceId,
              resource: {
                id: resourceId,
                message: {
                  raw: encodedMessage,
                },
              },
              message: {
                raw: encodedMessage,
              },
            })
          } else {
            request = gapi.client.gmail.users.messages.send({
              userId,
              resource: {
                raw: encodedMessage,
              },
            })
          }

          // console.log('request',request);

          request.then(result => {
            // console.log('result',result);

            // if (result && result.id) {
            if (result.status === 200) {
              dispatch({ type: ON_SEND_EMAIL_SUCCESS })
              dispatch({ type: EMAIL_SENT_SUCCESSFULLY })

              resolve(result)

              if (!isDraft) onDeleteGmailMessage(mailboxId, 'draft', draftMsg.email, userId, false)(dispatch)
            } else {
              onError(result.error)
            }
          })
        })
      } catch (error) {
        onError(error)
      }
    })
  })
}

export const onTrashGmailMessage = (mailboxId, folder, data, userId) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    dispatch({ type: ON_TRASH_EMAIL, folder, mailboxId })

    let { mailId } = data

    if (!mailId) {
      return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: trash 01` })
    }

    initGapiScripts()(dispatch).then(gapi => {
      let request = gapi.client.gmail.users.messages.trash({
        userId,
        id: mailId,
      })

      request.then(function (resp) {
        console.log('response', resp)

        if (resp.status === 200) {
          dispatch({ type: ON_TRASH_EMAIL_SUCCESS, folder, mailboxId, mailId })
          return resolve({ mailId })
        }

        dispatch({ type: ON_TRASH_EMAIL_FAILURE, folder, mailboxId, mailId, error: resp.error })
        return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: trash 02` })
      })
    })
  })
}

export const onDeleteGmailMessage = (mailboxId, folder, data, userId, useStore) => (dispatch, getState) => {
  useStore = useStore !== false

  return new Promise((resolve, reject) => {
    useStore && dispatch({ type: ON_DELETE_MAIL, folder, mailboxId })

    console.log('onDeleteGmailMessage > data', data)

    let { mailId } = data

    if (!mailId) {
      return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: Message deletion 01` })
    }

    let request = gapi.client.gmail.users.messages.delete({
      userId,
      id: mailId,
    })

    request.execute(function (resp) {
      console.log('onDeleteGmailMessage > response', resp)

      if (resp.code === 200) {
        useStore && dispatch({ type: ON_DELETE_MAIL_SUCCESS, folder, mailboxId, mailId })
        return resolve({ mailId })
      }

      useStore && dispatch({ type: ON_DELETE_MAIL_FAILURE, folder, mailboxId, mailId, error: resp.error })
      return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: Message deletion 02` })
    })
  })
}

export const moveGmailMessage = (mailboxId, fromFolder, toFolder, userId, mailId) => (dispatch, getState) => {
  dispatch({ type: GET_MAILBOX_LABELS, mailboxId })

  return new Promise((resolve, reject) => {
    initGapiScripts()(dispatch).then(gapi => {
      let signinStatus = gapi.auth2.getAuthInstance().isSignedIn.get()

      if (!signinStatus) {
        dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER_FAILURE, mailboxId })
        return reject('Usuaário não autenticado no gmail')
      }

      dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER, fromFolder, toFolder, mailboxId })

      var request = gapi.client.gmail.users.messages.modify({
        userId,
        id: mailId,
        addLabelIds: [fromFolder.toUpperCase()],
        removeLabelIds: [toFolder.toUpperCase()],
      })

      request.then(response => {
        if (response.status === 200) {
          dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER_SUCCESS, fromFolder, toFolder, mailboxId, mailId })
          return resolve({ mailId })
        }

        dispatch({
          type: ON_EMAIL_MOVE_TO_FOLDER_FAILURE,
          fromFolder,
          toFolder,
          mailboxId,
          mailId,
          error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: move 02`,
        })
        return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: move 02` })
      })
    })
  })
}

export const fetchGmailLabels = (mailboxId, userId) => (dispatch, getState) => {
  dispatch({ type: GET_MAILBOX_LABELS, mailboxId })

  return new Promise((res, rej) => {
    initGapiScripts()(dispatch).then(gapi => {
      let signinStatus = gapi.auth2.getAuthInstance().isSignedIn.get()

      if (!signinStatus) {
        dispatch({ type: GET_MAILBOX_LABELS_FAILURE, mailboxId })
        return rej('Usuaário não autenticado no gmail')
      }

      let request = gapi.client.gmail.users.labels.list({
        userId,
      })

      request.execute(
        resp => {
          // console.log('resp',resp);
          let labels = (resp.labels || [])
            .filter(label => label.id && label.id.indexOf('CATEGORY_') !== 0 && !folders.find(f => f.id.toLowerCase() === label.id.toLowerCase()))
            .map(l => ({
              ...l,
              title: `mailboxes.labels.${l.name}` in langMessages ? langMessages[`mailboxes.labels.${l.name}`] : l.name,
            }))

          dispatch({ type: GET_MAILBOX_LABELS_SUCCESS, payload: labels, mailboxId })

          FirestoreRef.collection(COLLECTIONS.MAILBOXES_COLLECTION_NAME).doc(mailboxId).update({ labels })

          return res(labels)
        },
        err => {
          console.error('error', err)
          dispatch({ type: GET_MAILBOX_LABELS_FAILURE, mailboxId })
          return rej(err.error)
        }
      )
    })
  })
}

export const gapiRequest = (uid, messageId) => (dispatch, getState) => {
  // 3. Initialize and make the API request.
  initGapiScripts()(dispatch).then(gapi => {
    return gapi.client
      .request({
        // 'path': `https://www.googleapis.com/gmail/v1/users/${uid}/messages`,
        path: `https://www.googleapis.com/gmail/v1/users/${uid}/drafts/${messageId}`,
      })
      .then(response => {
        console.log('response', response)
      })
      .catch(error => {
        console.error('error', error)
      })
  })
}
