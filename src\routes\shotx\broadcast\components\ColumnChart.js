import { Card, CardContent, Grid, makeStyles } from "@material-ui/core";
import GoogleChart from 'Components/Widgets/components/GoogleChart';
import React from 'react';
export default function ColumnChart({ chartData, optionsChart }) {

    const useStyles = makeStyles((theme) => ({
        card: {
            width: "100%",
        },
        chartContainer: {
            width: "100%",
            height: "100%",
        },
    }));

    const classes = useStyles();

    return (
        <Card className={classes.card}>
            <CardContent>
                <Grid container alignItems="center" justifyContent="center">
                    <Grid item xs={12} md={12}>
                        <div className={classes.chartContainer}>
                            <GoogleChart
                                campaigns={chartData}
                                darkMode={false}
                                data={chartData}
                                options={optionsChart}
                                chartType='ColumnChart'
                            />
                        </div>
                    </Grid>
                </Grid>
            </CardContent>
        </Card>
    )
}
