/**
 * Forms Actions
 */
import { FirestoreRef } from 'FirebaseRef'

import { langMessages } from '../lang'

import moment from 'moment'

import {
  GET_CUSTOM_FIELDS,
  GET_CUSTOM_FIELDS_SUCCESS,
  GET_CUSTOM_FIELDS_FAILURE,
  GET_CUSTOM_FIELD,
  GET_CUSTOM_FIELD_SUCCESS,
  GET_CUSTOM_FIELD_FAILURE,
  ADD_CUSTOM_FIELD,
  ADD_CUSTOM_FIELD_SUCCESS,
  ADD_CUSTOM_FIELD_FAILURE,
  DELETE_CUSTOM_FIELD,
  UPDATE_CUSTOM_FIELD,
  UPDATE_CUSTOM_FIELDS,
} from 'Actions/types'

import { CUSTOM_FIELDS_GROUP, MOMENT_ISO, DOC_DOESNT_EXIST } from '../constants/AppConstants'

import { FORMS_COLLECTION_NAME, FIELDS_COLLECTION_NAME } from '../constants/AppCollections'

import { sessionJSON, localJSON } from 'Helpers/helpers'

const collectionName = FORMS_COLLECTION_NAME
const fieldsCollection = FIELDS_COLLECTION_NAME

/**
 * Redux Action Get Custom Fields
 */
export const getCustomFields = (accountId, queries) => (dispatch, getState) => {
  dispatch({ type: GET_CUSTOM_FIELDS })
  return new Promise((resolve, reject) => {
    let QueryRef = FirestoreRef.collection(fieldsCollection).where('accountId', '==', `${accountId}`)
    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))
    QueryRef.get()
      .then(snapshot => {
        const posts = []
        snapshot.forEach(doc => {
          posts.push(doc.data())
        })
        dispatch({ type: GET_CUSTOM_FIELDS_SUCCESS, payload: posts })
        return resolve(posts)
      })
      .catch(function (error) {
        dispatch({ type: GET_CUSTOM_FIELDS_FAILURE })
        return reject()
      })
  })
}

/**
 * Redux Action Get Custom Field
 */
export const getCustomField = fieldId => (dispatch, getState) => {
  dispatch({ type: GET_CUSTOM_FIELD })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(fieldsCollection)
      .doc(`${fieldId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          dispatch({ type: GET_CUSTOM_FIELD_FAILURE })
          return reject({ message: langMessages[`${fieldsCollection}.inexistsMsg`], type: DOC_DOESNT_EXIST })
        }
        dispatch({ type: GET_CUSTOM_FIELD_SUCCESS, payload: doc.data() })
        return resolve(doc.data())
      })
      .catch(function (error) {
        dispatch({ type: GET_CUSTOM_FIELD_FAILURE })
        return reject()
      })
  })
}

export const addCustomField = newData => (dispatch, getState) => {
  dispatch({ type: ADD_CUSTOM_FIELD })
  return new Promise((resolve, reject) => {
    const user = localJSON.get('user', false)
    const currentOwnerID = sessionJSON.get('currentOwnerID', false)
    const ownerId = newData.owner || currentOwnerID || user.owner
    const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
    const accountId = newData.accountId || currentAccountID || user.accountId

    if (!accountId) {
      dispatch({ type: ADD_CUSTOM_FIELD_FAILURE })
      return reject()
    }

    const newField = {
      ...newData,
      date: moment().format(MOMENT_ISO),
      modified: moment().format(MOMENT_ISO),
      collection: fieldsCollection,
      author: user.ID,
      owner: ownerId,
      accountId,
    }

    FirestoreRef.collection(fieldsCollection)
      .add(newField)
      .then(doc => {
        const docId = doc.id
        const name = `${CUSTOM_FIELDS_GROUP}:${docId}`
        const { data } = newData
        const payload = {
          ...newField,
          name,
          id: docId,
          ID: docId,
          data: {
            ...data,
            name,
          },
        }
        dispatch({ type: ADD_CUSTOM_FIELD_SUCCESS, payload })
        return resolve(payload)
      })
      .catch(function (error) {
        dispatch({ type: ADD_CUSTOM_FIELD_FAILURE })
        return reject()
      })
  })
}

export const onUpdateCustomField = data => ({
  type: UPDATE_CUSTOM_FIELD,
  payload: data,
})

export const deleteCustomField = data => ({
  type: DELETE_CUSTOM_FIELD,
  payload: data,
})

export const onUpdateCustomFields = data => ({
  type: UPDATE_CUSTOM_FIELDS,
  payload: data,
})
