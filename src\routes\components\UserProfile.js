import { Avatar } from '@material-ui/core'; // Biblioteca de UI opcional
import PropTypes from 'prop-types'; // Para validação de props (opcional)
import React from 'react';

const UserProfile = ({ avatarUrl, title, subtitle }) => {
  const className = avatarUrl ? '' : 'bg-qiplus text-white'
  return (
    <div style={styles.container}>
      <Avatar src={avatarUrl} style={styles.avatar} alt={title} className={className}>
        {!avatarUrl && title?.charAt(0)} {/* Mostra a primeira letra do nome se não houver avatar */}
      </Avatar>
      <div style={styles.textContainer}>
        <span style={styles.title}>{title || ''}</span>
        <span style={styles.subtitle}>{subtitle || ''}</span>
      </div>
    </div>
  );
};

// Definição de estilos em JavaScript (inline styles)
const styles = {
  container: {
    display: 'flex',
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    marginRight: 10,
    color: 'white',
    backgroundColor: 'bg-qiplus',
    //
  },
  textContainer: {
    display: 'flex',
    flexDirection: 'column',
  },
  title: {
    fontSize: '16px',
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: '14px',
    color: 'gray',
  },
};

// Validação de props (opcional)
UserProfile.propTypes = {
  avatarUrl: PropTypes.string,
  title: PropTypes.string,
  subtitle: PropTypes.string,
};

export default UserProfile;
