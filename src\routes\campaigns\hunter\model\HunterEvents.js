import { langMessages } from "Lang/index";
import { Eye, MousePointer2 } from "lucide-react";
import React from "react";

export const HunterEvents = {
  pageview: {
    label: langMessages['campaigns.hunter.events.viewed'],
    key: 'viewed',
    color: '#82ca9d',
    icon: <Eye />
  },
  click: {
    label: langMessages['campaigns.hunter.events.clicked'],
    key: 'clicked',
    color: '#8884d8',
    icon: <MousePointer2 />
  },
}
