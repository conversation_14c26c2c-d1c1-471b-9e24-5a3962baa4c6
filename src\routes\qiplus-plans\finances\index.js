import { Box, Container, Grid } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import DateFiltersForm from 'Components/DateFilters/DateFiltersForm';
import { PageTitleBar } from 'Components/index';
import { langMessages } from 'Lang/index';
import { CreditCard, DollarSign, Receipt, UserCheck, UserMinus, Users, UserX } from 'lucide-react';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet';
import { connect } from 'react-redux';
import { formatCurrency } from 'Util/country/money';
import { PagarmeService } from '../../../services/pagarme';
import GeographicDistribution from './components/Dashboard/GeographicDistribution';
import MetricCard from './components/Dashboard/MetricCard';
import PaymentChart from './components/Dashboard/PaymentChart';
import PlanTotalsChart from './components/Dashboard/PlanTotalsChart';
import StatusDistribution from './components/Dashboard/StatusDistribution';
import SubscriptionChart from './components/Dashboard/SubscriptionChart';
import SubscriptionsTable from './components/Dashboard/SubscriptionsTable';

const useStyles = makeStyles((theme) => ({
  root: {
    // flexGrow: 1,
    // padding: theme.spacing(3),
  },
  title: {
    marginBottom: theme.spacing(4),
  },
  gridContainer: {
    marginBottom: theme.spacing(4),
  },
}));


const SubscriptionDashboard = (props) => {
  const { user, match } = props;

  const pagarmeService = new PagarmeService()

  const classes = useStyles();
  const [dateRange, setDateRange] = React.useState({
    start: moment().subtract(1, 'month').valueOf(),
    end: moment().valueOf(),
    range: 'last30Days',
  });
  const [subscriptions, setSubscriptions] = useState([]);
  const [loading, setLoading] = useState(true);

  const fetchSubscriptions = async () => {
    try {
      const subscriptions = await pagarmeService.getSubscriptions(dateRange);
      setSubscriptions(subscriptions);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscriptions();
  }, []);

  useEffect(() => {
    fetchSubscriptions();
  }, [dateRange]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </Box>
    );
  }

  const paids = subscriptions.filter((sub) => sub.status == 'paid')
  const unpaids = subscriptions.filter((sub) => sub.status == 'unpaid')
  const canceleds = subscriptions.filter((sub) => sub.status == 'canceled')
  const totalcount = paids.length + unpaids.length + canceleds.length

  const totalPaid = paids.reduce((acc, sub) => acc + (sub.plan.amount / 100), 0);
  const totalUnpaid = unpaids.reduce((acc, sub) => acc + (sub.plan.amount / 100), 0);
  const totalCanceled = canceleds.reduce((acc, sub) => acc + (sub.plan.amount / 100), 0);

  const creditCards = subscriptions.filter((sub) => sub.payment_method == 'credit_card' && sub.status != 'canceled')
  const creditCardsAmount = creditCards.reduce((acc, sub) => acc + (sub.plan.amount / 100), 0)

  const invoices = subscriptions.filter((sub) => sub.payment_method == 'boleto' && sub.status != 'canceled')
  const invoicesAmount = invoices.reduce((acc, sub) => acc + (sub.plan.amount / 100), 0)

  const totalAmount = totalPaid + totalUnpaid // + totalCanceled

  const totalRevenues = paids
    .reduce((acc, sub) => {
      const monthlyAmount = (sub.plan.amount / 100) / (sub.plan.days / 30);
      return acc + monthlyAmount;
    }, 0);

  return (
    <div className="ecom-dashboard-wrapper">
      <Helmet>
        <title>{langMessages["sidebar.plans.dashboard"]}</title>
        <meta name="description" content="Ads Reports" />
      </Helmet>
      <PageTitleBar
        title={langMessages["sidebar.plans.dashboard"]}
        match={match}
        rightComponents={
          <div className="d-flex justify-content-end">
            <DateFiltersForm filters={dateRange} onUpdate={setDateRange} />
          </div>
        }
      />
      <div className="dashboard-wrapper widgets-wrapper p-15">
        <div className="rowla">
          <Container maxWidth="xl" className={classes.root}>
            <Grid container spacing={4} className={classes.gridContainer}>
              <Grid item xs={12} sm={6} md={3}>
                <MetricCard
                  title={langMessages['subscription.overview.paids']}
                  value={formatCurrency(totalPaid)}
                  count={paids.length}
                  icon={<UserCheck />}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <MetricCard
                  title={langMessages['subscription.overview.unpaids']}
                  value={formatCurrency(totalUnpaid)}
                  count={unpaids.length}
                  icon={<UserX />}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <MetricCard
                  title={langMessages['subscription.overview.canceleds']}
                  value={formatCurrency(totalCanceled)}
                  count={canceleds.length}
                  icon={<UserMinus />}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <MetricCard
                  title={langMessages['subscription.overview.total']}
                  value={totalcount}
                  icon={<Users />}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <MetricCard
                  title={langMessages['subscription.overview.creditCards']}
                  value={formatCurrency(creditCardsAmount)}
                  icon={<CreditCard />}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <MetricCard
                  title={langMessages['subscription.overview.bankSlips']}
                  value={formatCurrency(invoicesAmount)}
                  icon={<Receipt />}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <MetricCard
                  title={langMessages['subscription.overview.monthlyRevenues']}
                  value={formatCurrency(totalRevenues)}
                  icon={<DollarSign />}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <MetricCard
                  title={langMessages['subscription.overview.totalRevenues']}
                  value={formatCurrency(totalAmount)}
                  icon={<DollarSign />}
                />
              </Grid>
            </Grid>

            <Grid container spacing={4}>
              <Grid item xs={12}>
                <PaymentChart subscriptions={subscriptions} />
              </Grid>
              <Grid item xs={12} md={8}>
                <PlanTotalsChart subscriptions={subscriptions} />
              </Grid>
              <Grid item xs={12} md={4}>
                <StatusDistribution subscriptions={subscriptions} />
              </Grid>
              <Grid item xs={12}>
                <SubscriptionChart subscriptions={subscriptions} />
              </Grid>
              <Grid item xs={12}>
                <SubscriptionsTable subscriptions={subscriptions} />
              </Grid>
              <Grid item xs={12}>
                <GeographicDistribution subscriptions={subscriptions} />
              </Grid>
            </Grid>
          </Container>
        </div>
      </div>
    </div>
  )
};

// map state to props
const mapStateToProps = ({ authReducer, settings }) => {
  const { user, account, ownerId } = authReducer;
  const { darkMode } = settings;

  return { user, account, accountId: account.ID, ownerId, darkMode };
}

export const FinancesDashboard = connect(mapStateToProps, null)(SubscriptionDashboard);
