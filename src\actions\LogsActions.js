/**
 * Logs Actions
 */
import { FirestoreRef, httpMailStats, CALLABLE_FUNCTIONS } from 'FirebaseRef'

import api from '../firebase/api'

// lang strings
import { langMessages } from '../lang'

import {
  COUNT_LOGS,
  COUNT_LOGS_SUCCESS,
  COUNT_LOGS_FAILURE,
  GET_POSTS_STATS,
  GET_POSTS_STATS_SUCCESS,
  GET_POSTS_STATS_FAILURE,
  GET_LOGS,
  GET_LOGS_SUCCESS,
  GET_LOGS_FAILURE,
  LISTEN_LOGS,
  LISTEN_LOGS_SUCCESS,
  LISTEN_LOGS_FAILURE,
  SAVE_NEW_LOG,
  SAVE_NEW_LOG_SUCCESS,
  SAVE_NEW_LOG_FAILURE,
} from './types'

import moment from 'moment'
import { LOGS_COLLECTION_NAME } from '../constants/AppCollections'
import CONSTANTS, { CREATED_FIELD, MOMENT_ISO, UPDATED_FIELD } from '../constants/AppConstants'
import ROLES, { OWNER_LEVEL } from '../constants/UsersRoles'
import { sessionJSON, localJSON, sortByUpdateDate, areEqualObjects, diffObjects, isset } from '../helpers/helpers'

/**
 * Redux Action Get Stats
 */

export const getMailingStats =
  ({ event, id, collection, queries, limit, lastPos, orderArgs, filters }) =>
  (dispatch, getState) => {
    queries = queries || []

    dispatch({ type: COUNT_LOGS, event, id, collection })

    return new Promise((resolve, reject) => {
      let orderBy = ''
      let order = ''

      if (event) {
        queries.push(['trigger', '==', event])
      } else if (orderArgs) {
        orderBy = typeof orderArgs === 'string' ? orderArgs : orderArgs.orderBy
        order = (orderArgs && orderArgs.order) || 'asc'
      } else if (!queries.length) {
        orderBy = UPDATED_FIELD
        order = 'desc'
      }

      const req = { id, collection, queries, limit, lastPos, orderBy, order, filters }

      httpMailStats(req)
        .then(result => {
          // console.log('httpMailStats > result',result);

          const { data } = result
          dispatch({ type: COUNT_LOGS_SUCCESS, event, id, collection, payload: data })
          return resolve(data)
        })
        .catch(error => {
          dispatch({ type: COUNT_LOGS_FAILURE, event, id, collection })
          console.error(error)
          return reject(error)
        })
    })
  }

export const resetMailingStats = ({ event, id, collection }) => {
  return {
    type: COUNT_LOGS_SUCCESS,
    event,
    id,
    collection,
    payload: {},
  }
}

let statsResolvers = {
  push: (collection, index, resolve) => {
    if (!isset(statsResolvers, [collection, index])) {
      statsResolvers[collection] = {
        ...(statsResolvers[collection] || {}),
        [index]: {
          fetching: true,
          resolvers: [],
        },
      }
    }
    statsResolvers[collection][index].resolvers.push(resolve)
    return statsResolvers[collection][index]
  },
  resolveAll: (collection, index, payload) => {
    let { resolvers } = statsResolvers[collection][index]
    resolvers.forEach(r => r(payload))
    statsResolvers[collection][index] = {
      fetching: false,
      resolvers: [],
    }
  },
}
export const getPostsStats = (request, skipCache, cacheFormat) => (dispatch, getState) => {
  let {
    authReducer: { user, account },
    logsReducer: { postsStatsRequests },
  } = getState()
  let {
    id,
    accountId,
    collection,
    dateFormats,
    filters,
    counters,
    accumulators,
    queries,
    keywords,
    groupKeywords,
    groupQueries,
    postsQueries,
    postsIds,
    limit,
    lastPos,
    orderArgs,
  } = request

  let index = postsStatsRequests[collection].length

  accountId = accountId || account.ID || user.accountId

  queries = queries || []
  keywords = keywords || []
  groupKeywords = groupKeywords || []
  groupQueries = groupQueries || []
  postsQueries = postsQueries || []

  let addedQueries = []
  const role = ROLES.QIPLUS_ROLES_HERARCHY[user.level]
  if (user.level > OWNER_LEVEL && (ROLES.QIPLUS_ROLES_RELATIONS[role] || []).includes(collection)) {
    let findKey = k => {
      if (Array.isArray(k)) {
        return k.indexOf(role) > -1 || k.find(a => a.indexOf(role) > -1)
      }
      return `${k}`.indexOf(role) > -1
    }
    if (!queries.find(q => q[0].indexOf(role) > -1 || findKey(q[2]))) {
      addedQueries.push([CONSTANTS.KEYWORDS_FIELD, 'array-contains', `[${role}]:[${user.ID}]`])
    }
  }

  if (addedQueries.length) {
    if (groupQueries.length || groupKeywords.length) {
      groupQueries = [...groupQueries, ...addedQueries]
    } else if (queries.length || keywords.length) {
      queries = [...queries, ...addedQueries]
    }
  }

  let apiRequest = {
    id,
    collection,
    accountId,
    filters: filters || {},
    dateFormats: dateFormats || [CONSTANTS.MOMENT_ISO_DAY],
    queries,
    keywords,
    groupKeywords,
    groupQueries,
    postsQueries,
    postsIds: postsIds || [],
    limit: limit || '',
    lastPos: lastPos || '',
    orderBy: (orderArgs || {}).orderBy || '',
    order: (orderArgs || {}).order || '',
    counters: counters || [],
    accumulators: accumulators || [],
  }

  return new Promise((resolve, reject) => {
    let consoleKey = `getPostsStats > ${collection} > ${index}`

    let previousPayload
    let options = { dateFormat: cacheFormat || CONSTANTS.MOMENT_ISO_HOURS, tolerance: 0, skipEmpty: false, skipPending: false }
    let foundRequest = !skipCache && findPreviousState(request, postsStatsRequests, options)

    if (foundRequest && foundRequest.payload) {
      index = foundRequest.index
      previousPayload = foundRequest.payload
      consoleKey += ` > found > ${index}`
    }

    let action = { index, collection, request }

    dispatch({ ...action, type: GET_POSTS_STATS })

    let colRequest = statsResolvers.push(collection, index, resolve)

    /*
        console.groupCollapsed(consoleKey);
        console.log('postsStatsRequests',window.jsonClone(postsStatsRequests));
        console.log('previousPayload',window.jsonClone(previousPayload));
        console.log('foundRequest',window.jsonClone(foundRequest));
        console.log('apiRequest',window.jsonClone(apiRequest));
        console.log('request',window.jsonClone(request));
        console.log('colRequest',window.jsonClone(colRequest));
        console.log(window.jsonClone({ statsResolvers }));
        console.groupEnd(consoleKey);
        */

    if (previousPayload) {
      dispatch({ ...action, type: GET_POSTS_STATS_SUCCESS, payload: previousPayload })
      !foundRequest.fetching && statsResolvers.resolveAll(collection, index, previousPayload)
      return
    }

    api(CALLABLE_FUNCTIONS.CALLABLE_POSTS_STATS, apiRequest)
      .then(response => {
        dispatch({ ...action, type: GET_POSTS_STATS_SUCCESS, payload: response })
        return statsResolvers.resolveAll(collection, index, response)
      })
      .catch(error => {
        // error handling
        console.error(error)
        dispatch({ ...action, type: GET_POSTS_STATS_FAILURE })
      })
  })
}

export const dispatchPayload =
  ({ collection, request, index, payload }) =>
  (dispatch, getState) => {
    dispatch({ type: GET_POSTS_STATS_SUCCESS, collection, request, index, payload })
  }

export const findPreviousState = (currentRequest, postsStatsRequests, options) => {
  let { dateFormat, tolerance, skipEmpty, skipPending } = options || {}

  dateFormat = dateFormat || CONSTANTS.MOMENT_ISO_HOURS

  let foundRequest
  let { collection } = currentRequest
  let { end, start, range } = currentRequest.filters || {}

  if (postsStatsRequests[collection]) {
    let matchRequests = window
      .jsonClone(postsStatsRequests[collection])
      .filter(r => r.request.collection === collection)
      .filter(r => {
        let {
          request,
          fetching,
          request: { filters },
          index,
          payload,
        } = r

        if (skipPending !== false && fetching) return false

        if (skipEmpty === true && !Object.keys(payload).length) return false

        filters = filters || {}
        let requestStart = moment(filters.start)
        let requestEnd = moment(filters.end)
        let startMatch = requestStart.format(dateFormat) === moment(start).format(dateFormat)
        let endMatch = requestEnd.format(dateFormat) === moment(end).format(dateFormat)

        if ((!startMatch || !endMatch) && range === filters.range && tolerance) {
          startMatch = requestStart.add(tolerance, 'minutes').valueOf() > moment(start).valueOf()
          endMatch = requestEnd.add(tolerance, 'minutes').valueOf() > moment(end).valueOf()
        }

        // console.log({
        //     startMatch, endMatch,
        //     diffRequest1: diffObjects(request, currentRequest, 10),
        //     diffRequest2: diffObjects(currentRequest, request, 10),
        // });

        if (startMatch && endMatch) {
          return areEqualObjects({ 1: { ...currentRequest, filters: null } }, { 1: { ...request, filters: null } })
        }
        return false
      })
    foundRequest = matchRequests.pop()
    // !foundRequest && console.log('payload not found:',{ currentRequest, matchRequests, diff: matchRequests.map(r=>diffObjects(currentRequest,r,10)) });
    // foundRequest && console.log('payload found:',{ foundRequest });
  }
  return foundRequest
}

export const findPreviousPayload = (currentRequest, postsStatsRequests, options) => {
  let foundRequest = findPreviousState(currentRequest, postsStatsRequests, options)
  return foundRequest && foundRequest.payload
}

/**
 * Redux Action Get Logs
 */
export const getLogs = (id, collection, queries, limit, lastPos, orderArgs) => (dispatch, getState) => {
  /*
    console.groupCollapsed('getLogs');
    console.log('collection',collection);
    console.log('queries',queries);
    console.log('limit',limit);
    console.log('lastPos',lastPos);
    console.log('orderArgs',orderArgs);
    console.groupEnd('getLogs');
    */
  queries = queries || []

  dispatch({ type: GET_LOGS, id, collection })

  return new Promise((resolve, reject) => {
    let QueryRef = FirestoreRef.collection(collection).doc(id).collection(LOGS_COLLECTION_NAME)

    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

    let orderBy = ''
    let order = ''

    if (orderArgs) {
      orderBy = typeof orderArgs === 'string' ? orderArgs : orderArgs.orderBy
      order = (orderArgs && orderArgs.order) || 'asc'
    } else if (!queries.length) {
      orderBy = UPDATED_FIELD
      order = 'desc'
    }

    if (orderBy) {
      QueryRef = QueryRef.orderBy(orderBy, order)
      !!lastPos && (QueryRef = QueryRef.startAt(lastPos))
    }

    !isNaN(limit) && limit && (QueryRef = QueryRef.limit(limit))

    QueryRef.get()
      .then(snapshot => {
        // console.log('logs snapshot',snapshot);

        const logs = []
        snapshot.forEach(doc => {
          logs.push(doc.data())
        })
        dispatch({ type: GET_LOGS_SUCCESS, id, collection, payload: logs })
        return resolve(logs)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: GET_LOGS_FAILURE, id, collection })
      })
  })
}

/**
 * Redux Action Listen Logs
 */
export const listenLogs = (id, collection, listenerFn, queries, limit, lastPos, orderArgs) => async dispatch => {
  // console.log('--------------------------------listenLogs-------------------------------------');
  queries = (Array.isArray(queries) && queries) || []

  dispatch({ type: LISTEN_LOGS, id, collection })

  let refListener

  let QueryRef = FirestoreRef.collection(collection).doc(id).collection(LOGS_COLLECTION_NAME)

  queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

  if (orderArgs) {
    let orderBy = typeof orderArgs === 'string' ? orderArgs : orderArgs.orderBy
    let order = (orderArgs && orderArgs.order) || 'asc'

    QueryRef = QueryRef.orderBy(orderBy, order)
    !!lastPos && (QueryRef = QueryRef.startAt(lastPos))
  }

  !isNaN(limit) && limit && (QueryRef = QueryRef.limit(limit))

  return new Promise(res => {
    let snapshotErr = error => {
      console.error(LISTEN_LOGS_FAILURE, error)
      dispatch({ type: LISTEN_LOGS_FAILURE, id, collection })
    }

    let snapshotSuccess = snapshot => {
      // console.groupCollapsed('listenLogs > snapshot');
      // console.log('snapshot > '+collection,snapshot.size);

      const logs = snapshot.docs.map(doc => doc.data()).sort(sortByUpdateDate)

      // let last = window.jsonClone(logs).pop()||{};
      // console.log('remoteLogs > logs',logs.filter((a,k)=>k<10).map(l=>l.updatedAt));
      // console.log('remoteLogs > updatedAt',last.updatedAt);
      // console.log('dups',logs.filter((l,k)=>logs.find((lo,ko)=>ko!==k&&lo.updatedAt===l.updatedAt)).length);

      dispatch({ type: LISTEN_LOGS_SUCCESS, id, collection, payload: logs })
      listenerFn && listenerFn({ logs, id, collection, refListener })
      // console.groupEnd('listenLogs > snapshot');
    }

    refListener = QueryRef.onSnapshot(snapshotSuccess, snapshotErr)

    return res(refListener)
  })
}

/**
 * Redux Action Get collectionGroup Logs
 */

export const getGroupLogs = (id, collection, queries, limit, lastPos, orderArgs, skipDispatch) => (dispatch, getState) => {
  /*
    console.groupCollapsed('getLogs');
    console.log('collection',collection);
    console.log('queries',queries);
    console.log('limit',limit);
    console.log('lastPos',lastPos);
    console.log('orderArgs',orderArgs);
    console.groupEnd('getLogs');
    */
  queries = queries || []

  skipDispatch !== true && dispatch({ type: GET_LOGS, id, collection })

  return new Promise((resolve, reject) => {
    let QueryRef = FirestoreRef.collectionGroup(LOGS_COLLECTION_NAME)

    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

    let orderBy = ''
    let order = ''

    if (orderArgs) {
      orderBy = typeof orderArgs === 'string' ? orderArgs : orderArgs.orderBy
      order = (orderArgs && orderArgs.order) || 'desc'
    } else if (!queries.length || !queries.find(queryArgs => queryArgs[0] === CONSTANTS.CREATED_FIELD)) {
      orderBy = CONSTANTS.CREATED_FIELD
      order = 'desc'
    }

    if (orderBy) {
      QueryRef = QueryRef.orderBy(orderBy, order)
      !!lastPos && (QueryRef = QueryRef.startAt(lastPos))
    } else if (queries.find(queryArgs => queryArgs[0] === CONSTANTS.CREATED_FIELD)) {
      /*
            | This is due to firestore indexes, in which
            | composed indexes where created only with createdAt/desc
            */
      QueryRef = QueryRef.orderBy(CONSTANTS.CREATED_FIELD, 'desc')
    }

    !isNaN(limit) && limit && (QueryRef = QueryRef.limit(limit))

    QueryRef.get()
      .then(snapshot => {
        // console.log('logs snapshot',snapshot);

        const logs = snapshot.docs.map(doc => doc.data()).sort(sortByUpdateDate)
        skipDispatch !== true && dispatch({ type: GET_LOGS_SUCCESS, id, collection, payload: logs })
        return resolve(logs)
      })
      .catch(function (error) {
        console.error(error)
        skipDispatch !== true && dispatch({ type: GET_LOGS_FAILURE, id, collection })
      })
  })
}

/**
 * Redux Action Listen collectionGroup Logs
 */
export const listenGroupLogs = (id, collection, listenerFn, queries, limit, lastPos, orderArgs) => async dispatch => {
  // console.log('--------------------------------listenGroupLogs-------------------------------------');
  queries = (Array.isArray(queries) && queries) || []

  dispatch({ type: LISTEN_LOGS, id, collection })

  let refListener

  let QueryRef = FirestoreRef.collectionGroup(LOGS_COLLECTION_NAME)

  queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

  if (orderArgs) {
    let orderBy = typeof orderArgs === 'string' ? orderArgs : orderArgs.orderBy
    let order = (orderArgs && orderArgs.order) || 'asc'

    QueryRef = QueryRef.orderBy(orderBy, order)
    !!lastPos && (QueryRef = QueryRef.startAt(lastPos))
  } else if (queries.find(queryArgs => queryArgs[0] === CONSTANTS.CREATED_FIELD)) {
    /*
        | This is due to firestore indexes, in which
        | composed indexes where created only with createdAt/desc
        */
    QueryRef = QueryRef.orderBy(CONSTANTS.CREATED_FIELD, 'desc')
  }

  !isNaN(limit) && limit && (QueryRef = QueryRef.limit(limit))

  return new Promise(res => {
    let snapshotErr = error => {
      console.error(LISTEN_LOGS_FAILURE, error)
      dispatch({ type: LISTEN_LOGS_FAILURE, id, collection })
    }

    let snapshotSuccess = snapshot => {
      // console.groupCollapsed('listenGroupLogs > snapshot');
      // console.log('snapshot > '+collection,snapshot.size);

      const logs = snapshot.docs.map(doc => doc.data()).sort(sortByUpdateDate)

      // console.log('remoteLogs > logs',logs.filter((a,k)=>k<10).map(l=>l.updatedAt));
      // console.log('remoteLogs > updatedAt',(window.jsonClone(logs).pop()||{}).updatedAt);
      // console.log('dups',logs.filter((l,k)=>logs.find((lo,ko)=>ko!==k&&lo.updatedAt===l.updatedAt)).length);

      dispatch({ type: LISTEN_LOGS_SUCCESS, id, collection, payload: logs })
      listenerFn && listenerFn({ logs, id, collection, refListener })
      // console.groupEnd('listenGroupLogs > snapshot');
    }

    refListener = QueryRef.onSnapshot(snapshotSuccess, snapshotErr)

    return res(refListener)
  })
}

export const saveNewLog = data => (dispatch, getState) => {
  console.log('--------------------------------saveNewLog-------------------------------------')

  let {
    authReducer: { user, ownerId, account },
  } = getState()

  const { collection, id } = data
  dispatch({ type: SAVE_NEW_LOG, id, collection })
  return new Promise((resolve, reject) => {
    const accountId = data.accountId || account.ID || user.accountId

    const newLog = {
      ...data,
      date: moment().format(MOMENT_ISO),
      operator_id: user.ID,
      owner: ownerId,
      accountId,
    }

    FirestoreRef.collection(collection)
      .doc(id)
      .collection(LOGS_COLLECTION_NAME)
      .add(newLog)
      .then(doc => {
        const payload = newLog
        dispatch({ type: SAVE_NEW_LOG_SUCCESS, id, collection, payload })
        return resolve(payload, collection)
      })
      .catch(function (error) {
        console.error(SAVE_NEW_LOG_FAILURE, error)
        dispatch({ type: SAVE_NEW_LOG_FAILURE, id, collection })
        return reject()
      })
  })
}
