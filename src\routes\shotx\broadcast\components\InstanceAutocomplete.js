import {
  Box,
  TextField
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Autocomplete } from '@material-ui/lab';
import { langMessages } from 'Lang/index';
import React, { useState } from 'react';
import { InstanceSelectCard } from '../../../../components-new/molecules';

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(2),
  },
  instancePreview: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  option: {
    fontSize: 14,
    '& > span': {
      marginRight: 10,
      fontSize: 18,
    },
  },
}));

const Instancecomplete = ({
  instances,
  selectedInstanceId,
  onInstanceChange,
  disabled = false
}) => {
  const classes = useStyles();
  const [inputValue, setInputValue] = useState('');

  const selectedInstance = instances.find(
    (m) => m.id.toString() === selectedInstanceId
  );

  return (
    <div className={classes.root}>
      <Autocomplete
        options={instances}
        disabled={disabled}
        getOptionLabel={(option) => option.title}
        value={instances.find((m) => m.id.toString() === selectedInstanceId) || null}
        onChange={(event, newValue) => {
          onInstanceChange(newValue ? newValue.id.toString() : '');
        }}
        inputValue={inputValue}
        onInputChange={(event, newInputValue) => {
          setInputValue(newInputValue);
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            disabled={disabled}
            label={langMessages['shotx.broadcast.instanceSelectLabel']}
            variant="outlined"
            fullWidth
          />
        )}
        renderOption={(option) => (
          <Box className={classes.option}>
            <InstanceSelectCard instance={option} />
          </Box>
        )}
      />
      {selectedInstance && (
        <Box className={classes.instancePreview}>
          <InstanceSelectCard instance={selectedInstance} />
        </Box>
      )}
    </div>
  );
};

export default Instancecomplete;
