import { langMessages } from "Lang/index";
import React from "react";
import { TextMessage } from "./text";
import { VideoFrame } from './video.style';

const VideoMessage = ({ message, channel }) => {
  const { video } = message

  if (video?.mediaUrl) {
    return (
      <div className="bg-transparent">
        <VideoFrame>
          <video src={video.mediaUrl} controls className='w-100 h-100' />
        </VideoFrame>
        {video?.caption && <TextMessage text={video.caption} />}
      </div>
    )
  }

  return (
    <p>{langMessages['shotx.chat.videoNotAvailable']}</p>
  )
}

export default VideoMessage;
