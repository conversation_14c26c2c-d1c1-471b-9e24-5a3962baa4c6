import TableCell from '@material-ui/core/TableCell'
import TableRow from '@material-ui/core/TableRow'
import { LEADS_COLLECTION_NAME } from 'Constants/AppCollections'
import { getCollectionPath } from 'Helpers'
import { langMessages } from 'Lang/index'
import moment from 'moment'
import React from 'react'
import { Badge } from 'reactstrap'
import UserProfile from 'Routes/components/UserProfile'

export const ShotXInteractionCard = ({
  interaction,
  lead,
  onVinculate,
  onUnvinculate
}) => {

  const { ig_sid, username, name, created_at, lead_id, event_type, avatar } = interaction

  const vinculated = !!lead
  const leadName = lead ? lead.displayName : langMessages['shotx.instance.interactions.notVinculated']
  const action = vinculated ? langMessages['shotx.instance.interactions.unvinculate'] : langMessages['shotx.instance.interactions.vinculate']
  const detailLing = `/${getCollectionPath(LEADS_COLLECTION_NAME)}/${lead_id}`

  const onLeadClick = () => {
    if (vinculated) {
      window.open(detailLing, '_blank')
    } else {
      onVinculate(interaction)
    }
  }

  const onActionClick = () => {
    if (vinculated) {
      onUnvinculate(interaction)
    } else {
      onVinculate(interaction)
    }
  }

  return (
    <TableRow key={ig_sid}>
      <TableCell component="th" scope="row">
        <UserProfile avatarUrl={avatar} title={username} subtitle={name} />
      </TableCell>
      <TableCell align="left">{event_type?.join(', ')}</TableCell>
      <TableCell align="left">{moment.unix(created_at).format('DD/MM/YY HH:mm')}</TableCell>
      <TableCell align="left">
        <Badge
          onClick={onLeadClick}
          className="cursor-pointer"
          color={vinculated ? 'success' : 'warning'}
        >
          {leadName}
        </Badge>
      </TableCell>
      <TableCell align="left">
        <Badge
          onClick={onActionClick}
          className="cursor-pointer"
          color={vinculated ? 'danger' : 'success'}
        >
          <i className={vinculated ? 'ti-unlink' : 'ti-link'}></i>
          {action}
        </Badge>
      </TableCell>
    </TableRow>
  )
}
