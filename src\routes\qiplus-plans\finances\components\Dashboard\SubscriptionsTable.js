import {
  Card,
  CardContent,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Typography
} from '@material-ui/core';
import { langMessages } from 'Lang/index';
import React, { useState } from 'react';
import { formatCurrency } from 'Util/country/money';

const SubscriptionsTable = ({ subscriptions }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const calculatePeriod = (subscription) => {
    const startDate = new Date(subscription.current_period_start);
    const endDate = new Date(subscription.current_period_end);
    return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {langMessages['subscription.overview.list']}
        </Typography>
        <TableContainer component={Paper} style={{ height: '400px', overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell colSpan={2}>{langMessages['subscription.overview.customer']}</TableCell>
                <TableCell>{langMessages['subscription.overview.plan']}</TableCell>
                <TableCell>{langMessages['subscription.overview.amount']}</TableCell>
                <TableCell>{langMessages['subscription.overview.paymentMethod']}</TableCell>
                <TableCell>{langMessages['subscription.overview.status']}</TableCell>
                <TableCell>{langMessages['subscription.overview.period']}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {subscriptions
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((subscription) => (
                  <TableRow key={subscription.id}>
                    <TableCell colSpan={2}>{subscription.customer.name}</TableCell>
                    <TableCell>{subscription.plan.name}</TableCell>
                    <TableCell>{formatCurrency(subscription.plan.amount / 100)}</TableCell>
                    <TableCell>{langMessages[`subscription.${subscription.payment_method}`]}</TableCell>
                    <TableCell>{langMessages[`subscription.${subscription.status}`]}</TableCell>
                    <TableCell>{calculatePeriod(subscription)}</TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={subscriptions.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </CardContent>
    </Card>
  );
};

export default SubscriptionsTable;
