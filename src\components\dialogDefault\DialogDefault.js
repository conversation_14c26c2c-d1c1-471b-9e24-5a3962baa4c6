import { Button, Dialog, DialogActions } from '@material-ui/core';
import { Loading } from 'Components/Widgets/components/Loading';
import { langMessages } from 'Lang/index';
import React from 'react';
import { DialogDefaultForm } from './DialogDefaultForm';
import { DialogDefaultTitle } from './DialogDefaultTitle';

export function DialogDefault(props) {
  const {
    open,
    title,
    content,
    isLoading,
    onBack,
    onNext,
    onClose,
    disableNext,
    disablePrevious,
    nextText,
    backText,
    width = '70vw',
    height = '70vh',
    buttonActiveColor = 'bg-qiplus text-white',
    showdialogactionbutton = true,
  } = props

  return (
    <Dialog
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      open={open}
      fullScreen
      scroll="paper"
      PaperProps={{
        style: {
          minHeight: height,
          minWidth: width,
        },
      }}
    >
      <DialogDefaultTitle  {...props} />
      <Loading loading={isLoading} label={langMessages['texts.loading']}>
        {<DialogDefaultForm {...content} />}
      </Loading>
      {showdialogactionbutton && <DialogActions>
        {onBack && <Button variant="contained" disabled={disablePrevious || isLoading} onClick={onBack} className={disablePrevious ? 'secondary' : buttonActiveColor} >
          {backText || langMessages['button.back']}
        </Button>}
        <Button variant="contained" onClick={onNext} disabled={disableNext || isLoading} className={disableNext ? 'secondary' : buttonActiveColor}>
          {nextText || langMessages['button.next']}
        </Button>
      </DialogActions>}
    </Dialog>
  )
}
