/**
 * Storage Actions
 * Storage Actions With Firebase
 */
// Firebase
import { ImagesRef } from 'FirebaseRef'

export function uploadFile(data) {
  const { file, path, postId, user } = data

  const metaData = {
    customMetadata: {
      postId,
      path,
      user,
    },
  }

  return dispatch =>
    new Promise(resolve => {
      const fileName = new Date().getTime()
      const uploadTask = ImagesRef.child(`${path}/${postId}/${fileName}`).put(file, metaData)

      return uploadTask.on(
        'state_changed',
        snapshot => {
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100
          resolve(
            dispatch({
              type: 'MEDIA_UPLOAD_PROGRESS',
              data: progress,
            })
          )
        },
        error => {
          resolve(
            dispatch({
              type: 'MEDIA_UPLOAD_ERROR',
              data: error,
            })
          )
        },
        () => {
          const { downloadURL, metadata } = uploadTask.snapshot
          resolve(
            dispatch({
              type: 'MEDIA_UPLOAD_END',
              data: { downloadURL, metadata },
            })
          )
        }
      )
    })
}
