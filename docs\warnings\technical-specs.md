# Especificações Técnicas - Warnings

## Arquitetura dos Componentes

### WarningsList (Function Component)
**Localização**: `src/routes/qiplus-plans/warnings/list/index.js`

#### Estado Local
```javascript
const [warnings, setWarnings] = useState([])      // Lista de avisos
const [loading, setLoading] = useState(false)     // Estado de carregamento
```

#### Hooks Utilizados
- `useEffect()` - Carrega dados na montagem do componente
- `useState()` - Gerenciamento de estado local

#### Props do Redux
- `user` - Dados do usuário logado
- `account` - Dados da conta
- `accountId` - ID da conta
- `ownerId` - ID do proprietário

#### Funcionalidades
- Carregamento assíncrono de dados mockados
- Renderização de cards responsivos
- Navegação para criação/edição
- Indicadores visuais de status

### WarningDetail (Function Component)
**Localização**: `src/routes/qiplus-plans/warnings/detail/index.js`

#### Estado Local
```javascript
const [warning, setWarning] = useState({ ...warningModel })  // Dados do aviso
const [loading, setLoading] = useState(false)               // Carregamento
const [saving, setSaving] = useState(false)                 // Salvamento
const [errors, setErrors] = useState({})                    // Erros de validação
const [success, setSuccess] = useState(false)               // Sucesso
```

#### Hooks Utilizados
- `useEffect()` - Carrega dados para edição
- `useState()` - Gerenciamento de estado local
- `useHistory()` - Navegação programática
- `useParams()` - Parâmetros da URL

#### Validações Implementadas
- Título obrigatório
- Conteúdo para email obrigatório
- Conteúdo para app obrigatório
- Feedback visual de erros

## Modelo de Dados

### warningModel
```javascript
{
  id: '',                    // ID único
  ID: '',                    // ID alternativo
  title: '',                 // Título do aviso
  content_mail: '',          // Conteúdo para email
  content_app: '',           // Conteúdo para app
  date: moment().format(),   // Data de criação
  modified: moment().format(), // Data de modificação
  collection: 'warnings',   // Coleção
  post_type: 'warning',      // Tipo de post
  active: true,              // Status ativo
  author: '',                // Autor
  owner: '',                 // Proprietário
  status: 'publish'          // Status de publicação
}
```

### Integração Firebase
Estrutura de dados no Firebase:
- Collection: `warnings`
- Campos de timestamp: `createdAt`, `updatedAt` (Unix timestamp em segundos)
- Campos de identificação: `id`, `ID` (ambos com mesmo valor)
- Busca de autores: Collection `qiusers` usando `where uid = created_by`
- Salvamento direto com `setDoc()` para evitar operações duplas

## Configuração de Rotas

### Arquivo: `src/routes/qiplus-plans/index.js`

```javascript
// Rotas específicas para warnings
<Route exact path={`${match.path}/warnings`} component={WarningsList} />
<Route exact path={`${match.path}/warnings/add`} component={WarningDetail} />
<Route exact path={`${match.path}/warnings/:warningId`} component={WarningDetail} />
```

### Parâmetros de Rota
- `:warningId` - ID do aviso para edição/visualização
- Detecção automática de modo (criação vs edição)

## Configuração do Menu

### Arquivo: `src/components/Sidebar/NavLinks.js`

```javascript
{
  path: '/qiplus-plans/warnings',
  menu_title: 'sidebar.plans.warnings',
  menu_icon: 'ti-announcement',
  menu_level: ROLES.WEBMASTER_LEVEL,
  new_item: false,
  cap: '',
  exact: true,
}
```

### Permissões
- **Nível**: WEBMASTER_LEVEL
- **Ícone**: ti-announcement
- **Posição**: Submenu de "Planos QIPlus"

## Internacionalização

### Chaves de Tradução Adicionadas

#### Português (`src/lang/locales/pt_BR.js`)
```javascript
"sidebar.plans.warnings": "Avisos"
```

#### Inglês (`src/lang/locales/en_US.js`)
```javascript
"sidebar.plans.warnings": "Warnings"
```

## Componentes UI Utilizados

### Reactstrap
- `Badge` - Indicadores de status
- `Button` - Botões de ação
- `Form`, `FormGroup`, `Label`, `Input` - Formulários
- `Alert` - Mensagens de feedback

### Material-UI
- `LinearProgress` - Indicador de carregamento

### Componentes Customizados
- `RctCollapsibleCard` - Cards padrão do sistema

## Fluxo de Dados

### Listagem
1. Componente monta → `useEffect` dispara
2. `loadWarnings()` consulta Firebase collection `warnings`
3. Normalização de timestamps (ms → s se necessário)
4. `loadAuthorsData()` busca dados dos autores na collection `qiusers`
5. Renderização dos cards com dados reais

### Criação/Edição
1. Componente monta → `useEffect` verifica modo
2. Se edição: `loadWarning()` carrega dados do Firebase + dados do autor
3. Se criação: inicializa com modelo vazio
4. Usuário preenche formulário
5. `handleSave()` valida e salva no Firebase usando `setDoc()`
6. Feedback de sucesso → redirecionamento

## Padrões de Código

### Nomenclatura
- Componentes: PascalCase
- Funções: camelCase
- Constantes: UPPER_SNAKE_CASE
- Arquivos: kebab-case para pastas, camelCase para arquivos

### Estrutura de Arquivos
```
warnings/
├── list/
│   └── index.js          # Componente de listagem
├── detail/
│   └── index.js          # Componente de criação/edição
└── model/
    ├── index.js          # Modelo e dados mockados
    └── fields.js         # Definição de campos
```

### Imports Organizados
1. React e hooks
2. Redux e React Router
3. Componentes UI
4. Helpers e utilitários
5. Modelos e dados locais

## Performance

### Otimizações Implementadas
- Simulação de delay para carregamento realista
- Estado local para evitar re-renders desnecessários
- Validação em tempo real com debounce implícito

### Considerações Futuras
- Implementar lazy loading para listas grandes
- Cache de dados para melhor UX
- Paginação quando integrar com Firebase

## Testes Sugeridos

### Testes Unitários
- Renderização dos componentes
- Validação de formulários
- Manipulação de estado
- Navegação entre rotas

### Testes de Integração
- Fluxo completo de criação
- Fluxo completo de edição
- Navegação entre listagem e detalhes
- Permissões de acesso

### Testes E2E
- Acesso via menu
- CRUD completo de avisos
- Responsividade em diferentes dispositivos

## Dependências

### Principais
- React 16.8+ (para hooks)
- React Router DOM
- Redux + React Redux
- Reactstrap
- Material-UI
- Moment.js

### DevDependencies Sugeridas
- Jest (testes)
- React Testing Library
- Cypress (E2E)

## Configuração de Desenvolvimento

### Variáveis de Ambiente
Nenhuma variável específica necessária para a funcionalidade atual.

### Scripts NPM
Utiliza os scripts padrão do projeto:
- `npm start` - Desenvolvimento
- `npm run build` - Build de produção
- `npm test` - Execução de testes
