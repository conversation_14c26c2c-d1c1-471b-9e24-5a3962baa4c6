import {
  Button,
  IconButton,
  Paper,
  Snackbar,
  Typography
} from '@material-ui/core';
import { FileCopy as CopyIcon } from '@material-ui/icons';
import { HUNTER_TRACK_URL } from 'Constants';
import JavaScriptObfuscator from 'javascript-obfuscator';
import { langMessages } from 'Lang/index';
import React from 'react';

const PixelCode = ({ pixel, onClose }) => {
  const [showCopyMessage, setShowCopyMessage] = React.useState(false);
  const obfuscateCode = (code) => {
    const obfuscatedCode = JavaScriptObfuscator.obfuscate(code, {
      compact: true,
      controlFlowFlattening: true,
      controlFlowFlatteningThreshold: 1,
      numbersToExpressions: true,
      simplify: true,
      stringArrayShuffle: true,
      splitStrings: true,
      stringArrayThreshold: 1,
      disableConsoleOutput: true,
      deadCodeInjection: true
    });

    return obfuscatedCode.getObfuscatedCode();
  };
  const generatedCode = `
  ((w, d, i, e) => {
      const enc = encodeURIComponent;
      const href = enc(enc(w.location.href));
      const tck = (e, m) => {
        const f = d.createElement('img');
        f.src = \`${HUNTER_TRACK_URL}t/\${enc(e)}/url/\${href}/d/\${enc(i)}\` + (m ? \`/m/\${enc(JSON.stringify(m))}\` : '') + '/pixel.png';
        };

      const urlQuery = {};
      for (let p of new URL(w.location.href).searchParams) urlQuery[p[0]]=p[1];

      const referrerQuery = {};
      if (d.referrer) for (let p of new URL(d.referrer).searchParams) referrerQuery[p[0]]=p[1];

      const decKey = 'dec2';
      const cms = {
        url: decKey + href,
        urlQuery,
        referrer: d.referrer ? decKey + enc(enc(d.referrer)) : '',
        referrerQuery,
        userAgent: decKey + enc(enc(w.navigator.userAgent)),
        deviceType: /Mobi|Android/i.test(w.navigator.userAgent) ? 'mobile' : 'desktop',
        os: w.navigator.platform,
        viewWidth: w.innerWidth,
        viewHeight: w.innerHeight,
        screenWidth: w.screen.width,
        screenHeight: w.screen.height,
        scrollDepth: Math.round((d.documentElement.scrollTop + w.innerHeight) / d.documentElement.scrollHeight * 100),
        pagetitle: d.title,
        route: decKey + enc(enc(w.location.pathname)),
      };

      e.forEach(e => {
        switch (e) {
          case 'pageview':
            tck('v', cms);
            break;
          default:
            w.addEventListener(e, (m) => {
              tck(e[0], {
                ...cms,
                x: m.offsetX,
                y: m.offsetY,
                eltag: m.target.tagName,
                elid: m.target.id,
              });
            });
            break;
        }
      });
    })(window,document, '${pixel.id}',['${pixel.events.join("', '")}']);
    `.trim();

  const obfuscatedCode = `
    <!-- ${langMessages['campaigns.hunter.codeStart']} : ${pixel.name} -->
    <script>
      ${obfuscateCode(generatedCode)}
    </script>
    <!-- ${langMessages['campaigns.hunter.codeEnd']} : ${pixel.name} -->
  `.trim();

  const copyCode = () => {

    navigator.clipboard.writeText(obfuscatedCode);
    setShowCopyMessage(true);
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
        <Typography variant="h6">{pixel.name}</Typography>
        <IconButton onClick={copyCode} color="primary" title={langMessages['campaigns.hunter.copy']}>
          <CopyIcon />
        </IconButton>
      </div>

      <Paper style={{ padding: '1rem', overflowX: 'auto' }}>
        <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-all', maxHeight: '400px', overflow: 'auto' }}>
          <code className='text-qiplus'>{obfuscatedCode}</code>
        </pre>
      </Paper>

      <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '1rem' }}>
        <Button onClick={onClose} color="primary">
          {langMessages['campaigns.hunter.close']}
        </Button>
      </div>

      <Snackbar
        open={showCopyMessage}
        autoHideDuration={3000}
        onClose={() => setShowCopyMessage(false)}
        message={langMessages['campaigns.hunter.copied']}
      />
    </div>
  );
};

export default PixelCode;
