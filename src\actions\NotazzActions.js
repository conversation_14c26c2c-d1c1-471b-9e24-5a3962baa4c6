/**
 * Notazz Actions
 */
import { FirestoreRef } from 'FirebaseRef'

import { qiAjax } from 'Helpers/helpers'

// lang strings
import { langMessages } from '../lang'

import {
  CREATE_NOTAZZ_NF,
  CREATE_NOTAZZ_NF_SUCCESS,
  CREATE_NOTAZZ_NF_FAILURE,
  GET_NOTAZZ_NF,
  GET_NOTAZZ_NF_SUCCESS,
  GET_NOTAZZ_NF_FAILURE,
} from 'Actions/types'

import COLLECTIONS from '../constants/AppCollections'

import {
  AJAX_ACTION_CREATE_NOTAZZ_NFSE,
  AJAX_ACTION_CREATE_NOTAZZ_NFE,
  AJAX_ACTION_GET_NOTAZZ_NFSE,
  AJAX_ACTION_GET_NOTAZZ_NFE,
} from '../constants/AppConstants'

/**
 * Redux Action Create NF
 */
export const createNotazzNF = (apiKey, nfType, nfData) => (dispatch, getState) => {
  // console.log('createNotazzNF > data',nfType,nfData);

  dispatch({ type: CREATE_NOTAZZ_NF })

  return new Promise((resolve, reject) => {
    let isService = nfType === 'service'
    let action = isService ? AJAX_ACTION_CREATE_NOTAZZ_NFSE : AJAX_ACTION_CREATE_NOTAZZ_NFE

    qiAjax({
      data: {
        action,
        nfe: nfData,
        nfse: nfData,
        apiKey,
      },
      success: response => {
        console.log('createNotazzNF > response', response)
        const { result, status, error } = response
        if (status === true) {
          dispatch({ type: CREATE_NOTAZZ_NF_SUCCESS, payload: result })
          return resolve(result)
        }
        dispatch({ type: CREATE_NOTAZZ_NF_FAILURE })
        if (result && result.motivo) {
          // codigoProcessamento: "121"
          // motivo: "O IP ************** nao esta autorizado a utilizar recursos da API"
          // statusProcessamento: "erro"
          return reject({ message: result.motivo })
        }
        return reject({ message: langMessages['nf.apiError'] })
      },
      error: err => {
        console.log('createNotazzNF > err', err)
        dispatch({ type: CREATE_NOTAZZ_NF_FAILURE })
        return reject(err)
      },
    })
  })
}

export const getNotazzNF = (apiKey, nfType, nfData) => (dispatch, getState) => {
  // console.log('getNotazzNF > data',nfType,nfData);
  // nfData['DOCUMENT_ID']
  // nfData['EXTERNAL_ID']

  dispatch({ type: GET_NOTAZZ_NF })

  return new Promise((resolve, reject) => {
    let isService = nfType === 'service'
    let action = isService ? AJAX_ACTION_GET_NOTAZZ_NFSE : AJAX_ACTION_GET_NOTAZZ_NFE

    qiAjax({
      data: {
        action,
        nfe: nfData,
        nfse: nfData,
        apiKey,
      },
      success: response => {
        console.log('getNotazzNF > response', response)
        const { result, status, error } = response
        if (status === true) {
          dispatch({ type: GET_NOTAZZ_NF_SUCCESS, payload: result })
          return resolve(result)
        }
        dispatch({ type: GET_NOTAZZ_NF_FAILURE })
        return reject({ message: langMessages['nf.apiError'] })
      },
      error: err => {
        console.log('getNotazzNF > err', err)
        dispatch({ type: GET_NOTAZZ_NF_FAILURE })
        return reject(err)
      },
    })
  })
}
