import { FirebaseRepository } from "FirebaseRef/repository"

export class ApiKeyRepository {

    constructor(accountId) {
        this.accountId = accountId
        this.repository = new FirebaseRepository()
        this.path = `shotx/${accountId}`
    }

    getAll = (onSuccess, onError) => {

        return this.repository.getDoc(this.path, onSuccess, onError)
    }

    update = async (value) => {
        return this.repository.setDoc(`${this.path}`, { sniperTokens: value })
    }
}
