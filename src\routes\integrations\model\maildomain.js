
import CONSTANTS from '../../../constants/AppConstants'

const { MAIL_DOMAIN_PLATFORM } = CONSTANTS

const platform = MAIL_DOMAIN_PLATFORM

const platformField = {
  field: 'platform',
  key: 'data',
  operator: '==',
  value: platform
}


const noMainProvidersCond = [
  [
    {
      field: 'host',
      key: 'data:maildomain_config',
      operator: '!=empty',
    },
    {
      field: 'host',
      key: 'data:maildomain_config',
      operator: '==email',
    },
    {
      field: 'host',
      key: 'data:maildomain_config',
      operator: '!=contains',
      value: '@gmail.',
    },
    {
      field: 'host',
      key: 'data:maildomain_config',
      operator: '!=contains',
      value: '@hotmail.',
    },
    {
      field: 'host',
      key: 'data:maildomain_config',
      operator: '!=contains',
      value: '@live.',
    },
    {
      field: 'host',
      key: 'data:maildomain_config',
      operator: '!=contains',
      value: '@outlook.',
    },
    {
      field: 'host',
      key: 'data:maildomain_config',
      operator: '!=contains',
      value: '@yahoo.',
    },
  ],
]

const Fields = [
  {
    block: platform,
    label: '',
    name: '',
    type: 'message',
    instructions: '',
    required: 0,
    conditional_logic: [
      [
        {
          field: 'host',
          key: 'data:maildomain_config',
          operator: '==contains',
          value: 'gmail.',
        },
      ],
      [
        {
          field: 'host',
          key: 'data:maildomain_config',
          operator: '==contains',
          value: 'hotmail.',
        },
      ],
      [
        {
          field: 'host',
          key: 'data:maildomain_config',
          operator: '==contains',
          value: 'live.',
        },
      ],
      [
        {
          field: 'host',
          key: 'data:maildomain_config',
          operator: '==contains',
          value: 'outlook.',
        },
      ],
      [
        {
          field: 'host',
          key: 'data:maildomain_config',
          operator: '==contains',
          value: 'yahoo.',
        },
      ],
    ],
    wrapper: {
      width: '',
      class: '',
      innerClass: 'alert alert-danger mb-20',
      id: '',
    },
    message: 'A integração com gmail, hotmail, outlook e yahoo deverá ser feita através de uma caixa de entrada',
    new_lines: '',
    esc_html: 0,
  },
]

export default Fields
