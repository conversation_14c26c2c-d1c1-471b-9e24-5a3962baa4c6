import { makeStyles, Paper, TableFooter } from '@material-ui/core'
import Table from '@material-ui/core/Table'
import TableBody from '@material-ui/core/TableBody'
import TableCell from '@material-ui/core/TableCell'
import TableContainer from '@material-ui/core/TableContainer'
import TableHead from '@material-ui/core/TableHead'
import TableRow from '@material-ui/core/TableRow'
import SimplePaginationNav from 'Components/Pagination/SimplePaginationNav'
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'
import React from 'react'
import NoPostsFound from 'Routes/components/NoPostsFound'
const useStyles = makeStyles({
  table: {
    minWidth: 650,
  },
});

export const ListBuilder = ({
  items = [],
  cols = [],
  build = (item, index) => { },
  refresh = () => { },
  addPostAction = () => { },
  collectionName,
  totalItems = 0,
  currentPage = 0,
  rowsPerPage = 10,
  onPageChange = () => { },
}) => {
  const paginated = totalItems > rowsPerPage
  const classes = useStyles();

  if (!items || !items.length) {
    return (
      <RctCollapsibleCard>
        <NoPostsFound refresh={refresh} collection={collectionName} showAdd={false} />
      </RctCollapsibleCard>
    )
  }

  return (
    <Paper>
      <TableContainer component={Paper}>
        <Table className={classes.table} size="small" aria-label="a table">
          <TableHead>
            <TableRow>
              {cols.map(({ label }, index) => (
                <TableCell key={index}>{label}</TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {items && items.map((item, index) => {
              let children = build(item, index)
              return React.cloneElement(children, { key: `ListBuilder-Item-${index + 1}` })
            })}
          </TableBody>
          <TableFooter>

          </TableFooter>
        </Table>
      </TableContainer>
      {/* <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={totalPages}
        rowsPerPage={rowsPerPage}
        page={currentPage}
        onPageChange={onPageChange}
        onRowsPerPageChange={onRowsPerPageChange}
      /> */}
      <SimplePaginationNav
        currentPage={currentPage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        onChange={onPageChange}
      />
    </Paper>
  )
}
