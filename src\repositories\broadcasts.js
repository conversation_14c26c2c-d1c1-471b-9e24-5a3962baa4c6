import { FirestoreRef } from 'FirebaseRef';

export const getBroadcastLogsByContactId = async (messageId, contactId, messageType) => {

    const logs = await FirestoreRef.collection('shotx-cron')
        .doc(messageId).collection('logs')
        .where('contactId', '==', `${contactId}`)
        .orderBy('createdAt', 'asc')
        .get().then(snapshot => {
            const logs = []
            snapshot.forEach(doc => {
                logs.push(doc.data())
            })
            return logs
        })

    if (messageType === 'sniper') {
        const logsByTrigger = logs.reduce((acc, log) => {
            const trigger = log.trigger || 'unknown';
            if (!acc[trigger]) {
                acc[trigger] = [];
            }
            acc[trigger].push(log);
            return acc;
        }, {});

        const filteredLogs = [];
        for (const trigger in logsByTrigger) {
            const triggerLogs = logsByTrigger[trigger];
            const latestLog = triggerLogs.reduce((latest, current) => {
                return (!latest || current.createdAt > latest.createdAt) ? current : latest;
            }, null);

            if (latestLog) {
                filteredLogs.push(latestLog);
            }
        }

        return filteredLogs;
    }

    return logs
}

export const getBroadcastsLastLogAddedByContactId = async (messageId, contactId) => {
    return await FirestoreRef.collection('shotx-cron')
        .doc(messageId).collection('logs')
        .where('contactId', '==', `${contactId}`)
        .orderBy('createdAt', 'desc')
        .limit(1)
        .get().then(snapshot => {
            const logs = []
            snapshot.forEach(doc => {
                logs.push(doc.data())
            })
            return logs
        })
}
