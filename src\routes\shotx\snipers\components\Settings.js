import React, { useEffect } from 'react'

// lang strings
import CONSTANTS from '../../../../constants/AppConstants'
import { langMessages } from '../../../../lang'

import { FormControl, FormControlLabel, FormGroup, InputAdornment, InputLabel, MenuItem, OutlinedInput, Select, Switch, TextField, makeStyles } from '@material-ui/core'

import CompareItens from 'Components/CompareItens'
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'
import { LeadsFields } from 'Models/fields/lead'

export const Settings = ({ state, setState }) => {

  const { SNIPER_APP_BOTS_URL } = CONSTANTS

  const useStyles = makeStyles((theme) => ({
    label: {
      margin: theme.spacing(1),
      justifyContent: 'center',
      alignItems: 'center',
    },
    formControl: {
      margin: theme.spacing(1),
      width: '90%',
    },
    selectEmpty: {
      marginTop: theme.spacing(2),
    },
    alert: {
      width: '90%',
      margin: theme.spacing(1),
      justifyContent: 'start',
      alignItems: 'center',
    },
    compare: {
      width: '90%',
    }
  }));


  const [leadVars, setLeadVars] = React.useState([])

  useEffect(() => {
    if (state['leadVarsAssociation']) {
      setLeadVars(state['leadVarsAssociation'] || [])
    }
  }, [])

  useEffect(() => {
    setAllState(leadVars)
  }, [leadVars])


  const setAllState = (leadVars) => {
    setState({ ...state, ['leadVarsAssociation']: leadVars })
  }

  const handleChange = (prop) => (event) => {
    console.log('handleChange', prop, event.target.value)
    setState({ ...state, [prop]: event.target.value });
  };

  const handleChangeLinkLeadFields = () => {
    setState({ ...state, updateLead: !state.updateLead })
  }

  const classes = useStyles();
  const display = 'inherit';
  const prefix = SNIPER_APP_BOTS_URL || 'https://sniperdev.qi.plus';
  const disabled = false;
  const enumTriggerTypes = [{
    value: 'all',
    label: 'shotx.snipers.types.all'

  },
  {
    value: 'keyword',
    label: 'shotx.snipers.types.keyword'
  }

  ]
  const enumTriggerOperator = [
    {
      value: 'contains',
      label: 'shotx.snipers.operator.contains'
    },
    {
      value: 'equals',
      label: 'shotx.snipers.operator.equals'
    },
    {
      value: 'startsWith',
      label: 'shotx.snipers.operator.startsWith'
    },
    {
      value: 'endsWith',
      label: 'shotx.snipers.operator.endsWith'
    },
  ]

  return (
    <div className="d-flex">
      <div className="flex-cols-1">
        <RctCollapsibleCard
          heading={
            <div>
              <i className="fa fa-cogs"></i> <span className="ml-5">{langMessages['components.settings']}</span>
            </div>
          }
        >
          <FormGroup>
            <div>
              <FormControl className={classes.formControl} variant="outlined" disabled={disabled} /*style={{ 'display': display }}*/>
                <InputLabel htmlFor={`label-public_sniper`}>{langMessages['sniper.botId']}</InputLabel>
                <OutlinedInput
                  autoFocus
                  id={`label-public_sniper`}
                  type={'text'}
                  value={state.publicSniper || ''}
                  onChange={(e) => handleChange('publicSniper')(e)}
                  disabled={disabled}
                  startAdornment={!!prefix ? <InputAdornment position="start">{prefix}</InputAdornment> : null}
                  labelWidth={164}
                />
              </FormControl>


              <FormControl className={classes.formControl} variant="outlined" disabled={disabled} /*style={{ 'display': display }}*/>
                <InputLabel htmlFor={`label-apiKey`}>{langMessages['shotx.snipers.form.apiKey']}</InputLabel>
                <OutlinedInput
                  autoFocus
                  id={`label-apiKey`}
                  type={'text'}
                  value={state.apiKey || ''}
                  onChange={(e) => handleChange('apiKey')(e)}
                  disabled={disabled}
                  required={false}
                  labelWidth={164}
                />
              </FormControl>

              <FormControl variant="outlined" className={classes.formControl} disabled={disabled}>
                <InputLabel id={`label-triggerTypes`}>{langMessages['shotx.snipers.form.trigger']}</InputLabel>
                <Select
                  labelId={`label-triggerTypes`}
                  id={"triggerTypes"}
                  value={state.triggerTypes || ''}
                  onChange={(e) => handleChange('triggerTypes')(e)}
                  disabled={disabled}
                  label={langMessages['shotx.snipers.form.selectTrigger']}
                  defaultValue={enumTriggerTypes[0].value || 'all'}
                  labelWidth={10}
                >
                  {enumTriggerTypes.map((option, index) => (
                    <MenuItem key={`option-${index}`} value={option.value}>
                      {langMessages[`${option.label}`]}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {state.triggerTypes === 'keyword' && (
                <FormGroup variant="outlined">
                  <FormControl variant="outlined" className={classes.formControl} disabled={disabled}>
                    <InputLabel id={`label-triggerOperator`}>{langMessages['shotx.snipers.form.typeTrigger']}</InputLabel>
                    <Select
                      labelId={`label-triggerOperator`}
                      id={"triggerOperator"}
                      value={state.triggerOperator || ''}
                      onChange={(e) => handleChange('triggerOperator')(e)}
                      disabled={disabled}
                      label={langMessages['shotx.snipers.form.selectTypeTrigger']}
                      defaultValue={enumTriggerOperator[0].value}
                      labelWidth={120}
                    >
                      {enumTriggerOperator.map((option, index) => (
                        <MenuItem key={`option-${index}`} value={option.value}>
                          {langMessages[`${option.label}`]}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <TextField
                    variant='outlined'
                    fullWidth

                    id={"triggerValue"}
                    placeholder={langMessages['shotx.snipers.form.keyword']}
                    label={langMessages['shotx.snipers.form.keyword']}
                    defaultValue={state.triggerValue}
                    onChange={(e) => {

                      handleChange('triggerValue')(e)
                    }}

                    className={classes.formControl}
                  />
                </FormGroup>
              )}

              <TextField
                variant='outlined'
                fullWidth

                id={"keywordFinish"}
                placeholder={langMessages['shotx.snipers.form.keywordFinishPlaceholder']}
                label={langMessages['shotx.snipers.form.keywordFinish']}
                defaultValue={state.keywordFinish}
                onChange={(e) => handleChange('keywordFinish')(e)}
                className={classes.formControl}
              />
              <TextField
                variant='outlined'
                fullWidth
                id={"unknownMessage"}
                placeholder={langMessages['shotx.snipers.form.unknownMessagePlaceholder']}
                label={langMessages['shotx.snipers.form.unknownMessage']}
                defaultValue={state.unknownMessage}
                onChange={(e) => handleChange('unknownMessage')(e)}
                className={classes.formControl}
              />
              <FormControlLabel
                className={classes.formControl}
                control={<Switch checked={state.updateLead} onClick={handleChangeLinkLeadFields} name="checkedA" />}
                label="Atualizar dados do lead no final do fluxo?"
              />

              {
                state.updateLead &&
                <CompareItens
                  listFields={LeadsFields}
                  state={leadVars}
                  setState={setLeadVars}
                  fieldToAssociateTitle={langMessages['shotx.whatsapp.associatedLeadFieldSniper']}
                  associatedFieldTitle={langMessages['shotx.whatsapp.associatedFieldSniper']}
                  fieldToAssociateName={'sniper'}
                />
              }
            </div>
          </FormGroup>
        </RctCollapsibleCard>


      </div>

    </div>
  )
}
