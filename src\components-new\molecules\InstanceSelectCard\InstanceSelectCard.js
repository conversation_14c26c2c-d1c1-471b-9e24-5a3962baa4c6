import { langMessages } from "Lang/index"
import { getPlatform } from "Routes/shotx/config"
import React from 'react'
import { Badge } from "reactstrap"

export const InstanceSelectCard = (props) => {
  const { instance, index, ...rest } = props
  const { title, status } = instance
  const state = status.state === 'open' ? 'open' : 'close'
  const platform = getPlatform(instance.platform)
  return (
    <div className="d-flex justify-content-between align-items-center" >
      <platform.logo className="mr-5" style={{ width: '20px' }} />
      <span id='title' style={{ width: '100%' }} className='mr-5 flex flex-row'>
        {title}
      </span>
      <span id='right'>
        <Badge color={state === 'open' ? 'success' : 'warning'}>
          {langMessages[`shotx.statuses.${state}`]}
        </Badge>
      </span>
    </div>
  )
}
