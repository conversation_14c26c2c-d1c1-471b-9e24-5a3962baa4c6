# Sistema de Alertas - Firebase Integration

## Visão Geral

Este documento descreve a implementação completa do sistema de alertas com integração Firebase, incluindo listeners em tempo real e interface de usuário simplificada.

## Arquivos Modificados

### 1. `src/reducers/SidebarReducer.js`

**Modificação:** Removidos dados mock e configurado estado inicial vazio para receber dados do Firebase.

**Estado Inicial:** `deskNotifications: []`

### 2. `src/components/Widgets/DeskNotificationsPopper.js`

**Modificações:**
- Removidos botões de ação (deletar e visualizar)
- Simplificada interface para mostrar apenas título, mensagem e timestamp
- Mantida funcionalidade de fechar popup e marcar como visualizado

### 3. `src/actions/DeskNotificationsActions.js`

**Adições:**
- <PERSON><PERSON> `createTestDeskNotifications()` para criar dados de teste
- Integração completa com Firebase já existente
- Listeners em tempo real funcionando

**Estrutura dos Dados Firebase:**
```javascript
{
  ID: 'string',           // Identificador único
  title: 'string',        // Título do alerta
  message: 'string',      // Mensagem do alerta
  scheduled_date: 'ISO',  // Data/hora em formato ISO
  viewed: boolean,        // Se foi visualizado ou não
  thumbnail: null,        // URL da imagem (opcional)
  url: 'string',         // URL de destino (opcional)
  qiuser: 'string',      // ID do usuário
  owner: 'string',       // ID do proprietário
  created: 'ISO',        // Data de criação
  modified: 'ISO',       // Data de modificação
  locale: 'string',      // Localização
  collection: 'string'   // Nome da coleção
}
```

## Firebase Integration

### Coleção Firebase
- **Nome:** `desktop-notifications`
- **Filtro:** Alertas filtrados por `qiuser` (ID do usuário logado)
- **Listener:** Tempo real com `onSnapshot`
- **Ordenação:** Por data de modificação

### Funcionalidades Implementadas
1. **Listener em Tempo Real:** Alertas são atualizados automaticamente
2. **Filtro por Usuário:** Cada usuário vê apenas seus alertas
3. **Badge de Contagem:** Mostra número de alertas não visualizados
4. **Marcar como Lido:** Ao fechar o popup, alertas são marcados como visualizados
5. **Interface Limpa:** Sem botões de ação, apenas visualização

## Como Testar

### 1. Criar Alertas de Teste
No console do navegador, execute:
```javascript
window.createTestAlerts()
```

### 2. Visualizar Alertas
1. Observe o badge vermelho no ícone de notificações na sidebar
2. Clique no ícone para abrir o popup
3. Veja os alertas com diferentes estados (lido/não lido)
4. Feche o popup para marcar alertas como visualizados

## Componentes Envolvidos

### SidebarContent.js
- Exibe o badge com contagem de alertas não visualizados
- Gerencia o clique para abrir/fechar o popup de notificações

### DeskNotificationsPopper.js
- Renderiza o popup com a lista de alertas
- Gerencia as ações de visualizar, marcar como lido e excluir
- Aplica estilos diferenciados para alertas novos vs visualizados

### SidebarReducer.js
- Armazena o estado dos alertas no Redux
- Contém os dados mock temporários

## Próximos Passos

### Funcionalidades Adicionais
1. **Interface de Administração:** Criar tela para gerenciar alertas
2. **Tipos de Alerta:** Implementar categorização (info, warning, error, success)
3. **Filtros Avançados:** Por data, tipo, status
4. **Notificações Push:** Integrar com service workers
5. **Templates:** Sistema de templates para alertas recorrentes

### Melhorias de UX
1. **Animações:** Transições suaves para novos alertas
2. **Sons:** Notificações sonoras opcionais
3. **Agrupamento:** Agrupar alertas similares
4. **Ações Rápidas:** Botões de ação contextual

## Status Atual

✅ **Implementado:**
- Firebase integration completa
- Listeners em tempo real
- Interface simplificada
- Filtro por usuário
- Badge de contagem
- Marcar como lido

⚠️ **Em Desenvolvimento:**
- Interface de administração
- Criação manual de alertas
- Tipos de alerta

## Estrutura Visual

- **Alertas Novos**: Destacados com classe CSS `new`
- **Badge**: Mostra contagem de alertas não visualizados
- **Timestamp**: Formatado usando moment.js (MOMENT_SHORT)
- **Ações**: Botões para visualizar e excluir cada alerta
- **Scroll**: Lista com scroll automático para muitos alertas

## Arquitetura Técnica

### Fluxo de Dados
1. **Firebase Listener** → `listenDeskNotifications()`
2. **Redux Action** → `LISTEN_DESK_NOTIFICATIONS_SUCCESS`
3. **Redux State** → `sidebarReducer.deskNotifications`
4. **React Component** → `DeskNotificationsPopper`
5. **UI Update** → Badge count + Popup list

### Coleção Firebase: `desktop-notifications`
```
/desktop-notifications/{alertId}
├── ID: string
├── title: string
├── message: string
├── scheduled_date: ISO string
├── viewed: boolean
├── qiuser: string (user ID)
├── owner: string
├── created: ISO string
├── modified: ISO string
├── locale: string
└── collection: string
```

### Redux Actions Disponíveis
- `listenDeskNotifications()` - Listener em tempo real
- `updateDeskNotifications()` - Atualizar múltiplos alertas
- `updateDeskNotification()` - Atualizar alerta individual
- `deleteDeskNotification()` - Deletar alerta
- `createTestDeskNotifications()` - Criar dados de teste

## Troubleshooting

### Alertas não aparecem
1. Verifique se o usuário está logado
2. Execute `window.createTestAlerts()` no console
3. Verifique o console para erros do Firebase
4. Confirme se a coleção `desktop-notifications` existe

### Badge não atualiza
1. Verifique se o listener está ativo
2. Confirme se os alertas têm `viewed: false`
3. Verifique se o `qiuser` corresponde ao usuário logado
