# Sistema de Alertas - Mock Implementation

## Visão Geral

Este documento descreve a implementação temporária de alertas mock no sistema para visualização da estrutura e layout dos alertas na sidebar antes da integração com Firebase.

## Arquivos Modificados

### 1. `src/reducers/SidebarReducer.js`

**Modificação:** Adicionados dados mock no estado inicial do Redux.

**Localização:** Linha 35-63

**Dados Mock Adicionados:**
- **mock-alert-1**: Alerta não visualizado (novo)
- **mock-alert-2**: Alerta já visualizado 
- **mock-alert-3**: Alerta com link externo

**Estrutura dos Dados:**
```javascript
{
  ID: 'string',           // Identificador único
  title: 'string',        // Título do alerta
  message: 'string',      // Mensagem do alerta
  scheduled_date: 'ISO',  // Data/hora em formato ISO
  viewed: boolean,        // Se foi visualizado ou não
  thumbnail: null,        // URL da imagem (opcional)
  url: 'string'          // URL de destino (opcional)
}
```

## Como Visualizar

1. **Badge de Notificação:** Na sidebar, o ícone de notificações mostrará um badge vermelho com o número de alertas não visualizados (2 alertas novos).

2. **Popup de Alertas:** Clique no ícone de notificações na sidebar para abrir o popup com a lista de alertas.

3. **Funcionalidades Disponíveis:**
   - Visualizar alertas não lidos (destacados)
   - Marcar como lido ao clicar em "Ver"
   - Excluir alertas individuais
   - Navegar para URLs internas ou externas

## Componentes Envolvidos

### SidebarContent.js
- Exibe o badge com contagem de alertas não visualizados
- Gerencia o clique para abrir/fechar o popup de notificações

### DeskNotificationsPopper.js
- Renderiza o popup com a lista de alertas
- Gerencia as ações de visualizar, marcar como lido e excluir
- Aplica estilos diferenciados para alertas novos vs visualizados

### SidebarReducer.js
- Armazena o estado dos alertas no Redux
- Contém os dados mock temporários

## Próximos Passos (Firebase Integration)

1. **Remover dados mock** do SidebarReducer.js
2. **Implementar actions** para buscar alertas do Firebase
3. **Configurar listeners** para atualizações em tempo real
4. **Implementar CRUD** completo (criar, atualizar, deletar alertas)
5. **Adicionar filtros** por tipo de alerta, data, etc.

## Observações Importantes

- ⚠️ **TEMPORÁRIO**: Esta implementação é apenas para visualização
- ⚠️ **NÃO USAR EM PRODUÇÃO**: Os dados são estáticos e não persistem
- ⚠️ **REMOVER ANTES DA INTEGRAÇÃO**: Limpar dados mock antes de implementar Firebase

## Estrutura Visual

- **Alertas Novos**: Destacados com classe CSS `new`
- **Badge**: Mostra contagem de alertas não visualizados
- **Timestamp**: Formatado usando moment.js (MOMENT_SHORT)
- **Ações**: Botões para visualizar e excluir cada alerta
- **Scroll**: Lista com scroll automático para muitos alertas

## Testando a Funcionalidade

1. Inicie o servidor de desenvolvimento
2. Faça login no sistema
3. Observe o badge vermelho no ícone de notificações na sidebar
4. Clique no ícone para abrir o popup
5. Teste as funcionalidades de visualizar e excluir alertas
