import { FirebaseRepository } from "FirebaseRef/repository"

export class InstanceService {

    constructor(accountId) {
        this.accountId = accountId
        this.repository = new FirebaseRepository()
        this.path = `shotx/${accountId}/instances`
    }

    listen = (onSuccess, onError) => {
        return this.repository.listenCollection(this.path, onSuccess, onError)
    }

    get = (id) => {
        return this.repository.getDoc(`${this.path}/${id}`)
    }
}
