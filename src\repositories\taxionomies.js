import { TAXONOMIES_COLLECTION_NAME } from 'Constants/AppCollections';
import { FirestoreRef } from 'FirebaseRef';

export const getTaxonomiesLeadsTags = async (accountId) => {

    return await FirestoreRef.collection(TAXONOMIES_COLLECTION_NAME).doc('leads').collection('tags').where('accountId', '==', `${accountId}`).get().then(snapshot => {
        const tags = []
        snapshot.forEach(doc => {
            tags.push(doc.data())
        })
        return tags
    })
}
