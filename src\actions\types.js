/**
 * App Redux Action Types
 */
export const FILTER_TEXT_CHANGE = 'FILTER_TEXT_CHANGE'
export const SEARCH_BAR_ADD_BUTTON = 'SEARCH_BAR_ADD_BUTTON'
export const SEARCH_BAR_SEARCH_BUTTON = 'SEARCH_BAR_SEARCH_BUTTON'
export const COLLAPSED_SIDEBAR = 'COLLAPSED_SIDEBAR'
export const DARK_MODE = 'DARK_MODE'
export const BOXED_LAYOUT = 'BOXED_LAYOUT'
export const RTL_LAYOUT = 'RTL_LAYOUT'
export const MINI_SIDEBAR = 'MINI_SIDEBAR'
export const SEARCH_FORM_ENABLE = 'SEARCH_FORM_ENABLE'
export const CHANGE_THEME_COLOR = 'CHANGE_THEME_COLOR'
export const TOGGLE_SIDEBAR_IMAGE = 'TOGGLE_SIDEBAR_IMAGE'
export const SET_SIDEBAR_IMAGE = 'SET_SIDEBAR_IMAGE'
export const SET_LANGUAGE = 'SET_LANGUAGE'
export const START_USER_TOUR = 'START_USER_TOUR'
export const STOP_USER_TOUR = 'STOP_USER_TOUR'
export const TOGGLE_DARK_SIDENAV = 'TOGGLE_DARK_SIDENAV'
// Chat App Actions
export const CHAT_WITH_SELECTED_USER = 'CHAT_WITH_SELECTED_USER'
export const SEND_MESSAGE_TO_USER = 'SEND_MESSAGE_TO_USER'
export const UPDATE_USERS_SEARCH = 'UPDATE_USERS_SEARCH'
export const SEARCH_USERS = 'SEARCH_USERS'
export const ADD_CHAT_USER = 'ADD_CHAT_USER'
export const ADD_NEW_CHAT_USER = 'ADD_NEW_CHAT_USER'
export const ADD_NEW_CHAT_USER_SUCCESS = 'ADD_NEW_CHAT_USER_SUCCESS'
export const ADD_NEW_CHAT_USER_ERROR = 'ADD_NEW_CHAT_USER_ERROR'
export const ADD_CHAT_GROUP = 'ADD_CHAT_GROUP'
export const ADD_CHAT_GROUP_SUCCESS = 'ADD_CHAT_GROUP_SUCCESS'
export const ADD_CHAT_GROUP_ERROR = 'ADD_CHAT_GROUP_ERROR'
export const LEAVE_CHAT = 'LEAVE_CHAT'
export const LEAVE_CHAT_SUCCESS = 'LEAVE_CHAT_SUCCESS'
export const LEAVE_CHAT_ERROR = 'LEAVE_CHAT_ERROR'
export const LEAVE_GROUP = 'LEAVE_GROUP'
export const LEAVE_GROUP_SUCCESS = 'LEAVE_GROUP_SUCCESS'
export const LEAVE_GROUP_ERROR = 'LEAVE_GROUP_ERROR'
export const DELETE_GROUP = 'DELETE_GROUP'
export const DELETE_GROUP_SUCCESS = 'DELETE_GROUP_SUCCESS'
export const DELETE_GROUP_ERROR = 'DELETE_GROUP_ERROR'
export const GET_CHAT_USERS = 'GET_CHAT_USERS'
export const GET_CHAT_USERS_ERROR = 'GET_CHAT_USERS_ERROR'
export const GET_CHAT_USERS_SUCCESS = 'GET_CHAT_USERS_SUCCESS'
export const GET_GROUPS = 'GET_GROUPS'
export const GET_GROUPS_SUCCESS = 'GET_GROUPS_SUCCESS'
export const GET_GROUPS_FAILURE = 'GET_GROUPS_FAILURE'
export const GET_GROUPS_KEYWORDS = 'GET_GROUPS_KEYWORDS'
export const ADD_CHAT_MESSAGE = 'ADD_CHAT_MESSAGE'
export const ADD_CHAT_MESSAGE_ERROR = 'ADD_CHAT_MESSAGE_ERROR'
export const ADD_CHAT_MESSAGE_SUCCESS = 'ADD_CHAT_MESSAGE_SUCCESS'
export const GET_CHAT_MESSAGES = 'GET_CHAT_MESSAGES'
export const GET_CHAT_MESSAGES_ERROR = 'GET_CHAT_MESSAGES_ERROR'
export const GET_CHAT_MESSAGES_SUCCESS = 'GET_CHAT_MESSAGES_SUCCESS'
export const GET_CHAT_USERS_WP = 'GET_CHAT_USERS_WP'
export const GET_WP_MESSAGES_SUCCESS = 'GET_WP_MESSAGES_SUCCESS'
export const SET_CHAT_ACCOUNT = 'SET_CHAT_ACCOUNT'
// Agency Sidebar
export const AGENCY_TOGGLE_MENU = 'AGENCY_TOGGLE_MENU'
export const CHANGE_AGENCY_LAYOUT_BG = 'CHANGE_AGENCY_LAYOUT_BG'
// Notazz
export const CREATE_NOTAZZ_NF = 'CREATE_NOTAZZ_NF'
export const CREATE_NOTAZZ_NF_SUCCESS = 'CREATE_NOTAZZ_NF_SUCCESS'
export const CREATE_NOTAZZ_NF_FAILURE = 'CREATE_NOTAZZ_NF_FAILURE'
export const GET_NOTAZZ_NF = 'GET_NOTAZZ_NF'
export const GET_NOTAZZ_NF_SUCCESS = 'GET_NOTAZZ_NF_SUCCESS'
export const GET_NOTAZZ_NF_FAILURE = 'GET_NOTAZZ_NF_FAILURE'
// Mail App
export const GET_MAILBOX = 'GET_MAILBOX'
export const GET_MAILBOX_SUCCESS = 'GET_MAILBOX_SUCCESS'
export const GET_MAILBOX_FAILURE = 'GET_MAILBOX_FAILURE'
export const GET_MAILBOXES = 'GET_MAILBOXES'
export const GET_MAILBOXES_SUCCESS = 'GET_MAILBOXES_SUCCESS'
export const GET_MAILBOXES_FAILURE = 'GET_MAILBOXES_FAILURE'
export const SAVE_MAILBOX = 'SAVE_MAILBOX'
export const SAVE_MAILBOX_SUCCESS = 'SAVE_MAILBOX_SUCCESS'
export const SAVE_MAILBOX_FAILURE = 'SAVE_MAILBOX_FAILURE'
export const TRASH_MAILBOX = 'TRASH_MAILBOX'
export const TRASH_MAILBOX_SUCCESS = 'TRASH_MAILBOX_SUCCESS'
export const TRASH_MAILBOX_FAILURE = 'TRASH_MAILBOX_FAILURE'
export const GET_MAILBOX_LABELS = 'GET_MAILBOX_LABELS'
export const GET_MAILBOX_LABELS_SUCCESS = 'GET_MAILBOX_LABELS_SUCCESS'
export const GET_MAILBOX_LABELS_FAILURE = 'GET_MAILBOX_LABELS_FAILURE'
export const GET_MAILBOX_FOLDERS = 'GET_MAILBOX_FOLDERS'
export const GET_MAILBOX_FOLDERS_SUCCESS = 'GET_MAILBOX_FOLDERS_SUCCESS'
export const GET_MAILBOX_FOLDERS_FAILURE = 'GET_MAILBOX_FOLDERS_FAILURE'
export const GET_EMAIL = 'GET_EMAIL'
export const GET_EMAIL_SUCCESS = 'GET_EMAIL_SUCCESS'
export const GET_EMAIL_FAILURE = 'GET_EMAIL_FAILURE'
export const SET_EMAIL_AS_STAR = 'SET_EMAIL_AS_STAR'
export const OPEN_EMAIL = 'OPEN_EMAIL'
export const HIDE_LOADING_INDICATOR = 'HIDE_LOADING_INDICATOR'
export const ON_SELECT_EMAIL = 'ON_SELECT_EMAIL'
export const UPDATE_EMAIL_SEARCH = 'UPDATE_EMAIL_SEARCH'
export const SEARCH_EMAIL = 'SEARCH_EMAIL'
export const ON_TRASH_EMAIL = 'ON_TRASH_EMAIL'
export const ON_TRASH_EMAIL_SUCCESS = 'ON_TRASH_EMAIL_SUCCESS'
export const ON_TRASH_EMAIL_FAILURE = 'ON_TRASH_EMAIL_FAILURE'
export const ON_DELETE_MAIL = 'ON_DELETE_MAIL'
export const ON_DELETE_MAIL_SUCCESS = 'ON_DELETE_MAIL_SUCCESS'
export const ON_DELETE_MAIL_FAILURE = 'ON_DELETE_MAIL_FAILURE'
export const ON_BACK_PRESS_NAVIGATE_TO_EMAIL_LISTING = 'ON_BACK_PRESS_NAVIGATE_TO_EMAIL_LISTING'
export const GET_FOLDER_EMAILS = 'GET_FOLDER_EMAILS'
export const GET_FOLDER_EMAILS_SUCCESS = 'GET_FOLDER_EMAILS_SUCCESS'
export const GET_FOLDER_EMAILS_FAILURE = 'GET_FOLDER_EMAILS_FAILURE'
export const ON_SELECT_MAILBOX = 'ON_SELECT_MAILBOX'
export const ON_CHANGE_PAGE = 'ON_CHANGE_PAGE'
export const ON_SELECT_FOLDER = 'ON_SELECT_FOLDER'
export const ON_EMAIL_MOVE_TO_FOLDER = 'ON_EMAIL_MOVE_TO_FOLDER'
export const ON_EMAIL_MOVE_TO_FOLDER_SUCCESS = 'ON_EMAIL_MOVE_TO_FOLDER_SUCCESS'
export const ON_EMAIL_MOVE_TO_FOLDER_FAILURE = 'ON_EMAIL_MOVE_TO_FOLDER_FAILURE'
export const SELECT_ALL_EMAILS = 'SELECT_ALL_EMAILS'
export const UNSELECT_ALL_EMAILS = 'UNSELECT_ALL_EMAILS'
export const ON_SAVE_EMAIL = 'ON_SAVE_EMAIL'
export const ON_SAVE_EMAIL_SUCCESS = 'ON_SAVE_EMAIL_SUCCESS'
export const ON_SAVE_EMAIL_FAILURE = 'ON_SAVE_EMAIL_FAILURE'
export const ON_SEND_EMAIL = 'ON_SEND_EMAIL'
export const ON_SEND_EMAIL_SUCCESS = 'ON_SEND_EMAIL_SUCCESS'
export const ON_SEND_EMAIL_FAILURE = 'ON_SEND_EMAIL_FAILURE'
export const EMAIL_SENT_SUCCESSFULLY = 'EMAIL_SENT_SUCCESSFULLY'
export const FILTER_EMAILS_WITH_LABELS = 'FILTER_EMAILS_WITH_LABELS'
export const RESET_EMAILS_FILTERS = 'RESET_EMAILS_FILTERS'
export const RESET_FOLDER_EMAILS = 'RESET_FOLDER_EMAILS'
export const ADD_LABELS_INTO_EMAILS = 'ADD_LABELS_INTO_EMAILS'
export const ON_GMAIL_AUTH_SUCCESS = 'ON_GMAIL_AUTH_SUCCESS'
export const ON_GMAIL_AUTH_ERROR = 'ON_GMAIL_AUTH_ERROR'
export const ON_GMAIL_SIGN_OUT = 'ON_GMAIL_SIGN_OUT'
export const GAPI_CLIENT_READY = 'GAPI_CLIENT_READY'
export const GAPI_CLIENT_ERROR = 'GAPI_CLIENT_ERROR'
export const GAPI_AUTH2_READY = 'GAPI_AUTH2_READY'
export const LIST_GMAIL_FOLDER_SUCCESS = 'LIST_GMAIL_FOLDER_SUCCESS'
export const SAVE_GMAIL_DRAFT = 'SAVE_GMAIL_DRAFT'
export const SAVE_GMAIL_DRAFT_SUCCESS = 'SAVE_GMAIL_DRAFT_SUCCESS'
export const SAVE_GMAIL_DRAFT_FAILURE = 'SAVE_GMAIL_DRAFT_FAILURE'
export const MSA_CLIENT_READY = 'MSA_CLIENT_READY'
export const MSA_CLIENT_ERROR = 'MSA_CLIENT_ERROR'
export const MSA_AUTH2_READY = 'MSA_AUTH2_READY'
export const ON_MSA_AUTH_SUCCESS = 'ON_MSA_AUTH_SUCCESS'
export const ON_MSA_AUTH_ERROR = 'ON_MSA_AUTH_ERROR'
export const ON_MSA_SIGN_OUT = 'ON_MSA_SIGN_OUT'
export const LIST_MSA_FOLDER_SUCCESS = 'LIST_MSA_FOLDER_SUCCESS'
export const GET_MSA_ACCOUNTS = 'GET_MSA_ACCOUNTS'
export const GET_MSA_ACCOUNT = 'GET_MSA_ACCOUNT'
export const SAVE_MSA_DRAFT = 'SAVE_MSA_DRAFT'
export const SAVE_MSA_DRAFT_SUCCESS = 'SAVE_MSA_DRAFT_SUCCESS'
export const SAVE_MSA_DRAFT_FAILURE = 'SAVE_MSA_DRAFT_FAILURE'
export const ON_SMTP_TEST_SUCCESS = 'ON_SMTP_TEST_SUCCESS'
export const ON_SMTP_TEST_ERROR = 'ON_SMTP_TEST_ERROR'
export const ON_IMAP_AUTH_SUCCESS = 'ON_IMAP_AUTH_SUCCESS'
export const ON_IMAP_AUTH_ERROR = 'ON_IMAP_AUTH_ERROR'
export const SAVE_IMAP_DRAFT = 'SAVE_IMAP_DRAFT'
export const SAVE_IMAP_DRAFT_SUCCESS = 'SAVE_IMAP_DRAFT_SUCCESS'
export const SAVE_IMAP_DRAFT_FAILURE = 'SAVE_IMAP_DRAFT_FAILURE'

// Firebase
export const FIRESTORE_ERROR = 'FIRESTORE_ERROR'
export const RESET_FIRESTORE_ERROR = 'RESET_FIRESTORE_ERROR'
export const MODULE_NOT_AVAILABLE = 'MODULE_NOT_AVAILABLE'
export const MODULE_LIMIT_REACHED = 'MODULE_LIMIT_REACHED'

// sidebar
export const TOGGLE_MENU = 'TOGGLE_MENU'

// ToDo App
export const GET_TODOS = 'GET_TODOS'
export const FETCH_TODOS = 'FETCH_TODOS'
export const ADD_NEW_TASK = 'ADD_NEW_TASK'
export const ON_SELECT_TODO = 'ON_SELECT_TODO'
export const ON_HIDE_LOADER = 'ON_HIDE_LOADER'
export const ON_BACK_TO_TODOS = 'ON_BACK_TO_TODOS'
export const ON_SHOW_LOADER = 'ON_SHOW_LOADER'
export const MARK_AS_STAR_TODO = 'MARK_AS_STAR_TODO'
export const DELETE_TODO = 'DELETE_TODO'
export const ADD_LABELS_INTO_THE_TASK = 'ADD_LABELS_INTO_THE_TASK'
export const GET_ALL_TODO = 'GET_ALL_TODO'
export const GET_COMPLETED_TODOS = 'GET_COMPLETED_TODOS'
export const GET_DELETED_TODOS = 'GET_DELETED_TODOS'
export const GET_STARRED_TODOS = 'GET_STARRED_TODOS'
export const GET_FILTER_TODOS = 'GET_FILTER_TODOS'
export const CLOSE_SNACKBAR = 'CLOSE_SNACKBAR'
export const COMPLETE_TASK = 'COMPLETE_TASK'
export const UPDATE_TASK_TITLE = 'UPDATE_TASK_TITLE'
export const UPDATE_TASK_DESCRIPTION = 'UPDATE_TASK_DESCRIPTION'
export const CHANGE_TASK_ASSIGNER = 'CHANGE_TASK_ASSIGNER'
export const ON_CHECK_BOX_TOGGLE_TODO_ITEM = 'ON_CHECK_BOX_TOGGLE_TODO_ITEM'
export const SELECT_ALL_TODO = 'SELECT_ALL_TODO'
export const GET_UNSELECTED_ALL_TODO = 'GET_UNSELECTED_ALL_TODO'
export const SELECT_STARRED_TODO = 'SELECT_STARRED_TODO'
export const SELECT_UNSTARRED_TODO = 'SELECT_UNSTARRED_TODO'
export const ON_LABEL_SELECT = 'ON_LABEL_SELECT'
export const ON_LABEL_MENU_ITEM_SELECT = 'ON_LABEL_MENU_ITEM_SELECT'
export const UPDATE_SEARCH = 'UPDATE_SEARCH'
export const SEARCH_TODO = 'SEARCH_TODO'
// Auth Actions
export const LOGIN_USER = 'LOGIN_USER'
export const LOGIN_USER_SUCCESS = 'LOGIN_USER_SUCCESS'
export const LOGIN_USER_FAILURE = 'LOGIN_USER_FAILURE'
export const AUTH_USER = 'AUTH_USER'
export const AUTH_USER_SUCCESS = 'AUTH_USER_SUCCESS'
export const AUTH_USER_FAILURE = 'AUTH_USER_FAILURE'
export const UPDATE_USER = 'UPDATE_USER'
export const VERIFY_USER = 'VERIFY_USER'
export const VERIFY_USER_SUCCESS = 'VERIFY_USER_SUCCESS'
export const VERIFY_USER_FAILURE = 'VERIFY_USER_FAILURE'
export const VERIFY_ACCOUNT = 'VERIFY_ACCOUNT'
export const VERIFY_ACCOUNT_SUCCESS = 'VERIFY_ACCOUNT_SUCCESS'
export const VERIFY_ACCOUNT_FAILURE = 'VERIFY_ACCOUNT_FAILURE'
export const INACTIVE_ACCOUNT_ALERT = 'INACTIVE_ACCOUNT_ALERT'
export const GET_ACCOUNT = 'GET_ACCOUNT'
export const GET_ACCOUNT_SUCCESS = 'GET_ACCOUNT_SUCCESS'
export const GET_ACCOUNT_FAILURE = 'GET_ACCOUNT_FAILURE'
export const ADD_BILLING_PLAN = 'ADD_BILLING_PLAN'
export const ADD_BILLING_PLAN_SUCCESS = 'ADD_BILLING_PLAN_SUCCESS'
export const ADD_BILLING_PLAN_FAILURE = 'ADD_BILLING_PLAN_FAILURE'
export const GET_BILLING_PLANS = 'GET_BILLING_PLANS'
export const GET_BILLING_PLANS_SUCCESS = 'GET_BILLING_PLANS_SUCCESS'
export const GET_BILLING_PLANS_FAILURE = 'GET_BILLING_PLANS_FAILURE'
export const ADD_SUBSCRIPTION = 'ADD_SUBSCRIPTION'
export const ADD_SUBSCRIPTION_SUCCESS = 'ADD_SUBSCRIPTION_SUCCESS'
export const ADD_SUBSCRIPTION_FAILURE = 'ADD_SUBSCRIPTION_FAILURE'
export const CREATE_TRANSACTION = 'CREATE_TRANSACTION'
export const CREATE_TRANSACTION_SUCCESS = 'CREATE_TRANSACTION_SUCCESS'
export const CREATE_TRANSACTION_FAILURE = 'CREATE_TRANSACTION_FAILURE'
export const GET_TRANSACTION = 'GET_TRANSACTION'
export const GET_TRANSACTION_SUCCESS = 'GET_TRANSACTION_SUCCESS'
export const GET_TRANSACTION_FAILURE = 'GET_TRANSACTION_FAILURE'
export const CREATE_SUBSCRIPTION = 'CREATE_SUBSCRIPTION'
export const CREATE_SUBSCRIPTION_SUCCESS = 'CREATE_SUBSCRIPTION_SUCCESS'
export const CREATE_SUBSCRIPTION_FAILURE = 'CREATE_SUBSCRIPTION_FAILURE'
export const GET_SUBSCRIPTION = 'GET_SUBSCRIPTION'
export const GET_SUBSCRIPTION_SUCCESS = 'GET_SUBSCRIPTION_SUCCESS'
export const GET_SUBSCRIPTION_FAILURE = 'GET_SUBSCRIPTION_FAILURE'
export const CANCEL_SUBSCRIPTION = 'CANCEL_SUBSCRIPTION'
export const CANCEL_SUBSCRIPTION_SUCCESS = 'CANCEL_SUBSCRIPTION_SUCCESS'
export const CANCEL_SUBSCRIPTION_FAILURE = 'CANCEL_SUBSCRIPTION_FAILURE'
export const SAVE_NEW_ACCOUNT = 'SAVE_NEW_ACCOUNT'
export const SAVE_NEW_ACCOUNT_FAILURE = 'SAVE_NEW_ACCOUNT_FAILURE'
export const SAVE_NEW_ACCOUNT_SUCCESS = 'SAVE_NEW_ACCOUNT_SUCCESS'
export const SAVE_ACCOUNT = 'SAVE_ACCOUNT'
export const SAVE_ACCOUNT_FAILURE = 'SAVE_ACCOUNT_FAILURE'
export const SAVE_ACCOUNT_SUCCESS = 'SAVE_ACCOUNT_SUCCESS'
export const SAVE_ACCOUNT_END = 'SAVE_ACCOUNT_END'
export const PASSWORD_RESET = 'PASSWORD_RESET'
export const PASSWORD_RESET_SUCCESS = 'PASSWORD_RESET_SUCCESS'
export const PASSWORD_RESET_FAILURE = 'PASSWORD_RESET_FAILURE'
export const RESET_SUCCESS_MSG = 'RESET_SUCCESS_MSG'
export const RESET_ERROR_MSG = 'RESET_ERROR_MSG'
export const LOGOUT_USER = 'LOGOUT_USER'
export const SIGNUP_USER = 'SIGNUP_USER'
export const SIGNUP_USER_SUCCESS = 'SIGNUP_USER_SUCCESS'
export const SIGNUP_USER_FAILURE = 'SIGNUP_USER_FAILURE'
export const GET_AUTH_USER = 'GET_AUTH_USER'
export const GET_AUTH_USER_SUCCESS = 'GET_AUTH_USER_SUCCESS'
export const GET_AUTH_USER_ERROR = 'GET_AUTH_USER_ERROR'
export const GET_ACCOUNT_OWNER = 'GET_ACCOUNT_OWNER'
export const GET_ACCOUNT_OWNER_SUCCESS = 'GET_ACCOUNT_OWNER_SUCCESS'
export const GET_ACCOUNT_OWNER_FAILURE = 'GET_ACCOUNT_OWNER_FAILURE'
export const GET_PENDING_UPDATES_SUCCESS = 'GET_PENDING_UPDATES_SUCCESS'
export const UPDATE_ACCOUNT_OWNER = 'UPDATE_ACCOUNT_OWNER'
// Logs Actions
export const COUNT_LOGS = 'COUNT_LOGS'
export const COUNT_LOGS_SUCCESS = 'COUNT_LOGS_SUCCESS'
export const COUNT_LOGS_FAILURE = 'COUNT_LOGS_FAILURE'
export const GET_POSTS_STATS = 'GET_POSTS_STATS'
export const GET_POSTS_STATS_SUCCESS = 'GET_POSTS_STATS_SUCCESS'
export const GET_POSTS_STATS_FAILURE = 'GET_POSTS_STATS_FAILURE'
export const GET_LOGS = 'GET_LOGS'
export const GET_LOGS_SUCCESS = 'GET_LOGS_SUCCESS'
export const GET_LOGS_FAILURE = 'GET_LOGS_FAILURE'
export const LISTEN_LOGS = 'LISTEN_LOGS'
export const LISTEN_LOGS_SUCCESS = 'LISTEN_LOGS_SUCCESS'
export const LISTEN_LOGS_FAILURE = 'LISTEN_LOGS_FAILURE'
export const SAVE_NEW_LOG = 'SAVE_NEW_LOG'
export const SAVE_NEW_LOG_SUCCESS = 'SAVE_NEW_LOG_SUCCESS'
export const SAVE_NEW_LOG_FAILURE = 'SAVE_NEW_LOG_FAILURE'
// Owner Actions
export const GET_OWNERS = 'GET_OWNERS'
export const GET_OWNERS_SUCCESS = 'GET_OWNERS_SUCCESS'
export const GET_OWNERS_FAILURE = 'GET_OWNERS_FAILURE'
export const GET_OWNER = 'GET_OWNER'
export const GET_OWNER_SUCCESS = 'GET_OWNER_SUCCESS'
export const GET_OWNER_FAILURE = 'GET_OWNER_FAILURE'
// QIUser Actions
export const GET_QIUSERS = 'GET_QIUSERS'
export const GET_QIUSERS_SUCCESS = 'GET_QIUSERS_SUCCESS'
export const GET_QIUSERS_FAILURE = 'GET_QIUSERS_FAILURE'
export const GET_QIUSERS_KEYWORDS = 'GET_QIUSERS_KEYWORDS'
export const GET_QIUSER = 'GET_QIUSER'
export const GET_QIUSER_SUCCESS = 'GET_QIUSER_SUCCESS'
export const GET_QIUSER_FAILURE = 'GET_QIUSER_FAILURE'
export const SAVE_NEW_QIUSER = 'SAVE_NEW_QIUSER'
export const SAVE_NEW_QIUSER_FAILURE = 'SAVE_NEW_QIUSER_FAILURE'
export const SAVE_NEW_QIUSER_SUCCESS = 'SAVE_NEW_QIUSER_SUCCESS'
export const UPDATE_QIUSERS = 'UPDATE_QIUSERS'
export const UPDATE_QIUSER = 'UPDATE_QIUSER'
export const SAVE_QIUSER = 'SAVE_QIUSER'
export const SAVE_QIUSER_SUCCESS = 'SAVE_QIUSER_SUCCESS'
export const SAVE_QIUSER_FAILURE = 'SAVE_QIUSER_FAILURE'
export const DELETE_QIUSER = 'DELETE_QIUSER'
export const DELETE_QIUSER_SUCCESS = 'DELETE_QIUSER_SUCCESS'
export const DELETE_QIUSER_FAILURE = 'DELETE_QIUSER_FAILURE'
export const TRASH_QIUSER = 'TRASH_QIUSER'
export const TRASH_QIUSER_SUCCESS = 'TRASH_QIUSER_SUCCESS'
export const TRASH_QIUSER_FAILURE = 'TRASH_QIUSER_FAILURE'
export const GET_QIUSER_ID = 'GET_QIUSER_ID'
export const GET_QIUSER_ID_SUCCESS = 'GET_QIUSER_ID_SUCCESS'
export const GET_QIUSER_ID_FAILURE = 'GET_QIUSER_ID_FAILURE'
export const GET_CURRENT_QIUSER = 'GET_CURRENT_QIUSER'
export const GET_CURRENT_QIUSER_SUCCESS = 'GET_CURRENT_QIUSER_SUCCESS'
export const GET_CURRENT_QIUSER_FAILURE = 'GET_CURRENT_QIUSER_FAILURE'
export const UPDATE_QIUSER_ID = 'UPDATE_QIUSER_ID'
// QIUser Actions
export const GET_AFFILIATES = 'GET_AFFILIATES'
export const GET_AFFILIATES_SUCCESS = 'GET_AFFILIATES_SUCCESS'
export const GET_AFFILIATES_FAILURE = 'GET_AFFILIATES_FAILURE'
export const GET_AFFILIATES_KEYWORDS = 'GET_AFFILIATES_KEYWORDS'
export const GET_AFFILIATE = 'GET_AFFILIATE'
export const GET_AFFILIATE_SUCCESS = 'GET_AFFILIATE_SUCCESS'
export const GET_AFFILIATE_FAILURE = 'GET_AFFILIATE_FAILURE'
export const SAVE_NEW_AFFILIATE = 'SAVE_NEW_AFFILIATE'
export const SAVE_NEW_AFFILIATE_FAILURE = 'SAVE_NEW_AFFILIATE_FAILURE'
export const SAVE_NEW_AFFILIATE_SUCCESS = 'SAVE_NEW_AFFILIATE_SUCCESS'
export const UPDATE_AFFILIATES = 'UPDATE_AFFILIATES'
export const UPDATE_AFFILIATE = 'UPDATE_AFFILIATE'
export const SAVE_AFFILIATE = 'SAVE_AFFILIATE'
export const SAVE_AFFILIATE_SUCCESS = 'SAVE_AFFILIATE_SUCCESS'
export const SAVE_AFFILIATE_FAILURE = 'SAVE_AFFILIATE_FAILURE'
export const DELETE_AFFILIATE = 'DELETE_AFFILIATE'
export const DELETE_AFFILIATE_SUCCESS = 'DELETE_AFFILIATE_SUCCESS'
export const DELETE_AFFILIATE_FAILURE = 'DELETE_AFFILIATE_FAILURE'
export const TRASH_AFFILIATE = 'TRASH_AFFILIATE'
export const TRASH_AFFILIATE_SUCCESS = 'TRASH_AFFILIATE_SUCCESS'
export const TRASH_AFFILIATE_FAILURE = 'TRASH_AFFILIATE_FAILURE'
export const GET_AFFILIATE_ID = 'GET_AFFILIATE_ID'
export const GET_AFFILIATE_ID_SUCCESS = 'GET_AFFILIATE_ID_SUCCESS'
export const GET_AFFILIATE_ID_FAILURE = 'GET_AFFILIATE_ID_FAILURE'
export const GET_CURRENT_AFFILIATE = 'GET_CURRENT_AFFILIATE'
export const GET_CURRENT_AFFILIATE_SUCCESS = 'GET_CURRENT_AFFILIATE_SUCCESS'
export const GET_CURRENT_AFFILIATE_FAILURE = 'GET_CURRENT_AFFILIATE_FAILURE'
export const UPDATE_AFFILIATE_ID = 'UPDATE_AFFILIATE_ID'
// Feedbacks
export const GET_FEEDBACKS = 'GET_FEEDBACKS'
export const GET_FEEDBACKS_SUCCESS = 'GET_FEEDBACKS_SUCCESS'
export const GET_ALL_FEEDBACKS = 'GET_ALL_FEEDBACKS'
export const ON_CHANGE_FEEDBACK_PAGE_TABS = 'ON_CHANGE_FEEDBACK_PAGE_TABS'
export const MAKE_FAVORITE_FEEDBACK = 'MAKE_FAVORITE_FEEDBACK'
export const ON_DELETE_FEEDBACK = 'ON_DELETE_FEEDBACK'
export const VIEW_FEEDBACK_DETAILS = 'VIEW_FEEDBACK_DETAILS'
export const ADD_NEW_FEEDBACK = 'ADD_NEW_FEEDBACK'
export const SHOW_FEEDBACK_LOADING_INDICATOR = 'SHOW_FEEDBACK_LOADING_INDICATOR'
export const HIDE_FEEDBACK_LOADING_INDICATOR = 'HIDE_FEEDBACK_LOADING_INDICATOR'
export const NAVIGATE_TO_BACK = 'NAVIGATE_TO_BACK'
export const REPLY_FEEDBACK = 'REPLY_FEEDBACK'
export const SEND_REPLY = 'SEND_REPLY'
export const UPDATE_SEARCH_IDEA = 'UPDATE_SEARCH_IDEA'
export const ON_SEARCH_IDEA = 'ON_SEARCH_IDEA'
export const ON_COMMENT_FEEDBACK = 'ON_COMMENT_FEEDBACK'
// ecommerce
export const ON_DELETE_ITEM_FROM_CART = 'ON_DELETE_ITEM_FROM_CART'
export const ON_QUANTITY_CHANGE = 'ON_QUANTITY_CHANGE'
export const ON_ADD_ITEM_TO_CART = 'ON_ADD_ITEM_TO_CART'
//crm
export const ADD_NEW_CLIENT = 'ADD_NEW_CLIENT'
export const DELETE_CLIENT = 'DELETE_CLIENT'
export const UPDATE_CLIENT = 'UPDATE_CLIENT'
//Dialogs
export const OPEN_DIALOG = 'OPEN_DIALOG'
export const CLOSE_DIALOG = 'CLOSE_DIALOG'
export const DIALOG_RESULTS = 'DIALOG_RESULTS'
//Posts
export const GET_POSTS = 'GET_POSTS'
export const GET_POSTS_SUCCESS = 'GET_POSTS_SUCCESS'
export const GET_POSTS_FAILURE = 'GET_POSTS_FAILURE'
export const LISTEN_POSTS = 'LISTEN_POSTS'
export const LISTEN_POSTS_SUCCESS = 'LISTEN_POSTS_SUCCESS'
export const LISTEN_POSTS_FAILURE = 'LISTEN_POSTS_FAILURE'
export const LISTEN_COLLECTION_INDEX = 'LISTEN_COLLECTION_INDEX'
export const LISTEN_COLLECTION_INDEX_SUCCESS = 'LISTEN_COLLECTION_INDEX_SUCCESS'
export const LISTEN_COLLECTION_INDEX_FAILURE = 'LISTEN_COLLECTION_INDEX_FAILURE'
export const GET_POST = 'GET_POST'
export const GET_POST_SUCCESS = 'GET_POST_SUCCESS'
export const GET_POST_FAILURE = 'GET_POST_FAILURE'
export const LISTEN_POST = 'LISTEN_POST'
export const LISTEN_POST_SUCCESS = 'LISTEN_POST_SUCCESS'
export const LISTEN_POST_FAILURE = 'LISTEN_POST_FAILURE'
export const SAVE_NEW_POST = 'SAVE_NEW_POST'
export const SAVE_NEW_POST_SUCCESS = 'SAVE_NEW_POST_SUCCESS'
export const SAVE_NEW_POST_FAILURE = 'SAVE_NEW_POST_FAILURE'
export const SAVE_POST = 'SAVE_POST'
export const SAVE_POST_SUCCESS = 'SAVE_POST_SUCCESS'
export const SAVE_POST_FAILURE = 'SAVE_POST_FAILURE'
export const SAVE_POST_FIELDS = 'SAVE_POST_FIELDS'
export const SAVE_POST_FIELDS_SUCCESS = 'SAVE_POST_FIELDS_SUCCESS'
export const SAVE_POST_FIELDS_FAILURE = 'SAVE_POST_FIELDS_FAILURE'
export const SAVE_DOCUMENT_FIELDS = 'SAVE_DOCUMENT_FIELDS'
export const SAVE_DOCUMENT_FIELDS_SUCCESS = 'SAVE_DOCUMENT_FIELDS_SUCCESS'
export const SAVE_DOCUMENT_FIELDS_FAILURE = 'SAVE_DOCUMENT_FIELDS_FAILURE'
export const UPDATE_POSTS = 'UPDATE_POSTS'
export const UPDATE_POST = 'UPDATE_POST'
export const DELETE_POST = 'DELETE_POST'
export const DELETE_POST_SUCCESS = 'DELETE_POST_SUCCESS'
export const DELETE_POST_FAILURE = 'DELETE_POST_FAILURE'
export const TRASH_POST = 'TRASH_POST'
export const TRASH_POST_SUCCESS = 'TRASH_POST_SUCCESS'
export const TRASH_POST_FAILURE = 'TRASH_POST_FAILURE'
//Posts
export const GET_TAXONOMIES = 'GET_TAXONOMIES'
export const GET_TAXONOMIES_SUCCESS = 'GET_TAXONOMIES_SUCCESS'
export const GET_TAXONOMIES_FAILURE = 'GET_TAXONOMIES_FAILURE'
export const GET_TAXONOMIES_KEYWORDS = 'GET_TAXONOMIES_KEYWORDS'
export const LISTEN_TAXONOMIES = 'LISTEN_TAXONOMIES'
export const LISTEN_TAXONOMIES_SUCCESS = 'LISTEN_TAXONOMIES_SUCCESS'
export const LISTEN_TAXONOMIES_FAILURE = 'LISTEN_TAXONOMIES_FAILURE'
export const GET_TAXONOMY = 'GET_TAXONOMY'
export const GET_TAXONOMY_SUCCESS = 'GET_TAXONOMY_SUCCESS'
export const GET_TAXONOMY_FAILURE = 'GET_TAXONOMY_FAILURE'
export const SAVE_NEW_TAXONOMY = 'SAVE_NEW_TAXONOMY'
export const SAVE_NEW_TAXONOMY_SUCCESS = 'SAVE_NEW_TAXONOMY_SUCCESS'
export const SAVE_NEW_TAXONOMY_FAILURE = 'SAVE_NEW_TAXONOMY_FAILURE'
export const SAVE_TAXONOMY = 'SAVE_TAXONOMY'
export const SAVE_TAXONOMY_SUCCESS = 'SAVE_TAXONOMY_SUCCESS'
export const SAVE_TAXONOMY_FAILURE = 'SAVE_TAXONOMY_FAILURE'
export const UPDATE_TAXONOMIES = 'UPDATE_TAXONOMIES'
export const UPDATE_TAXONOMY = 'UPDATE_TAXONOMY'
export const DELETE_TAXONOMY = 'DELETE_TAXONOMY'
//Events
export const GET_EVENT_PARTICIPANTS = 'GET_EVENT_PARTICIPANTS'
export const GET_EVENT_PARTICIPANTS_SUCCESS = 'GET_EVENT_PARTICIPANTS_SUCCESS'
export const GET_EVENT_PARTICIPANTS_FAILURE = 'GET_EVENT_PARTICIPANTS_FAILURE'
export const ADD_EVENT_PARTICIPANT = 'ADD_EVENT_PARTICIPANT'
export const ADD_EVENT_PARTICIPANT_SUCCESS = 'ADD_EVENT_PARTICIPANT_SUCCESS'
export const ADD_EVENT_PARTICIPANT_FAILURE = 'ADD_EVENT_PARTICIPANT_FAILURE'
export const DELETE_EVENT_PARTICIPANT = 'DELETE_EVENT_PARTICIPANT'
export const DELETE_EVENT_PARTICIPANT_SUCCESS = 'DELETE_EVENT_PARTICIPANT_SUCCESS'
export const DELETE_EVENT_PARTICIPANT_FAILURE = 'DELETE_EVENT_PARTICIPANT_FAILURE'
export const CONFIRM_EVENT_PARTICIPANT = 'CONFIRM_EVENT_PARTICIPANT'
export const CONFIRM_EVENT_PARTICIPANT_SUCCESS = 'CONFIRM_EVENT_PARTICIPANT_SUCCESS'
export const CONFIRM_EVENT_PARTICIPANT_FAILURE = 'CONFIRM_EVENT_PARTICIPANT_FAILURE'
export const DISCONFIRM_EVENT_PARTICIPANT = 'DISCONFIRM_EVENT_PARTICIPANT'
export const DISCONFIRM_EVENT_PARTICIPANT_SUCCESS = 'DISCONFIRM_EVENT_PARTICIPANT_SUCCESS'
export const DISCONFIRM_EVENT_PARTICIPANT_FAILURE = 'DISCONFIRM_EVENT_PARTICIPANT_FAILURE'
export const CHECKIN_EVENT_PARTICIPANT = 'CHECKIN_EVENT_PARTICIPANT'
export const CHECKIN_EVENT_PARTICIPANT_SUCCESS = 'CHECKIN_EVENT_PARTICIPANT_SUCCESS'
export const CHECKIN_EVENT_PARTICIPANT_FAILURE = 'CHECKIN_EVENT_PARTICIPANT_FAILURE'
export const UNDO_CHECKIN_EVENT_PARTICIPANT = 'UNDO_CHECKIN_EVENT_PARTICIPANT'
export const UNDO_CHECKIN_EVENT_PARTICIPANT_SUCCESS = 'UNDO_CHECKIN_EVENT_PARTICIPANT_SUCCESS'
export const UNDO_CHECKIN_EVENT_PARTICIPANT_FAILURE = 'UNDO_CHECKIN_EVENT_PARTICIPANT_FAILURE'

//Campaigns
export const GET_CAMPAIGNS_PARTICIPANTS = 'GET_CAMPAIGNS_PARTICIPANTS'
export const GET_CAMPAIGNS_PARTICIPANTS_SUCCESS = 'GET_CAMPAIGNS_PARTICIPANTS_SUCCESS'
export const GET_CAMPAIGNS_PARTICIPANTS_FAILURE = 'GET_CAMPAIGNS_PARTICIPANTS_FAILURE'
export const GET_CAMPAIGN_PARTICIPANTS = 'GET_CAMPAIGN_PARTICIPANTS'
export const GET_CAMPAIGN_PARTICIPANTS_SUCCESS = 'GET_CAMPAIGN_PARTICIPANTS_SUCCESS'
export const GET_CAMPAIGN_PARTICIPANTS_FAILURE = 'GET_CAMPAIGN_PARTICIPANTS_FAILURE'

// Deals
export const SAVE_NEW_DEAL = 'SAVE_NEW_DEAL'
export const SAVE_NEW_DEAL_FAILURE = 'SAVE_NEW_DEAL_FAILURE'
export const SAVE_NEW_DEAL_SUCCESS = 'SAVE_NEW_DEAL_SUCCESS'
export const SAVE_DEAL = 'SAVE_DEAL'
export const SAVE_DEAL_SUCCESS = 'SAVE_DEAL_SUCCESS'
export const SAVE_DEAL_FAILURE = 'SAVE_DEAL_FAILURE'
export const SAVE_DEAL_FIELDS = 'SAVE_DEAL_FIELDS'
export const SAVE_DEAL_FIELDS_SUCCESS = 'SAVE_DEAL_FIELDS_SUCCESS'
export const SAVE_DEAL_FIELDS_FAILURE = 'SAVE_DEAL_FIELDS_FAILURE'
// Leads
export const GET_LEADS = 'GET_LEADS'
export const GET_LEADS_SUCCESS = 'GET_LEADS_SUCCESS'
export const GET_LEADS_FAILURE = 'GET_LEADS_FAILURE'
export const GET_LEADS_KEYWORDS = 'GET_LEADS_KEYWORDS'
export const GET_LEAD = 'GET_LEAD'
export const GET_LEAD_SUCCESS = 'GET_LEAD_SUCCESS'
export const GET_LEAD_FAILURE = 'GET_LEAD_FAILURE'
export const SAVE_NEW_LEAD = 'SAVE_NEW_LEAD'
export const SAVE_NEW_LEAD_FAILURE = 'SAVE_NEW_LEAD_FAILURE'
export const SAVE_NEW_LEAD_SUCCESS = 'SAVE_NEW_LEAD_SUCCESS'
export const UPDATE_LEADS = 'UPDATE_LEADS'
export const UPDATE_LEAD = 'UPDATE_LEAD'
export const LEAD_MODAL_OPEN = 'LEAD_MODAL_OPEN'
export const LEAD_MODAL_CLOSE = 'LEAD_MODAL_CLOSE'
export const SAVE_LEAD = 'SAVE_LEAD'
export const SAVE_LEAD_SUCCESS = 'SAVE_LEAD_SUCCESS'
export const SAVE_LEAD_FAILURE = 'SAVE_LEAD_FAILURE'
export const SAVE_LEAD_FIELDS = 'SAVE_LEAD_FIELDS'
export const SAVE_LEAD_FIELDS_SUCCESS = 'SAVE_LEAD_FIELDS_SUCCESS'
export const SAVE_LEAD_FIELDS_FAILURE = 'SAVE_LEAD_FIELDS_FAILURE'
export const DELETE_LEAD = 'DELETE_LEAD'
export const DELETE_LEAD_SUCCESS = 'DELETE_LEAD_SUCCESS'
export const DELETE_LEAD_FAILURE = 'DELETE_LEAD_FAILURE'
export const TRASH_LEAD = 'TRASH_LEAD'
export const TRASH_LEAD_SUCCESS = 'TRASH_LEAD_SUCCESS'
export const TRASH_LEAD_FAILURE = 'TRASH_LEAD_FAILURE'
// Calendar
export const GET_CALENDAR_EVENTS = 'GET_CALENDAR_EVENTS'
export const GET_CALENDAR_EVENTS_SUCCESS = 'GET_CALENDAR_EVENTS_SUCCESS'
export const GET_CALENDAR_EVENTS_FAILURE = 'GET_CALENDAR_EVENTS_FAILURE'
export const LISTEN_CALENDAR_EVENTS = 'LISTEN_CALENDAR_EVENTS'
export const LISTEN_CALENDAR_EVENTS_SUCCESS = 'LISTEN_CALENDAR_EVENTS_SUCCESS'
export const LISTEN_CALENDAR_EVENTS_FAILURE = 'LISTEN_CALENDAR_EVENTS_FAILURE'
export const GET_CALENDAR_EVENT = 'GET_CALENDAR_EVENT'
export const GET_CALENDAR_EVENT_SUCCESS = 'GET_CALENDAR_EVENT_SUCCESS'
export const GET_CALENDAR_EVENT_FAILURE = 'GET_CALENDAR_EVENT_FAILURE'
export const ADD_CALENDAR_EVENT = 'ADD_CALENDAR_EVENT'
export const SAVE_NEW_CALENDAR_EVENT = 'SAVE_NEW_CALENDAR_EVENT'
export const SAVE_NEW_CALENDAR_EVENT_SUCCESS = 'SAVE_NEW_CALENDAR_EVENT_SUCCESS'
export const SAVE_NEW_CALENDAR_EVENT_FAILURE = 'SAVE_NEW_CALENDAR_EVENT_FAILURE'
export const SAVE_CALENDAR_EVENT = 'SAVE_CALENDAR_EVENT'
export const SAVE_CALENDAR_EVENT_SUCCESS = 'SAVE_CALENDAR_EVENT_SUCCESS'
export const SAVE_CALENDAR_EVENT_FAILURE = 'SAVE_CALENDAR_EVENT_FAILURE'
export const UPDATE_CALENDAR_EVENT = 'UPDATE_CALENDAR_EVENT'
export const UPDATE_CALENDAR_EVENTS = 'UPDATE_CALENDAR_EVENTS'
export const DELETE_CALENDAR_EVENT = 'DELETE_CALENDAR_EVENT'
export const DELETE_CALENDAR_EVENT_SUCCESS = 'DELETE_CALENDAR_EVENT_SUCCESS'
export const DELETE_CALENDAR_EVENT_FAILURE = 'DELETE_CALENDAR_EVENT_FAILURE'
// Sellers
export const GET_SELLERS = 'GET_SELLERS'
export const GET_SELLERS_SUCCESS = 'GET_SELLERS_SUCCESS'
export const GET_SELLERS_FAILURE = 'GET_SELLERS_FAILURE'
// Managers
export const GET_MANAGERS = 'GET_MANAGERS'
export const GET_MANAGERS_SUCCESS = 'GET_MANAGERS_SUCCESS'
export const GET_MANAGERS_FAILURE = 'GET_MANAGERS_FAILURE'
// Emails
export const GET_MAILING = 'GET_MAILING'
export const GET_MAILING_SUCCESS = 'GET_MAILING_SUCCESS'
export const GET_MAILING_FAILURE = 'GET_MAILING_FAILURE'
export const SEND_MAILING = 'SEND_MAILING'
export const SEND_MAILING_SUCCESS = 'SEND_MAILING_SUCCESS'
export const SEND_MAILING_FAILURE = 'SEND_MAILING_FAILURE'
export const CONFIRM_BROADCAST_MAILING = 'CONFIRM_BROADCAST_MAILING'
export const ON_SEND_BROADCAST_MAILING = 'ON_SEND_BROADCAST_MAILING'
export const MISSING_BROADCAST_CONFIRMATION = 'MISSING_BROADCAST_CONFIRMATION'
export const RESET_MAILING_ERROR = 'RESET_MAILING_ERROR'
export const DELETE_MAILING = 'DELETE_MAILING'
export const DELETE_MAILING_SUCCESS = 'DELETE_MAILING_SUCCESS'
export const DELETE_MAILING_FAILURE = 'DELETE_MAILING_FAILURE'
// App Notifications
export const GET_DESK_NOTIFICATIONS = 'GET_DESK_NOTIFICATIONS'
export const GET_DESK_NOTIFICATIONS_SUCCESS = 'GET_DESK_NOTIFICATIONS_SUCCESS'
export const GET_DESK_NOTIFICATIONS_FAILURE = 'GET_DESK_NOTIFICATIONS_FAILURE'
export const LISTEN_DESK_NOTIFICATIONS = 'LISTEN_DESK_NOTIFICATIONS'
export const LISTEN_DESK_NOTIFICATIONS_SUCCESS = 'LISTEN_DESK_NOTIFICATIONS_SUCCESS'
export const LISTEN_DESK_NOTIFICATIONS_FAILURE = 'LISTEN_DESK_NOTIFICATIONS_FAILURE'
export const GET_DESK_NOTIFICATION = 'GET_DESK_NOTIFICATION'
export const GET_DESK_NOTIFICATION_SUCCESS = 'GET_DESK_NOTIFICATION_SUCCESS'
export const GET_DESK_NOTIFICATION_FAILURE = 'GET_DESK_NOTIFICATION_FAILURE'
export const UPDATE_DESK_NOTIFICATION = 'UPDATE_DESK_NOTIFICATION'
export const UPDATE_DESK_NOTIFICATION_SUCCESS = 'UPDATE_DESK_NOTIFICATION_SUCCESS'
export const UPDATE_DESK_NOTIFICATION_FAILURE = 'UPDATE_DESK_NOTIFICATION_FAILURE'
export const UPDATE_DESK_NOTIFICATIONS = 'UPDATE_DESK_NOTIFICATIONS'
export const UPDATE_DESK_NOTIFICATIONS_SUCCESS = 'UPDATE_DESK_NOTIFICATIONS_SUCCESS'
export const UPDATE_DESK_NOTIFICATIONS_FAILURE = 'UPDATE_DESK_NOTIFICATIONS_FAILURE'
export const TOGGLE_DESK_NOTIFICATIONS = 'TOGGLE_DESK_NOTIFICATIONS'
export const DELETE_DESK_NOTIFICATION = 'DELETE_DESK_NOTIFICATION'
export const DELETE_DESK_NOTIFICATION_SUCCESS = 'DELETE_DESK_NOTIFICATION_SUCCESS'
export const DELETE_DESK_NOTIFICATION_FAILURE = 'DELETE_DESK_NOTIFICATION_FAILURE'
// Forms
export const GET_CUSTOM_FIELDS = 'GET_CUSTOM_FIELDS'
export const GET_CUSTOM_FIELDS_SUCCESS = 'GET_CUSTOM_FIELDS_SUCCESS'
export const GET_CUSTOM_FIELDS_FAILURE = 'GET_CUSTOM_FIELDS_FAILURE'
export const GET_CUSTOM_FIELD = 'GET_CUSTOM_FIELD'
export const GET_CUSTOM_FIELD_SUCCESS = 'GET_CUSTOM_FIELD_SUCCESS'
export const GET_CUSTOM_FIELD_FAILURE = 'GET_CUSTOM_FIELD_FAILURE'
export const ADD_CUSTOM_FIELD = 'ADD_CUSTOM_FIELD'
export const ADD_CUSTOM_FIELD_SUCCESS = 'ADD_CUSTOM_FIELD_SUCCESS'
export const ADD_CUSTOM_FIELD_FAILURE = 'ADD_CUSTOM_FIELD_FAILURE'
export const DELETE_CUSTOM_FIELD = 'DELETE_CUSTOM_FIELD'
export const UPDATE_CUSTOM_FIELD = 'UPDATE_CUSTOM_FIELD'
export const UPDATE_CUSTOM_FIELDS = 'UPDATE_CUSTOM_FIELDS'
// Landing-Pages
export const ADD_LANDING_PAGE = 'ADD_LANDING_PAGE'
export const ADD_LANDING_PAGE_SUCCESS = 'ADD_LANDING_PAGE_SUCCESS'
export const ADD_LANDING_PAGE_FAILURE = 'ADD_LANDING_PAGE_FAILURE'
export const UPDATE_LANDING_PAGE = 'UPDATE_LANDING_PAGE'
export const UPDATE_LANDING_PAGE_SUCCESS = 'UPDATE_LANDING_PAGE_SUCCESS'
export const UPDATE_LANDING_PAGE_FAILURE = 'UPDATE_LANDING_PAGE_FAILURE'

export const UPDATE_CONTACT = 'UPDATE_CONTACT'
