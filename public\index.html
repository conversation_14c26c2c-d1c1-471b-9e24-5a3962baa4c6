<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no" />
  <meta name="theme-color" content="#000000" />
  <!--
      manifest.json provides metadata used when your web app is added to the
      homescreen on Android. See https://developers.google.com/web/fundamentals/engage-and-retain/web-app-manifest/
    -->
  <link rel="shortcut icon" href="favicon.ico" />
  <!-- Cache -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <!-- Use For Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
  <!-- Use For Material Iconic Font -->
  <link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/material-design-iconic-font/2.2.0/css/material-design-iconic-font.min.css" />
  <!-- Use For Simple Line Icon -->
  <link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.css" />
  <!-- Use For Leaflet Map -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.1.0/leaflet.css" />
  <!-- Use For FontAwesome Icons -->
  <link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"
    integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous" />
  <!-- Use For Jvector Map -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jvectormap/2.0.4/jquery-jvectormap.css"
    type="text/css" media="screen" />
  <!-- Use For Google Font -->
  <link href="https://fonts.googleapis.com/css?family=Heebo:100,300,400,500,700,800,900" rel="stylesheet" />


  <!-- Use For GrapesJs -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/grapesjs/0.18.4/css/grapes.min.css" rel="stylesheet" />
  <!-- <link href="https://unpkg.com/grapesjs-preset-newsletter@0.2.21/dist/grapesjs-preset-newsletter.css"
    rel="stylesheet" /> -->

  <script src="https://cdnjs.cloudflare.com/ajax/libs/grapesjs/0.18.4/grapes.min.js"></script>
  <script src="https://unpkg.com/grapesjs-preset-newsletter@1.0.2/dist/index.js"></script>

  <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
  <title>QIPLUS || Transforme seus clientes em fãs</title>

  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=UA-*********-1"></script>
  <script>
    window.dataLayer = window.dataLayer || []
    function gtag() {
      dataLayer.push(arguments)
    }
    gtag('js', new Date())
    gtag('config', 'UA-*********-1')
  </script>
</head>

<body>
  <script>
    window.fbAsyncInit = function () {
      FB.init({
        appId: '***************',
        xfbml: true,
        version: 'v19.0',
      })
      if (window.location.hostname !== 'localhost') {
        FB.AppEvents.logPageView()
      }
    }
      ; (function (d, s, id) {
        var js,
          fjs = d.getElementsByTagName(s)[0]
        if (d.getElementById(id)) {
          return
        }
        js = d.createElement(s)
        js.id = id
        js.src = 'https://connect.facebook.net/en_US/sdk.js'
        fjs.parentNode.insertBefore(js, fjs)
      })(document, 'script', 'facebook-jssdk')
  </script>
  <script>
    function jsonClone(obj) {
      if (!obj || typeof obj !== 'object') return obj
      try {
        var clone = JSON.parse(JSON.stringify(obj))
        Object.keys(obj).forEach((key, i, keys) => {
          if (obj[key] && typeof obj[key] === 'object') {
            clone[key] = jsonClone(obj[key])
          } else if (typeof obj[key] === 'function') {
            clone[key] = obj[key]
          }
        })
        return clone
      } catch (err) {
        return obj
      }
    }
    JSON.clone = jsonClone
  </script>
  <noscript> You need to enable JavaScript to run this app. </noscript>
  <div id="root"></div>
  <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
</body>

</html>
