import { FirebaseRepository } from "FirebaseRef/repository"
import { axiosChatService } from "Routes/chat/API/axiosChatService"
import moment from "moment"

export class SniperService {

    constructor(accountId) {
        this.accountId = accountId
        this.repository = new FirebaseRepository()
        this.path = `shotx/${accountId}/snipers`
    }

    listen = (onSuccess, onError) => {
        return this.repository.listenCollection(this.path, onSuccess, onError)
    }

    get = (id, onSuccess, onError) => {
        const where = [['id', '==', id]]
        return this.repository.getCollection(this.path, onSuccess, onError, '', '', where)
    }

    getAll = (onSuccess, onError) => {

        return this.repository.getCollection(this.path, onSuccess, onError)
    }

    create = async (sniper) => {

        sniper.createdAt = moment().unix()
        sniper.updatedAt = moment().unix()
        sniper.accountId = this.accountId

        return this.repository.addDoc(this.path, sniper)
            .then(result => {
                const { id } = result
                return this.update(id, { id })
                    .then(() => {
                        return { ...sniper, id }
                    })
                    .catch(err => {
                        console.error(err)
                        return sniper
                    })
            })
            .catch(err => {
                console.error("err create", err)
                return sniper
            })
    }

    update = async (id, value) => {
        if (!id) {
            console.error('sniper id is required')
            return
        }

        value.updatedAt = moment().unix()

        return this.repository.setDoc(`${this.path}/${id}`, value)

    }

    delete = async (id) => {
        return this.repository.deleteDoc(`${this.path}/${id}`)
    }

    getSniperAccountData = async (body) => {
        return await axiosChatService.post(`/sniper/getSniperAccountData`, body)
    }

    getSniperToImport = async (data) => {
        return await axiosChatService.post(`/sniper/getSniper`, data)
    }
}
