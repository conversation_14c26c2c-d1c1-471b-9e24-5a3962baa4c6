import { Card, CardContent, Typography } from '@material-ui/core';
import { langMessages } from 'Lang/index';
import React from 'react';
import { Bar, Bar<PERSON>hart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { formatCurrency } from 'Util/country/money';

const PlanTotalsChart = ({ subscriptions = [] }) => {
  const COLORS = { paid: '#4caf50', unpaid: '#ff9800', canceled: '#f44336' }

  const plansXstatusXvalue = subscriptions.reduce((acc, { plan, status }) => {
    if (!acc[plan.name]) {
      acc[plan.name] = {
        paid: 0,
        unpaid: 0,
        canceled: 0
      }
    }
    acc[plan.name][status] += plan.amount / 100
    return acc
  }, {})

  const plansData = Object.entries(plansXstatusXvalue).map(([name, values]) => ({
    name,
    paid: values.paid,
    unpaid: values.unpaid,
    canceled: values.canceled
  }))

  return (
    <Card className="h-96">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {langMessages['subscription.overview.plansTotals']}
        </Typography>
        <ResponsiveContainer width={"100%"} height={300}>
          <BarChart
            data={plansData}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <YAxis tickFormatter={formatCurrency} />
            <XAxis dataKey="name" />
            <Tooltip
              contentStyle={{ backgroundColor: 'transparent', border: 'none' }}
              itemStyle={{ backgroundColor: 'transparent', border: 'none' }}
              cursor={{ fill: 'transparent' }}
              formatter={formatCurrency}
            />
            <Bar dataKey="paid" stackId={"a"} name={langMessages['subscription.paid']} fill="#4caf50" />
            <Bar dataKey="unpaid" stackId={"a"} name={langMessages['subscription.unpaid']} fill="#ff9800" />
            <Bar dataKey="canceled" stackId={"a"} name={langMessages['subscription.canceled']} fill="#f44336" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default PlanTotalsChart;
