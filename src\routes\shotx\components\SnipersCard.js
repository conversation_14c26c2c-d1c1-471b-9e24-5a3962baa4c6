import { GridCard, GridCardRow } from 'Components/index'
import { MOMENT_LOCAL } from 'Constants/AppConstants'
import { getTheDate } from 'Helpers'
import { langMessages } from 'Lang/index'
import React from 'react'

export const SnipersCard = ({ snipers, onEdit, onDelete, collection }) => {
    const { id, title, keywordFinish, public_sniper, triggerOperator, triggerTypes, triggerValue, unknownMessage, createdAt, updatedAt } = snipers

    const handleTypeSanitize = () => {
        let types
        let operator
        switch (triggerTypes) {
            case 'keyword':
                types = langMessages['shotx.snipers.types.keyword'] //'Palavras-Chave'
                break;

            default:
                types = langMessages['shotx.snipers.types.all']
                break;
        }

        if (triggerTypes == 'all') return types


        switch (triggerOperator) {
            case 'contains':
                operator = langMessages['shotx.snipers.operator.contains']
                break;
            case 'equals':
                operator = langMessages['shotx.snipers.operator.equals']
                break;
            case 'startsWith':
                operator = langMessages['shotx.snipers.operator.startsWith']
                break;
            case 'endsWith':
                operator = langMessages['shotx.snipers.operator.endsWith']
                break;

            default:
                operator = langMessages['shotx.snipers.types.all']
                break;
        }

        return types + " / " + operator
    }

    return <GridCard
        id={id}
        title={title || public_sniper}
        contents={[

            <GridCardRow
                childs={[
                    {
                        title: langMessages['shotx.snipers.form.keywordFinish'],
                        subtitle: keywordFinish,
                    },
                    {
                        title: langMessages['shotx.snipers.form.unknownMessage'],
                        subtitle: unknownMessage,
                    }
                ]}
            />,
            <GridCardRow
                childs={[
                    {
                        title: langMessages['shotx.snipers.typeOperador'],
                        subtitle: handleTypeSanitize(),
                    },
                    {
                        title: langMessages['shotx.snipers.value'],
                        subtitle: triggerValue,
                    }
                ]}
            />,
            <GridCardRow
                childs={[
                    {
                        title: langMessages['widgets.dateCreated'],
                        subtitle: getTheDate(createdAt, MOMENT_LOCAL),
                    },
                    {
                        title: langMessages['dates.modified'],
                        subtitle: getTheDate(updatedAt, MOMENT_LOCAL),
                    }
                ]}
            />,
        ]}
        footer={{
            title: "footer",
            collection,
            //actions,
            onEdit,
            onDelete,
            deleteMessage: langMessages['shotx.dialogs.willBeDeleted'],
        }}
    />
}
