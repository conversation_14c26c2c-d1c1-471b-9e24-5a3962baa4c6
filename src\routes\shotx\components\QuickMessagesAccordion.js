import { Accordion, AccordionDetails, AccordionSummary } from "@material-ui/core"
import RctCollapsibleCard from "Components/RctCollapsibleCard/RctCollapsibleCard"
import ShortcodesMap from "Constants/ShortcodesMap"
import { langMessages } from "Lang/index"
import React, { useState } from "react"

export const QuickMessagesAccordion = (props) => {
  const [editorShortcodes, setEditorShortcodes] = useState(window.jsonClone(ShortcodesMap))


  const createPanels = () => {
    const panels = []

    editorShortcodes.forEach(shortCode => {
      const { group, name } = shortCode
      let panel = panels.find(p => p.group === group)
      if (!panel) {
        panel = {
          group,
          label: langMessages[`forms.${group}`] || langMessages[`modules.${group}`] || group,
          fields: [name],
        }
        panels.push(panel)
      } else {
        panel.fields.push(name)
      }
    })
    return panels
  }

  const [expanded, setExpanded] = useState(-1)

  const panels = createPanels()



  return (
    <div className="col-sm-12 col-xl-4">
      <RctCollapsibleCard
        headingClasses={'rct-block-title-sm'}
        contentClasses={'p-0'}
        customClasses="form-fields-block"
        heading={langMessages['forms.personalize']}
      >
        {panels.map((panel, p) => {
          const { label, fields, group } = panel
          return (
            <Accordion
              expanded={expanded === p}
              onChange={e => (expanded === p ? setExpanded(-1) : setExpanded(p))}
              key={p}
              classes={{ expanded: 'my-5' }}
            >
              <AccordionSummary classes={{ root: 'text-dark' }} expandIcon={<i className="zmdi zmdi-chevron-down text-dark"></i>}>
                <span className="">{label}</span>
              </AccordionSummary>
              <AccordionDetails classes={{ root: 'p-10 bg-light-gray' }}>
                <div className="d-flex flex-column w-100">
                  {editorShortcodes
                    .filter(s => s.group === group && fields.indexOf(s.name) !== -1)
                    .map((shortCode, k) => {
                      const { shortcode, label, shortcodeField, shortcodeTip } = shortCode
                      return (
                        <div
                          key={k}
                          className={`qi-icon-button alert text-white mb-5 bg-dark`}
                          role="alert"
                          onClick={e => props.onClick(shortCode)}
                        >
                          <span className="alert-addon">
                            <i className={`fa fa-plus font-md`}></i>
                          </span>
                          <p>{label || shortcodeField}</p>
                          {shortcodeTip && <small className="tip">{shortcodeTip}</small>}
                        </div>
                      )
                    })}
                </div>
              </AccordionDetails>
            </Accordion>
          )
        })}
      </RctCollapsibleCard>
    </div>
  )
}
