import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  IconButton,
  makeStyles,
  Switch,
  Typography
} from '@material-ui/core';
import { getTheDateRefact } from 'Helpers';
import { langMessages } from 'Lang/index';
import { ChartColumnIcon, Code, CopyPlusIcon, Edit2Icon, Trash } from 'lucide-react';
import React from 'react';
import { HunterEvents } from '../model/HunterEvents';

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    padding: theme.spacing(2),
  },
  card: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  cardContent: {
    flexGrow: 1,
  },
  events: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: theme.spacing(1),
  },
  details: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: theme.spacing(1),
  },
  actions: {
    justifyContent: 'flex-end',
  },
}));

const PixelGrid = ({ pixels, onEdit, onDelete, onToggle, onViewCode, onClone, onStats }) => {
  const classes = useStyles();

  const shortenUrl = (url) => {
    if (!url) return url;
    if (url.length > 40) return url.substring(0, 40) + '...';
    return url;
  };

  return (
    <Grid container spacing={3} className={classes.root}>
      {pixels.map((pixel) => (
        <Grid item xs={12} sm={6} md={4} key={pixel.id}>
          <Card className={classes.card}>
            <CardHeader
              title={pixel.name}
              titleTypographyProps={{
                variant: 'h6',
              }}
              action={
                <Switch
                  checked={pixel.active}
                  onChange={() => onToggle(pixel.id, !pixel.active)}
                  title={langMessages['campaigns.hunter.statusToggle']}
                  size="small"
                />
              }
            />
            <CardContent className={classes.cardContent}>

              <div className={classes.events}>
                <Typography variant="body2">
                  {langMessages['campaigns.hunter.domain']}: <br />
                  {shortenUrl(pixel.domain)}
                </Typography>
              </div>

              <div className={classes.events}>
                <Typography variant="body2">
                  {langMessages['campaigns.hunter.events.title']}: <br />
                  {pixel.events.length > 0 ?
                    Object.keys(HunterEvents)
                      .filter((event) => pixel.events.includes(event))
                      .map((event) => HunterEvents[event].label)
                      .join(', ')
                    :
                    langMessages['campaigns.hunter.noEvents']
                  }
                </Typography>
              </div>

              <div className={classes.details}>
                <Typography variant="body2">
                  {langMessages['campaigns.hunter.createdAt']}:<br />{getTheDateRefact(pixel.createdAt, langMessages['format.date.full'])}
                </Typography>
                <Typography variant="body2">
                  {langMessages['campaigns.hunter.updatedAt']}:<br /> {getTheDateRefact(pixel.updatedAt, langMessages['format.date.full'])}
                </Typography>
                <Typography variant="body2">
                  {langMessages['campaigns.hunter.status']}:<br /> {pixel.active ? langMessages['campaigns.hunter.statusEnabled'] : langMessages['campaigns.hunter.statusDisabled']}
                </Typography>
              </div>
            </CardContent>
            <Divider />
            <CardActions className={classes.actions}>
              {!!pixel.events.length && (
                <IconButton size="small" onClick={() => onViewCode(pixel)} title={langMessages['campaigns.hunter.code']}>
                  <Code size={16} />
                </IconButton>
              )}
              <IconButton size="small" onClick={() => onStats(pixel)} title={langMessages['campaigns.hunter.stats']}>
                <ChartColumnIcon size={16} />
              </IconButton>
              <IconButton size="small" onClick={() => onClone(pixel)} title={langMessages['campaigns.hunter.duplicate']}>
                <CopyPlusIcon size={16} />
              </IconButton>
              <IconButton size="small" onClick={() => onEdit(pixel)} title={langMessages['campaigns.hunter.edit']}>
                <Edit2Icon size={16} />
              </IconButton>
              <IconButton size="small" onClick={() => onDelete(pixel.id)} title={langMessages['campaigns.hunter.delete']}>
                <Trash size={16} />
              </IconButton>
            </CardActions>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default PixelGrid;
