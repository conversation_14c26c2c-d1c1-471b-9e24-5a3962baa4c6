import { CHAT_APP_URL } from 'Constants/AppConstants';
import { openWindow } from "Util/Dialog";
export function instagramLogout() {
  return new Promise(res => {
    FB.getLoginStatus(function (response) {
      console.log('FB > logoutStatus > response', response)
      if (response.authResponse !== null) {
        FB.logout(function (response) {
          // user is now logged out
          console.log('FB > logout > response', response)
          return res(true)
        })
      } else {
        return res(true)
      }
    })
  })
}

export function instagramLogin(accountId, instanceId, callback) {
  return openWindow({
    url: `${CHAT_APP_URL}/instagram/auth/login/${accountId}/${instanceId}`,
    name: 'Instagram Login',
    // onClose: () => {
    //   const service = new InstanceService(accountId)
    //   service.get(instanceId).then(instance => {
    //     callback && callback({
    //       ...instance,
    //       status: instance.status.state,
    //       access_token: instance.auth.longToken
    //     })
    //     return true
    //   })
    // }
  })
}

