import { Button, ButtonGroup, makeStyles, withStyles } from '@material-ui/core';
import MuiDialogTitle from '@material-ui/core/DialogTitle';
import { PageTitleBar } from 'Components/index';
import React from 'react';
const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});

const useStyles = makeStyles((theme) => ({
  label: {
    margin: theme.spacing(1),
    justifyContent: 'center',
    alignItems: 'center',
  },
  formControl: {
    margin: theme.spacing(1),
    width: '90%',
  },
  selectEmpty: {
    marginTop: theme.spacing(2),
  },
}));

const DialogTitle = withStyles(styles)((props) => {

  const match = {
    path: '/dashboard'
  }
  const { children, classes, onChange, onClose, onNext, title, disableNext, isLoading, nextText, buttonActiveColor, inputs, ...other } = props;

  return (
    <MuiDialogTitle disableTypography className={classes.root} {...other}>
      <PageTitleBar
        title={title}
        match={match}
        rightComponents={
          <ButtonNavBar {...props} />
        }
      />
    </MuiDialogTitle>
  );
});

const ButtonNavBar = (props) => {
  const { onClose, backText, nextText, onNext } = props

  return (
    <div className={'d-flex justify-content-between align-items-center'}>
      <ButtonGroup color="primary" >
        <Button disableElevation variant={'outlined'} className={'border-primary text-primary'} onClick={onClose}>
          <i className={`ti-write mr-5`}></i>
          <span>
            {backText}
          </span>
        </Button>

        <Button disableElevation variant={'contained'} className={'bg-primary text-white'} onClick={onNext}>
          <i className={`ti-save mr-5`}></i>
          <span>
            {
              nextText
            }
          </span>
        </Button>
      </ButtonGroup>
    </div>
  )
}

export const DialogDefaultTitle = (props) => {
  return (
    <DialogTitle
      id="shotx-dialog-title"
      {...props}
    />

  )
}
