import { Loading } from 'Components/Widgets/components/Loading'
import { GridList } from 'Components/index'
import { SHOTX_SNIPERS_COLLECTION_NAME } from 'Constants/AppCollections'
import { langMessages } from 'Lang/index'
import React, { useContext, useEffect, useState } from 'react'
import { withRouter } from 'react-router-dom'
import { Alert } from 'reactstrap'
import { ImportSnipersCard } from '../components/ImportSnipersCard'
import { ModuleContext } from '../contexts/ModuleContext'

const collectionName = SHOTX_SNIPERS_COLLECTION_NAME

const ImportSnipers = ({ match, history }) => {

    const {
        setAttribute,
        getSniperAccountData,
        getSniperToImport,
        getApiKeys
    } = useContext(ModuleContext)
    const [loading, setLoading] = useState(false)
    const [items, setItems] = useState([])
    const [hasSniperTokens, setHasSniperTokens] = useState(false)

    const handleValidateApiKeys = async () => {
        const keys = await getApiKeys()
        if (keys?.sniperTokens?.length > 0) setHasSniperTokens(true)
    }


    useEffect(() => {
        setAttribute({ title: langMessages['shotx.tabs.ImportSniper'], showSearchBar: false })
        handleValidateApiKeys()
        getSniperAccount()
    }, [])

    const getSniperAccount = async () => {
        setLoading(true)
        const retorno = await getSniperAccountData()
        setItems(retorno?.data?.data || [])
        setLoading(false)
    }

    const validUrl = () => {
        const lastIndex = match.url.length - 1
        if (match.url[lastIndex] === '/') {
            return match.url
        } else {
            return `${match.url}/`
        }
    }


    const onImportClick = ({ sniper, token }) => {
        const dataGetPublic = {
            "privateSniperId": sniper.id,
            "apiKey": token
        }


        getPublicId(dataGetPublic).then((res) => {
            const publicId = res?.data?.data?.sniper?.publicId || ''

            return history.push(`${validUrl()}snipers/add?public_id=${publicId}&name=${sniper.name}&key=${token}`)
        }
        ).catch(
            console.log("Error")
        )
    }

    const getPublicId = async (dataGetPublic) => {
        return await getSniperToImport(dataGetPublic)
    }

    return (
        <div className="projects-wrapper">
            {!hasSniperTokens ? (
                <div className="projects-wrapper">
                    <Alert className="mb-15" fade={false} color="warning" isOpen={true}>
                        {langMessages['shotx.snipers.noApiKeyToken']}
                    </Alert>
                </div>
            ) : (
                <Loading loading={loading}>
                    <GridList
                        items={items || []}
                        flexDirection="col"
                        collectionName={collectionName}
                        build={(sniper) => {
                            return <ImportSnipersCard
                                collection={collectionName}
                                snipersKeys={sniper}
                                onImportClick={onImportClick}

                            />
                        }}
                    />
                </Loading>
            )}


        </div>
    )
}

export default withRouter(ImportSnipers)
