import { ChatAP<PERSON> } from 'Routes/chat/API/chat'
import { instagramLogin } from '../../../lib/instagram'
import { LogoInstagram } from "../logos/instagram"
import { createPlatform } from "./Platform"
const platformId = 'instagram'
const platformName = 'Instagram'
const useSocket = false
const state = {
  instanceName: '',
  number: '',
  pushName: '',
  status: '',
}

const create = async ({ values, dependences }) => {
  const { user, account } = dependences
  console.log('create', values)
  const instance = {
    ...values.instance,
    userId: user.accountId,
    accountId: account.accountId,
    platform: values.platform.name
  }

  return await ChatAPI.createInstance(instance)
}

const update = async (instance) => {
  return ChatAPI.updateInstance(instance)
  // return ShotXRepository.updateInstance(instance).catch((error) => console.log(error))
}

const connect = async (instance) => {
  const { accountId, id } = instance

  const callback = (result) => {
    return { ...result, state: result.status, error: result.error, message: result.message }
  }

  instagramLogin(accountId, id, callback)
}

const disconnect = async (instance) => {
  if (!instance) return

  ChatAPI.logoutIgAccount(instance)
    .then(res => {
      console.log(res)
    })
    .catch(err => {
      console.log(err)
    })
}

const messages = {
  onConnect: 'shotx.instance.connectedSuccessfully',
  onDisconnect: 'shotx.instance.disconnectedSuccessfully',
  onRefused: 'shotx.instagram.onRefused',
}

const platform = {
  id: platformId,
  name: platformName,
  logo: LogoInstagram,
  useSocket,
  state,
  create,
  update,
  connect,
  disconnect,
  hasInteractions: true,
  showModalOnConnect: false,
  showModalOnDisconnect: false,
  messages,
  events: [{ name: 'comments', label: 'texts.comments' }, { name: 'messages', label: "widgets.messages" }],
  createInfo: 'shotx.instagram.instance.create.info',
}

export const Instagram = createPlatform(platform)
