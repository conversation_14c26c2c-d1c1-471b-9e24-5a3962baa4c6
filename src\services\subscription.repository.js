import COLLECTIONS from "Constants/AppCollections";
import { FirebaseRepository } from "FirebaseRef/repository";

export class SubscriptionRepository {
    collection = COLLECTIONS.SUBSCRIPTIONS_COLLECTION_NAME;

    constructor(accountId) {
        this.accountId = accountId
        this.repository = new FirebaseRepository()
        this.path = `${this.collection}`
    }

    getDocs = async (where, orderBy, order, limit) => {
        if (!this.path) {
            console.error('Path is required')
            return null
        }
        return this.repository.getDocs(`${this.path}`, where, orderBy, order, limit)
    }

    findActive = async () => {
        if (!this.accountId) {
            return null
        }

        const accounts = await this.getDocs(
            [
                ['accountId', '==', this.accountId],
                ['status', 'in', ['paid', 'trialing', 'active']],
            ], '', '', 1)

        if (accounts.length > 0) {
            return accounts[0]
        }
        return null
    }

    getAllSubscriptions = async () => {
        if (!this.accountId) {
            return null
        }
        const qiUsers = await this.getDocs([['accountId', '==', this.accountId]], '', '', 1)
        if (qiUsers.length > 0) {
            return qiUsers[0]
        }
        return null
    }

    getSubscription = async (subscriptionId) => {
        if (!subscriptionId) {
            return null
        }
        return this.repository.getDoc(`${this.path}/${subscriptionId}`)
    }
}
