import { LEADS_COLLECTION_NAME } from "Constants/AppCollections";
import { FirebaseRepository } from "FirebaseRef/repository";

const repository = new FirebaseRepository()

export const getLead = async (leadId) => {
  if (!leadId) Error('leadId is required')

  const path = `/${LEADS_COLLECTION_NAME}/${leadId}`
  return await repository.getDoc(path)
}

export const getLeadsByIds = async (leadIds) => {
  if (!leadIds.length) Error('leadIds is required')

  return await repository.getDocs(LEADS_COLLECTION_NAME, [['ID', 'in', leadIds]])

}
