import { Avatar, Checkbox, FormControlLabel, Toolt<PERSON> } from "@material-ui/core"
import { Skeleton } from '@material-ui/lab'
import DeleteConfirmationDialog from 'Components/DeleteConfirmationDialog/DeleteConfirmationDialog'
import PaginationNav from 'Components/Pagination/PaginationNav'
import SimplePaginationNav from "Components/Pagination/SimplePaginationNav"
import React from 'react'
import { Link } from 'react-router-dom'

import { Badge } from 'reactstrap'

export const TableCustom = ({
  items = [],
  itemsCount = 0,
  selectedItem,
  onSelectItem,
  cols,
  actions,
  hasSelectedItem,
  loading,
  currentPage,
  pageLimit,
  fullPagination = false,
  goToPage
}) => {

  const pages = Math.ceil(itemsCount / pageLimit)
  const paginated = itemsCount > pageLimit
  // Select All
  const onSelectAll = (e) => {

    let selectAll = selectedItem.length < items.length

    if (selectAll) {
      onSelectItem(items)
    } else {
      onSelectItem([])
    }
  }

  /**
   * On Select Interaction
   */
  const onToggleInteraction = (interactionItem) => {
    if (selectedItem.find(s => s.id === interactionItem.id)) {
      onSelectItem(selectedItem.filter(s => s.id !== interactionItem.id))
    } else {
      onSelectItem([...selectedItem, interactionItem])
    }
  }

  const handleCheckInteraction = (interactionItem) => {
    const isChecked = selectedItem.find(s => s.id === interactionItem.id)
    return !!isChecked
  }

  const openDeleteConfirmationDialog = (interactionItem) => {
    if (interactionItem && interactionItem.id) setTrashId(interactionItem.id)
    DeleteConfirmationDialog.open()
  }

  return (
    <table className="table table-middle table-hover mb-0">
      <thead>
        <tr>
          {hasSelectedItem && <th className="w-5">
            <FormControlLabel
              control={
                <Checkbox
                  indeterminate={selectedItem.length > 0 && selectedItem.length < items.length}
                  checked={selectedItem.length > 0}
                  onChange={e => onSelectAll(e)}
                  value="all"
                  color="primary"
                />
              }
              label=""
            />
          </th>
          }
          {cols.map((col, key) => (
            <th key={key}>{col.label}</th>
          ))}



          {/* {user.level === WEBMASTER_LEVEL && <th>{langMessages['modules.accounts.singular']}</th>} */}
          {actions && <th></th>}
        </tr>
      </thead>
      <tbody>
        {(!loading &&
          items.map((interactionItem, index) => {
            return (
              (interactionItem && interactionItem.username && (
                <tr key={index}>
                  {hasSelectedItem &&
                    <td>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={handleCheckInteraction(interactionItem)} //!!selectedUsers.find(s => s.username === interactionItem.idDoInstagram)}
                            onChange={() => onToggleInteraction(interactionItem)}
                            color="primary"
                          />
                        }
                      />
                    </td>
                  }

                  {cols.map((col, key) => {
                    if (col?.type === 'link' && !!interactionItem?.detailLink) {
                      return (
                        <td key={key}>
                          <Link to={interactionItem?.detailLink}>
                            <div className="media d-flex align-items-center">
                              {col?.avatar &&

                                interactionItem?.avatar ? (
                                <img src={interactionItem?.avatar} alt={interactionItem[col.field]} className="rounded-circle mr-15" width="40" height="40" />
                              ) : col?.avatar ? (
                                <Avatar className="mr-15">{(interactionItem[col.field].charAt(0)) || '?'}</Avatar>
                              ) : null}

                              <div className="media-body">
                                <h5 className="mb-5 fw-bold">{interactionItem[col.field]}</h5>
                                {col?.badge && <Badge color={interactionItem.badgeColor}>{interactionItem.badgeTitle}</Badge>
                                }
                              </div>
                            </div>
                          </Link>
                        </td>
                      )
                    }
                    if (col?.type === 'link') {
                      return (
                        <td key={key}>
                          <div className="media d-flex align-items-center">
                            {col?.avatar &&

                              interactionItem?.avatar ? (
                              <img src={interactionItem?.avatar} alt={interactionItem[col.field]} className="rounded-circle mr-15" width="40" height="40" />
                            ) : col?.avatar ? (
                              <Avatar className="mr-15">{(interactionItem[col.field].charAt(0)) || '?'}</Avatar>
                            ) : null}

                            <div className="media-body">
                              <h5 className="mb-5 fw-bold">{interactionItem[col.field]}</h5>
                              {col?.badge &&
                                <Badge
                                  onClick={() => interactionItem?.onVinculateClick(interactionItem)}
                                  className="cursor-pointer"
                                  color={interactionItem.badgeColor}
                                >
                                  {interactionItem.badgeTitle}
                                </Badge>
                              }
                            </div>
                          </div>
                        </td>
                      )
                    }
                    return (<td key={key}>
                      <div className="media d-flex align-items-center">

                        {col?.avatar && interactionItem?.avatar ? (
                          <img src={interactionItem?.avatar} alt={interactionItem[col.field]} className="rounded-circle mr-15" width="40" height="40" />
                        ) : col?.avatar ? (
                          <Avatar className="mr-15">{(interactionItem[col.field].charAt(0)) || '?'}</Avatar>
                        ) : null}
                        {interactionItem[col.field]}
                      </div>
                    </td>)

                  })}



                  {/* {user.level === WEBMASTER_LEVEL && <td>{(accounts.find(a => a.ID === interactionItem[ACCOUNT_FIELD]) || {}).title || ''}</td>} */}
                  <td className="list-action">
                    <div className="d-flex justify-content-end">
                      <div>
                        {actions && actions.map((action, index) => {
                          if (action?.type === 'link') {
                            return (
                              <Tooltip key={index} title={action?.tooltip ? action?.tooltip : ''} placement={action?.tooltip ? "bottom" : null}>
                                <Link className="rct-link-btn" to={action?.link}>
                                  {' '}
                                  <i className={action?.icon ? action?.icon : "ti-pencil"}></i>{' '}
                                </Link>
                              </Tooltip>
                            )
                          } else {
                            return (
                              <Tooltip key={index} title={action?.tooltip ? action?.tooltip : ''} placement={action?.tooltip ? "bottom" : null}>
                                <button
                                  type="button"
                                  className={`rct-link-btn ${action?.visibled && interactionItem?.[action?.visibled] ? "d-none" : "d-inherit"}`}
                                  onClick={() => action?.onClick(interactionItem)}>
                                  <i className={action?.icon ? action?.icon : "ti-arrow-top-right"}></i>{' '}
                                </button>

                              </Tooltip>
                            )
                          }
                        })}
                      </div>
                    </div>
                  </td>
                </tr>
              )) ||
              null
            )
          })) ||
          (loading &&
            (
              <tr key={key}>
                <td>
                  <Skeleton variant="rect" height={15} width={15} />
                </td>
                <td>
                  <div className="media d-flex align-items-center">
                    <div className="mr-15">
                      <Skeleton variant="circle" height={40} width={40} />
                    </div>
                    <div className="media-body">
                      <h5 className="mb-5 fw-bold">
                        <Skeleton variant="text" height={18} width="60%" />
                      </h5>
                    </div>
                  </div>
                </td>
                <td>
                  <Skeleton variant="text" height={18} width="80%" />
                </td>
                <td className="d-flex justify-content-start"></td>
                <td>
                  <Skeleton variant="text" height={18} width="60%" />
                </td>
                <td>
                  <Skeleton variant="text" height={18} width="60%" />
                </td>
                <td className="list-action"></td>
              </tr>
            )
            ||
            null)}
      </tbody>
      <tfoot className="border-top">
        <tr>
          <td colSpan="100%">
            {fullPagination && paginated && (
              <PaginationNav
                page={currentPage}
                pages={pages}
                goToPage={(page) => goToPage(page)}
              // hasInput
              />
            )}
            {!fullPagination && paginated && (
              <SimplePaginationNav
                currentPage={currentPage}
                totalPages={pages}
                onChange={goToPage}
              />
            )}
          </td>
        </tr>
      </tfoot>
    </table>
  )
}
