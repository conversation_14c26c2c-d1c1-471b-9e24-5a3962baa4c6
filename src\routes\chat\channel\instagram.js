/* eslint-disable no-undef */
import { ShotXActions } from 'Actions/ShotXActions'
import { ChatAPI } from '../API/chatInstagram'


/**
 * Implementacao do canal Instagram para utilizar com o ChatTab
 */
export default class InstagramChannel {
  /**
   * @param {object} me Informações do usuário
   * @param {string} contactId Lead
   * @param {string} instance instância para selecionada
   */
  constructor({ me, contactId, instance }) {
    this.me = me
    this.instance = instance
    this.contact = {
      id: contactId,
      name: null,
      ig_id: null,
    }
    this.initialized = false
    this.platformName = 'Instagram'
    this.channelId = 'shotx-instagram'
    this.onMessageReceive = undefined
    this.api = ChatAPI
    this.unsubscribe = undefined
    this.contactFieldId = 'ig_sid'
    this.actions = new ShotXActions()
    this.canRecordAudio = false
  }

  get userId() {
    return this.me?.id
  }

  get userUid() {
    return this.me?.uid
  }

  get accountId() {
    return this.me?.accountId
  }

  get contactID() {
    return this.contact?.id
  }

  get instanceID() {
    return this.instance?.id
  }

  get instanceStatus() {
    return this.instance?.status?.state
  }

  get instanceConnected() {
    return this.instanceStatus === 'open'
  }

  get contactRemoteID() {
    return this.contact?.ig_id
  }

  get canSendMessage() {
    return this.integrationIsReady && this.contactID !== undefined && this.instanceID !== undefined && this.contactRemoteID !== undefined
  }

  get canListenMessages() {
    return this.instanceID !== undefined && this.accountId !== undefined && this.contactRemoteID !== undefined
  }

  get hasIntegration() {
    return this.instanceID !== undefined
  }

  get hasNoIntegration() {
    return !this.hasIntegration
  }

  get integrationIsReady() {
    return this.instanceStatus === 'open'
  }

  get hasNoContact() {
    return !this.contactRemoteID
  }

  get feedback() {
    if (this.hasNoContact) {
      return {
        type: 'warning',
        message: 'shotx.instagram.noContact',
      }
    }

    if (this.hasNoIntegration) {
      return {
        type: 'warning',
        message: 'shotx.instagram.noIntegrations',
      }
    }

    if (!this.instanceConnected) {
      return {
        type: 'warning',
        message: 'shotx.instance.disconnectedInfo',
      }
    }

    if (!this.integrationIsReady) {
      return {
        type: 'warning',
        message: 'shotx.instagram.notReady',
      }
    }
  }

  get contactName() {
    return this.contact?.name
  }

  get contactPhone() {
    return this.contact.username
  }

  get audioRecordName() {
    return `${this.contactID}_${Date.now()}`
  }

  get audioRecordType() {
    return 'audio/mp4'
  }

  get contactAvatar() {
    return this.contact?.avatar
  }

  async getContact(onGetContact) {
    if (!this.contactID) {
      console.log('Contato não informado')
      onGetContact && onGetContact(undefined)
      return
    }

    const onFetchContact = async contact => {
      if (!contact) {
        console.log('Contato não encontrado')
        onGetContact && onGetContact(undefined)
        return

      } else {
        const { displayName, firstName, ig_contacts = [] } = contact

        const ig_sid = ig_contacts.find(c => c.instanceId === this.instanceID)
        if (!ig_sid) {
          console.log('Interação não encontrada')
          onGetContact && onGetContact(undefined)
          return
        }

        const onFetchInstanceContact = async (instanceContact) => {
          const contactDoc = {
            id: this.contactID,
            name: displayName || firstName,
            ig_id: ig_sid.ig_sid,
            username: ig_sid.ig_username,
            avatar: instanceContact?.avatar
          }
          this.contact = contactDoc

          onGetContact && onGetContact(this.contact)
        }

        this.actions.fetchInstanceContact(this.accountId, this.instanceID, ig_sid.ig_sid, onFetchInstanceContact)
      }
    }

    return await this.actions.fetchContact(this.contactID, onFetchContact)
  }

  listenerMessages = (onMessageReceive, ignore = false) => {
    const callback = (messages) => {
      if (this.initialized || ignore) {
        this.markAsRead(messages)

        const model_messages = messages.map(message => {
          const { type, instagram, intagram } = message
          const attachments = (instagram || intagram)?.message?.attachments || []
          if (type === 'attachment') {
            return {
              ...message,
              attachments: attachments.map(attachment => {
                const { payload, type } = attachment
                return {
                  ...message,
                  ...attachment,
                  [type]: {
                    mediaUrl: payload?.url,
                  }
                }
              }
              )
            }
          }

          return message
        })

        onMessageReceive && onMessageReceive(model_messages)
      }
    }

    return this.actions.listenUserMessages(this.accountId, this.instanceID, this.contactRemoteID, callback)
  }

  async sendMessage(message) {
    if (!this.instanceID) {
      console.log('Instância não encontrada')
      return
    }

    const messageData = {
      instance: this.instance,
      ig_id: this.contactRemoteID,
      message,
      instance: this.instance,
    }
    return await this.api.sendMessage(messageData)
  }

  async sendPresence(presence) {

    return
  }

  sendAudio = async (audio) => {
    if (!this.instanceID) {
      console.log('Instância não encontrada')
    }

    if (!audio) {
      console.log('Arquivo de audio não informado')
      return
    }

    const fileName = `${this.audioRecordName}.${this.audioRecordType.split('/')[1]}`
    // Convert blob to file
    const file = new File(
      [audio],
      fileName,
      {
        type: this.audioRecordType,
        lastModified: Date.now(),
      }
    )

    console.log('Enviando audio...', file)
    this.api.sendMediaFile({
      instance: this.instance,
      ig_sid: this.contactRemoteID,
      file: file,
      type: 'audio'
    })
  }

  resendMessage = async messageId => {
    if (!this.instanceID) {
      console.log('Instância não encontrada')
      return
    }

    return this.api.resendMessage({ instance: this.instance, phone: this.contact?.ig_id, messageId })
  }

  sendAttachment = async ({ file, option }) => {
    if (!this.instanceID) {
      console.log('Instância não encontrada')
      return
    }

    if (!file) {
      console.log('Anexo não informado')
      return
    }

    this.api.sendMediaFile({
      instance: this.instance,
      ig_sid: this.contactRemoteID,
      file,
      type: option.type,
    })
  }

  editMessage(message) {
    // TODO: metodo responsavel por editar uma mensagem
    console.warn("Method 'editMessage' not implemented.")
  }

  deleteMessage(message) {
    // TODO: metodo responsavel por deletar uma mensagem
    console.warn("Method 'deleteMessage' not implemented.")
  }

  markAsRead(messages) {
    // TODO: metodo responsavel por marcar uma conversa como lida
    console.warn("Method 'markAsRead' not implemented.")
  }

  startChannel = async (onStartChannel, onMessageReceive) => {
    this.onMessageReceive = onMessageReceive
    if (!this.initialized) {
      //await this.getInstances()
      const onGetContact = _ => {
        this.initialized = true
        if (this.canListenMessages) this.unsubscribe = this.listenerMessages(onMessageReceive)
        onStartChannel && onStartChannel(this)
      }
      await this.getContact(onGetContact)
    }
  }

  stopChannel = onStopChannel => {
    this.me = undefined
    this.instance = undefined
    this.contact = {}
    this.initialized = false
    this.onMessageReceive = undefined
    if (this.unsubscribe) {
      this.unsubscribe()
      this.unsubscribe = undefined
    }
    onStopChannel && onStopChannel(this)
  }

  resetMessagesCount = () => {
    const where = [
      [this.contactFieldId, '==', this.contactRemoteID]
    ]
    return this.actions.resetMessagesCount(this.accountId, this.instanceID, where)
  }

  supportedMedias = [
    {
      label: 'texts.image',
      icon: 'zmdi zmdi-image',
      type: 'image',
      accept: [
        'image/png',
        'image/jpg',
        'image/jpeg',
        'image/gif',
      ],
      maxSize: 8 * 1024 * 1024,
    },
    {
      label: "texts.video",
      icon: 'zmdi zmdi-videocam',
      type: 'video',
      accept: [
        'video/mp4',
        'video/ogg',
        'video/avi',
        'video/mov',
        'video/webm',
      ],
      maxSize: 25 * 1024 * 1024,
    }
  ]


  updateSniperSession = async (action) => {

    return await this.actions.updateSniperSession({
      action,
      accountId: this.accountId,
      contactId: this.contactRemoteID,
      instanceId: this.instanceID,
      platform: this.platformName
    })
  }
}
