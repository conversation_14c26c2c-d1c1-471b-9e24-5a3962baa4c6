/**
 * Mailings Actions
 */
import { FirestoreRef, createEmail } from 'FirebaseRef'

// lang strings
import { langMessages } from '../lang'

import {
  GET_MAILING,
  GET_MAILING_SUCCESS,
  GET_MAILING_FAILURE,
  SEND_MAILING,
  SEND_MAILING_SUCCESS,
  SEND_MAILING_FAILURE,
  CONFIRM_BROADCAST_MAILING,
  ON_SEND_BROADCAST_MAILING,
  MISSING_BROADCAST_CONFIRMATION,
  DELETE_MAILING,
  DELETE_MAILING_SUCCESS,
  DELETE_MAILING_FAILURE,
  RESET_MAILING_ERROR,
} from '../actions/types'

import { localJSON, sessionJSON } from 'Helpers'

import { OWNER_LEVEL } from 'Constants/UsersRoles'

import COLLECTIONS from '../constants/AppCollections'

const emailsCollection = COLLECTIONS.MAIL_COLLECTION_NAME

/**
 * Redux Action get Mailing
 */
export const getMailingEmails = (accountId, queries, limit, lastPos, orderArgs) => (dispatch, getState) => {
  /* 
    console.groupCollapsed('getMailing');
    console.log('queries',queries);
    console.log('limit',limit);
    console.log('lastPos',lastPos);
    console.log('orderArgs',orderArgs);
    console.groupEnd('getMailing'); 
    */
  queries = queries || []

  dispatch({ type: GET_MAILING })

  return new Promise((resolve, reject) => {
    let QueryRef = FirestoreRef.collection(emailsCollection)

    if (accountId !== 1) QueryRef = QueryRef.where('accountId', '==', `${accountId}`)

    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

    let orderBy = ''
    let order = ''

    if (orderArgs) {
      orderBy = typeof orderArgs === 'string' ? orderArgs : orderArgs.orderBy
      order = (orderArgs && orderArgs.order) || 'asc'
    } else if (!queries.length) {
      orderBy = 'createdAt'
      order = 'desc'
    }

    if (orderBy) {
      QueryRef = QueryRef.orderBy(orderBy, order)
      !!lastPos && (QueryRef = QueryRef.startAt(lastPos))
    }

    !isNaN(limit) && limit && (QueryRef = QueryRef.limit(limit))

    QueryRef.get()
      .then(snapshot => {
        const emails = []
        snapshot.forEach(doc => {
          emails.push(doc.data())
        })
        dispatch({ type: GET_MAILING_SUCCESS, payload: emails })
        return resolve(emails)
      })
      .catch(function (error) {
        console.error(GET_MAILING_FAILURE, error)
        dispatch({ type: GET_MAILING_FAILURE })
      })
  })
}

export const listenMailingEmails = (accountId, listenerFn, queries, limit, lastPos, orderArgs) => (dispatch, getState) => {
  /**
   * Redux Action Listen MailingEmails
   */

  // console.groupCollapsed('listenMailingEmails');
  // console.log('queries',queries);
  // console.groupEnd('listenMailingEmails');

  dispatch({ type: GET_MAILING })

  let QueryRef = FirestoreRef.collection(emailsCollection).where('accountId', '==', `${accountId}`)
  Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

  let orderBy = ''
  let order = ''

  if (orderArgs) {
    orderBy = typeof orderArgs === 'string' ? orderArgs : orderArgs.orderBy
    order = (orderArgs && orderArgs.order) || 'asc'
  } else if (!queries.length) {
    orderBy = 'createdAt'
    order = 'desc'
  }

  if (orderBy) {
    QueryRef = QueryRef.orderBy(orderBy, order)
    !!lastPos && (QueryRef = QueryRef.startAt(lastPos))
  }

  !isNaN(limit) && limit && (QueryRef = QueryRef.limit(limit))

  return new Promise(res => {
    const refListener = QueryRef.onSnapshot(
      snapshot => {
        // console.groupCollapsed('listenMailingEmails > snapshot');
        // console.log('snapshot:'+collection,snapshot.docs);
        // console.groupEnd('listenMailingEmails > snapshot');

        const emails = []
        snapshot.forEach(doc => {
          emails.push(doc.data())
        })

        dispatch({ type: GET_MAILING_SUCCESS, payload: emails })
        listenerFn && listenerFn({ emails, refListener })
      },
      error => {
        console.error(GET_MAILING_FAILURE, error)
        dispatch({ type: GET_MAILING_FAILURE })
      }
    )
    return res(refListener)
  })
}

/**
 * Redux Action send broadcast Mailing
 */
export const confirmBroadcastMailing = callback => (dispatch, getState) => {
  dispatch({ type: CONFIRM_BROADCAST_MAILING })
  setTimeout(() => callback(), 100)
}

/**
 * Redux Action send broadcast Mailing
 */
export const broadcastMailing = data => (dispatch, getState) => {
  const { mailingReducer } = getState()

  if (mailingReducer.broadCastConfirm) {
    dispatch({ type: ON_SEND_BROADCAST_MAILING })
    return sendMailingEmails(data, dispatch, getState)
  }

  dispatch({ type: MISSING_BROADCAST_CONFIRMATION })
}

/**
 * Redux Action send Mailing
 */
export const sendMailing = data => (dispatch, getState) => {
  return sendMailingEmails(data, dispatch, getState)
}

const sendMailingEmails = (data, dispatch, getState) => {
  const user = localJSON.get('user', false)

  // owner
  const currentOwnerID = sessionJSON.get('currentOwnerID', false)
  const ownerId = currentOwnerID || user.owner || (user.level <= OWNER_LEVEL && user.ID) || ''
  const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
  const accountId = data.accountId || currentAccountID || user.accountId
  const owner = data.owner || ownerId
  // end owner

  const { content, collection, ID, settings } = data

  dispatch({ type: SEND_MAILING })

  return new Promise((resolve, reject) => {
    if (content && settings && settings.subject && settings.address) {
      settings.context = {
        ...(settings.context || {}),
        collection,
        accountId,
        owner,
        id: ID,
        operator_id: user.ID,
      }

      let html = content
      if (settings.footer) {
        html += '<br>\n' + settings.footer
      }
      if (settings.address) {
        html += `${'\n'}<br><p style="text-align:center;font-size:11px;">${settings.address}</p>`
      }

      const newEmail = { ...settings, html, owner, accountId }

      return createEmail(newEmail)
        .then(response => {
          console.log('createEmail > response', response)

          let result = response.data
          if (result.error) {
            let errorMsg = langMessages['errors.genericErrorMsg']
            switch (result.error) {
              case 'MODULE_NOT_AVAILABLE':
                errorMsg = langMessages['errors.MODULE_NOT_AVAILABLE']
                break
              case 'MODULE_LIMIT_REACHED':
                errorMsg = langMessages['placeholders.MODULE_LIMIT_REACHED'].replace(
                  '[%s]',
                  langMessages[`modules.${emailsCollection}`] || emailsCollection
                )
                break
              default:
                break
            }
            dispatch({ type: SEND_MAILING_FAILURE, collection, error: result.error })
            return reject({ message: errorMsg })
          }

          const payload = result
          dispatch({ type: SEND_MAILING_SUCCESS, collection, payload })
          return resolve(payload)
        })
        .catch(error => {
          console.error(error)
          dispatch({ type: SEND_MAILING_FAILURE, error })
          return reject({ message: langMessages['errors.dbErrorMsg'] })
        })
    } else {
      let error = 'missing settings'
      let errorMsg = langMessages['errors.genericErrorMsg']

      if (!settings) {
        error = 'missing settings'
        errorMsg = langMessages['errors.genericErrorMsg']
      } else if (!settings.subject) {
        error = 'missing subject'
        errorMsg = langMessages['placeholders.requiredField'].replace('[%s]', langMessages['email.fields.subject'])
      } else if (!settings.address) {
        error = 'missing address'
        errorMsg = langMessages['placeholders.requiredField'].replace('[%s]', langMessages['emails.footerAddress'])
      } else if (!content) {
        error = 'missing content'
        errorMsg = langMessages['placeholders.requiredField'].replace('[%s]', langMessages['email.fields.content'])
      }

      console.error(error)
      dispatch({ type: SEND_MAILING_FAILURE, error, errorMsg })
      return reject({ message: errorMsg })
    }
  })
}

export const deleteScheduledEmail = mailId => (dispatch, getState) => {
  dispatch({ type: DELETE_MAILING })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(emailsCollection)
      .doc(`${mailId}`)
      .delete()
      .then(() => {
        dispatch({ type: DELETE_MAILING_SUCCESS, payload: mailId })
        return resolve()
      })
      .catch(function (error) {
        dispatch({ type: DELETE_MAILING_FAILURE, payload: mailId })
      })
  })
}

export const resetMailingError = data => {
  return { type: RESET_MAILING_ERROR }
}
