import { Card, CardContent, Typography } from '@material-ui/core';
import { langMessages } from 'Lang/index';
import React from 'react';
import { <PERSON>, Legend, Pie, Pie<PERSON>hart, ResponsiveContainer, Tooltip } from 'recharts';

const COLORS = { paid: '#4caf50', unpaid: '#ff9800', canceled: '#f44336' }

const StatusDistribution = ({ subscriptions }) => {

  const statuses = ['paid', 'unpaid', 'canceled'];
  const data = statuses.map((status) => ({ name: langMessages[`subscription.${status}`], value: subscriptions.filter((subscription) => subscription.status === status).length }));

  const RADIAN = Math.PI / 180;
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Card className="h-96">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {langMessages['subscription.overview.statusDistribution']}
        </Typography>
        <ResponsiveContainer width={"100%"} height={300}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomizedLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {statuses.map((status, index) => (
                <Cell key={`cell-${status}`} fill={COLORS[status]} />
              ))}
            </Pie>
            <Tooltip
              contentStyle={{ backgroundColor: 'transparent', border: 'none' }}
              itemStyle={{ backgroundColor: 'transparent', border: 'none' }}
              cursor={{ fill: 'transparent' }}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default StatusDistribution;
