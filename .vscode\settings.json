{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit", "source.sortImports": "explicit"}, "editor.autoClosingBrackets": "always", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.inlineSuggest.enabled": true, "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "i18n-ally.localesPaths": ["src/lang", "src/lang/locales"]}