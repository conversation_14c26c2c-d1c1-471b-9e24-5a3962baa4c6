import { Button } from '@material-ui/core';
import GetAppIcon from '@material-ui/icons/GetApp';
import { langMessages } from 'Lang/index';
import React from 'react';
const FileMessage = ({ message, channel }) => {
    const { file } = message

    const fileName = file?.documentMessage?.fileName || langMessages['shotx.chat.fileNotAvailable']
    const disabled = !file?.documentMessage?.fileName

    const setOnClick = (res) => {
        const link = document.createElement('a');
        link.href = res
        link.target = '_blank'
        link.rel = 'noopener'
        link.download = file.documentMessage.fileName

        console.log(link)

        // Append to html link element page
        document.body.appendChild(link);

        // Start download
        link.click();

        // Clean up and remove the link
        link.parentNode.removeChild(link);
    }

    const onClick = () => {
        if (file.mediaUrl) {
            setOnClick(file.mediaUrl)
            return
        }

        channel.getMediaFile(message).then(setOnClick)
    }

    return (
        <Button
            download={file?.fileName}
            onClick={onClick}
            endIcon={<GetAppIcon />}
            disabled={disabled}
        >
            {fileName}
        </Button>
    );
}

export default FileMessage;
