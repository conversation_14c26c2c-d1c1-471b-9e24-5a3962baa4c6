import { makeStyles } from '@material-ui/core';
import FormControl from '@material-ui/core/FormControl';
import FormGroup from '@material-ui/core/FormGroup';
import FormHelperText from '@material-ui/core/FormHelperText';
import FormLabel from '@material-ui/core/FormLabel';
import React from 'react';

const useStyles = makeStyles((theme) => ({
  label: {
    margin: theme.spacing(1),
    justifyContent: 'center',
    alignItems: 'center',
  },
  formControl: {
    margin: theme.spacing(1),
    width: '90%',
  },
  selectEmpty: {
    marginTop: theme.spacing(2),
  },
  alert: {
    width: '90%',
    margin: theme.spacing(1),
    justifyContent: 'start',
    alignItems: 'center',
  }
}));

export const HorizontalGroup = ({ childs, input }) => {
  const classes = useStyles();

  const { name, disabled, label, help } = input

  return (
    <FormControl
      component="fieldset"
      className={classes.formControl}
      key={`horizontalGroup-${name}`}
      variant="outlined"
      disabled={disabled}
    >
      <FormLabel component="legend">{label}</FormLabel>
      <FormGroup row>
        {childs}
      </FormGroup>
      {help && <FormHelperText>{help}</FormHelperText>}
    </FormControl>
  );
}
