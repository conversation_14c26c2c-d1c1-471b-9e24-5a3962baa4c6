/**
 * Sellers Actions
 */
import { FirestoreRef } from 'FirebaseRef'

import { GET_SELLERS, GET_SELLERS_SUCCESS, GET_SELLERS_FAILURE } from 'Actions/types'

import { ACCOUNT_FIELD, ROLES_FIELD, SELLER_FIELD } from '../constants/AppConstants'
import COLLECTIONS from '../constants/AppCollections'

const collectionName = COLLECTIONS.QIUSERS_COLLECTION_NAME

/**
 * Redux Action Get Sellers
 */
export const getSellers = (accountId, queries) => (dispatch, getState) => {
  dispatch({ type: GET_SELLERS })
  return new Promise((resolve, reject) => {
    let QueryRef = FirestoreRef.collection(collectionName)
      .where(ACCOUNT_FIELD, '==', `${accountId}`)
      .where(ROLES_FIELD, 'array-contains', SELLER_FIELD)

    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))
    QueryRef.get()
      .then(snapshot => {
        const posts = []
        snapshot.forEach(doc => {
          posts.push(doc.data())
        })
        dispatch({ type: GET_SELLERS_SUCCESS, payload: posts })
        return resolve(posts)
      })
      .catch(function (error) {
        dispatch({ type: GET_SELLERS_FAILURE })
      })
  }).catch(error => console.error(error) || dispatch({ type: GET_SELLERS_FAILURE }))
}
