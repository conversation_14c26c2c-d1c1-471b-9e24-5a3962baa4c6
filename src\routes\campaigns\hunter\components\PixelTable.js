import {
  IconButton,
  Paper,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@material-ui/core';
import { langMessages } from 'Lang/index';
import { ChartColumnIcon, Code, CopyPlusIcon, Edit2Icon, Trash } from 'lucide-react';
import React from 'react';

const PixelTable = ({ pixels, onEdit, onDelete, onToggle, onViewCode, onClone, onStats }) => {
  return (
    <Paper>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>{langMessages['campaigns.hunter.name']}</TableCell>
            <TableCell>{langMessages['campaigns.hunter.domain']}</TableCell>
            <TableCell>{langMessages['campaigns.hunter.status']}</TableCell>
            <TableCell>{langMessages['campaigns.hunter.events.title']}</TableCell>
            <TableCell align="right">{langMessages['campaigns.hunter.actions']}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {pixels.map((pixel) => (
            <TableRow key={pixel.id}>
              <TableCell>{pixel.name}</TableCell>
              <TableCell>{pixel.domain}</TableCell>
              <TableCell>
                <Switch
                  checked={pixel.active}
                  onChange={(e) => onToggle(pixel.id, e.target.checked)}
                  color="primary"
                  title={langMessages['campaigns.hunter.statusToggle']}
                  size="small"
                />
              </TableCell>
              <TableCell>{pixel.events.length}</TableCell>
              <TableCell align="right">
                {!!pixel.events.length && (
                  <IconButton size="small" onClick={() => onViewCode(pixel)} title={langMessages['campaigns.hunter.code']}>
                    <Code size={16} />
                  </IconButton>
                )}
                <IconButton size="small" onClick={() => onStats(pixel)} title={langMessages['campaigns.hunter.stats']}>
                  <ChartColumnIcon size={16} />
                </IconButton>
                <IconButton size="small" onClick={() => onClone(pixel)} title={langMessages['campaigns.hunter.duplicate']}>
                  <CopyPlusIcon size={16} />
                </IconButton>
                <IconButton size="small" onClick={() => onEdit(pixel)} title={langMessages['campaigns.hunter.edit']}>
                  <Edit2Icon size={16} />
                </IconButton>
                <IconButton size="small" onClick={() => onDelete(pixel.id)} title={langMessages['campaigns.hunter.delete']}>
                  <Trash size={16} />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Paper>
  );
};

export default PixelTable;
