/**
 * Email App Actions
 */

import moment from 'moment'

import download from '../helpers/download'

import { FirestoreRef } from '../firebase'

import { langMessages } from '../lang'

import { NotificationManager } from 'react-notifications'

import {
  MSA_CLIENT_READY,
  MSA_CLIENT_ERROR,
  MSA_AUTH2_READY,
  GET_MSA_ACCOUNTS,
  GET_MSA_ACCOUNT,
  ON_MSA_AUTH_SUCCESS,
  ON_MSA_AUTH_ERROR,
  ON_MSA_SIGN_OUT,
  LIST_MSA_FOLDER_SUCCESS,
  ON_EMAIL_MOVE_TO_FOLDER,
  ON_EMAIL_MOVE_TO_FOLDER_SUCCESS,
  ON_EMAIL_MOVE_TO_FOLDER_FAILURE,
  GET_MAILBOX_LABELS,
  GET_MAILBOX_LABELS_SUCCESS,
  GET_MAILBOX_LABELS_FAILURE,
  GET_MAILBOX_FOLDERS,
  GET_MAILBOX_FOLDERS_SUCCESS,
  GET_MAILBOX_FOLDERS_FAILURE,
  SAVE_MSA_DRAFT,
  SAVE_MSA_DRAFT_SUCCESS,
  SAVE_MSA_DRAFT_FAILURE,
  GET_EMAIL,
  GET_EMAIL_SUCCESS,
  GET_EMAIL_FAILURE,
  ON_TRASH_EMAIL,
  ON_TRASH_EMAIL_SUCCESS,
  ON_TRASH_EMAIL_FAILURE,
  ON_DELETE_MAIL,
  ON_DELETE_MAIL_SUCCESS,
  ON_DELETE_MAIL_FAILURE,
  SET_EMAIL_AS_STAR,
  SEARCH_EMAIL,
  ON_SEND_EMAIL,
  ON_SEND_EMAIL_SUCCESS,
  EMAIL_SENT_SUCCESSFULLY,
  ON_SEND_EMAIL_FAILURE,
  GET_FOLDER_EMAILS,
  GET_FOLDER_EMAILS_SUCCESS,
  GET_FOLDER_EMAILS_FAILURE,
  ADD_LABELS_INTO_EMAILS,
} from 'Actions/types'

import { validateEmail, extractEmailAddress } from 'Helpers/formHelpers'

import { MSA_API_CLIENT_ID, MSA_API_REDIRECT_URI } from 'Constants/AppConstants'

import { MOMENT_ISO } from 'Constants'

// folders
import folders from 'Routes/mail/model/folders'
import folderModel from 'Routes/mail/model/folder'
import emailModel from 'Routes/mail/model/email'

import COLLECTIONS from '../constants/AppCollections'

import { UserAgentApplication } from 'msal'
import { PublicClientApplication } from 'msal'

import { ImplicitMSALAuthenticationProvider } from '@microsoft/microsoft-graph-client/lib/src/ImplicitMSALAuthenticationProvider'
import { MSALAuthenticationProviderOptions } from '@microsoft/microsoft-graph-client/lib/src/MSALAuthenticationProviderOptions'

import { Client } from '@microsoft/microsoft-graph-client'

// An Optional options for initializing the MSAL @see https://github.com/AzureAD/microsoft-authentication-library-for-js/wiki/MSAL-basics#configuration-options
const graphScopes = ['email', 'mail.read', 'mail.readwrite', 'mail.send', 'user.read', 'mailboxsettings.read'] // An array of graph scopes

const tokenRequest = {
  scopes: graphScopes,
}

// Config object to be passed to Msal on creation
const msalConfig = {
  auth: {
    clientId: MSA_API_CLIENT_ID, // Client Id of the registered application
    redirectUri: MSA_API_REDIRECT_URI,
    postlogoutRedirectUrl: MSA_API_REDIRECT_URI,
  },
  cache: {
    cacheLocation: 'localStorage', // This configures where your cache will be stored
    storeAuthStateInCookie: false, // Set this to "true" if you are having issues on IE11 or Edge
  },
}

// Important Note: This library implements loginPopup and acquireTokenPopup flow, remember this while initializing the msal
// Initialize the MSAL @see https://github.com/AzureAD/microsoft-authentication-library-for-js#1-instantiate-the-useragentapplication
const msalApplication = new UserAgentApplication(msalConfig)
const options = new MSALAuthenticationProviderOptions(graphScopes)
const authProvider = new ImplicitMSALAuthenticationProvider(msalApplication, options)

const getMsaAccounts = () => msalApplication.getAllAccounts()
const getMsaAccount = () => msalApplication.getAccount()

let messageModel = {
  id: '',
  conversationId: '',
  internetMessageId: '',
  body: { content: '' }, // "<html>"
  subject: '', // "Teste Microsoft"
  categories: [], // []
  importance: 'normal', // "normal"
  hasAttachments: false, // false
  isDraft: false, // false
  isRead: false, // true
  createdDateTime: '2020-08-15T07:54:12Z', // "2020-08-15T07:54:12Z"
  sentDateTime: '2020-08-15T07:54:12Z', // "2020-08-15T07:54:12Z"
  receivedDateTime: '2020-08-15T07:54:12Z', // "2020-08-15T07:54:12Z"
  lastModifiedDateTime: '2020-08-15T07:54:12Z', // "2020-08-15T07:54:12Z"
  bccRecipients: [], // []
  ccRecipients: [], // []
  toRecipients: [], // []
  replyTo: [], // []
  flag: { flagStatus: 'notFlagged' }, // "notFlagged"
}

/**
 * Redux Action To Mark As Star Email
 */
export const getOutlookAccounts = () => ({
  type: GET_MSA_ACCOUNTS,
  payload: getMsaAccounts(),
})

/**
 * Redux Action To Mark As Star Email
 */
export const getOutlookAccount = () => ({
  type: GET_MSA_ACCOUNT,
  payload: getMsaAccount(),
})

/**
 * Redux Action To Mark As Star Email
 */
export const outlookMarkAsStarEmail = email => ({
  type: SET_EMAIL_AS_STAR,
  payload: email,
})

/**
 * Redux Action To Search Email
 */
export const outlookSearchEmail = searchText => ({
  type: SEARCH_EMAIL,
  payload: searchText,
})

/**
 * Redux Action To Add Labels Into Email
 */
export const outlookAddLabelsIntoEmail = label => ({
  type: ADD_LABELS_INTO_EMAILS,
  payload: label,
})

let client, msaGraphLoaded, errorTimeout

export const getMsaClient = () => (dispatch, getState) => {
  return new Promise(async (res, rej) => {
    // console.log('window.msaClient',window.msaClient);

    // if (window.msaClient) {
    //     return res(window.msaClient);
    // }

    client = Client.initWithMiddleware({
      authProvider, // An instance created from previous step
    })

    try {
      let userDetails = await client.api('/me').get()
      window.msaClient = client
      dispatch({ type: MSA_CLIENT_READY, payload: client })
      return res(client)
    } catch (error) {
      msalApplication
        .acquireTokenPopup(tokenRequest)
        .then(accessToken => {
          console.log('accessToken', accessToken)
          window.msaClient = client
          dispatch({ type: MSA_CLIENT_READY, payload: client })
          clearTimeout(errorTimeout)
          return res(client)
        })
        .catch(acquireTokenPopupError => {
          console.log('acquireTokenPopupError', acquireTokenPopupError)
          dispatch({ type: MSA_CLIENT_ERROR, error })
          let errorMsg = 'Erro na integração com Microsoft'
          clearTimeout(errorTimeout)
          errorTimeout = setTimeout(() => {
            NotificationManager.error(errorMsg)
          }, 3000)
          return rej({ error: errorMsg })
        })
    }
  })
}

const getUserFromOutlookProfile = profile => {
  let user = {}
  if (profile && validateEmail(extractEmailAddress(profile.mail || profile.userPrincipalName))) {
    let email = extractEmailAddress(profile.mail || profile.userPrincipalName)
    user = {
      id: profile.id,
      email,
      firstName: profile.givenName,
      lastName: profile.surname,
      displayName: profile.displayName,
      mobile: profile.mobilePhone,
      picture: '',
      shortName: profile.givenName,
      fromName: profile.displayName,
      from: email,
      domain: email.split('@')[1],
    }
  }
  // console.log('user', user);
  return user
}

export const signinOutlookUser = () => (dispatch, getState) => {
  // console.log('signinOutlookUser >>> ')

  return new Promise((res, rej) => {
    getMsaClient()(dispatch).then(async client => {
      try {
        let userDetails = await client.api('/me').get()
        let outlookUser = getUserFromOutlookProfile(userDetails)

        // console.log('userDetails',userDetails);
        // console.log('outlookUser',outlookUser);

        dispatch({ type: MSA_AUTH2_READY, authInstance: client })
        dispatch({ type: ON_MSA_AUTH_SUCCESS, payload: outlookUser })

        return res(outlookUser)
      } catch (error) {
        dispatch({ type: ON_MSA_AUTH_ERROR, error })
        console.error('ON_MSA_AUTH_ERROR', error)
      }
    })
  })
}

export const outlookLogoutPopup = () => (dispatch, getState) => {
  let w = window.open('/app/mail/outlook/logout', '_blank', 'width=400,height=600')
  return new Promise(async res => {
    let wInterval = setInterval(async () => {
      console.log(w.closed)
      if (w.closed) {
        clearInterval(wInterval)
        try {
          let userDetails = await client.api('/me').get()
          console.log('userDetails', userDetails)
          return res(false)
        } catch (error) {
          // console.log('userDetails > error',error);
          dispatch({ type: ON_MSA_SIGN_OUT })
          return res(true)
        }
      }
    }, 500)
  })
}

export const signoutOutlookUser = () => (dispatch, getState) => {
  // console.log('signoutOutlookUser >>> ')

  return new Promise(res => {
    getMsaClient()(dispatch)
      .then(async client => {
        let signinStatus = client
        console.log('signinStatus', signinStatus)
        msalApplication.logout()

        dispatch({ type: ON_MSA_SIGN_OUT })

        return res(true)
      })
      .catch(err => res(true))
  })
}

export const extractOutlookMessage = (message, data) => {
  let html = ''

  let {
    id,
    conversationId,
    internetMessageId,
    body: { content }, // "<html>"
    subject, // "Teste Microsoft"
    categories, // []
    importance, // "normal"
    hasAttachments, // false
    isDraft, // false
    isRead, // true
    createdDateTime, // "2020-08-15T07:54:12Z"
    sentDateTime, // "2020-08-15T07:54:12Z"
    receivedDateTime, // "2020-08-15T07:54:12Z"
    lastModifiedDateTime, // "2020-08-15T07:54:12Z"
    bccRecipients, // []
    ccRecipients, // []
    toRecipients, // []
    replyTo, // []
    flag: { flagStatus }, // "notFlagged"
  } = { ...messageModel, ...message }

  let from, fromName

  if (isDraft) {
    let account = getMsaAccount()
    let { idToken } = account
    let sender = { fromName: (idToken || {}).name || '', from: (idToken || {}).email || '' }

    from = (data || {}).from || sender.from
    fromName = (data || {}).fromName || sender.fromName || from
  } else {
    let {
      from: { emailAddress },
    } = message
    from = emailAddress.address
    fromName = emailAddress.name
  }

  let bcc = (bccRecipients || []).map(r => `${r.emailAddress.address}`).join(',')
  let cc = (ccRecipients || []).map(r => `${r.emailAddress.address}`).join(',')
  let to = (toRecipients || []).map(r => `${r.emailAddress.address}`).join(',')

  if (content && content.indexOf('<body') !== -1) {
    let tempHtml = content.split('<body')[1].split('</body>')[0]
    html = tempHtml.replace(tempHtml.split('>')[0] + '>', '')
  } else {
    html = content
  }

  let email = {
    ...emailModel,
    ...(data || {}),
    html,
    id,
    ID: id,
    mailId: id,
    resourceId: internetMessageId,
    date: moment(createdDateTime).format(MOMENT_ISO),
    modified: moment(lastModifiedDateTime).format(MOMENT_ISO),
    sentDate: moment(sentDateTime).format(MOMENT_ISO),
    receivedDate: moment(receivedDateTime).format(MOMENT_ISO),
    createdAt: moment(createdDateTime).valueOf(),
    updatedAt: moment(lastModifiedDateTime).valueOf(),
    labels: categories,
    subject,
    bcc,
    cc,
    to,
    fromName,
    from,
    isRead,
  }

  return email
}

export const downloadOutlookAttachment = attachment => {
  try {
    download(`data:${attachment.mimeType};base64,` + attachment.contentBytes, attachment.name, attachment.mimeType)
  } catch (error) {
    alert(error)
  }
}

export const getOutlookAttachments = messageId => (dispatch, getState) => {
  return new Promise((res, rej) => {
    getMsaClient()(dispatch).then(async client => {
      let attachments = []
      let response = await client.api(`/me/messages/${messageId}/attachments`).get()

      console.log('getOutlookAttachments > response', response)

      if (Array.isArray(response.value)) {
        // contentType: "contentType-value",
        // contentLocation: "contentLocation-value",
        // contentBytes: "contentBytes-value",
        // contentId: "null",
        // lastModifiedDateTime: "datetime-value",
        // id: "id-value",
        // isInline: false,
        // name: "name-value",
        // size: 99

        attachments = response.value.map(a => {
          let { id, contentId, name, contentType, contentBytes } = a
          let attachment_url = ''
          let preview_url = ''

          return {
            attachment_url,
            preview_url,
            attachment_id: contentId,
            name,
            mimeType: contentType,
            contentBytes,
          }
        })

        console.log('attachments', attachments)

        return res(attachments)
      }

      return res([])
    })
  })
}

export const getOutlookMessage = (mailboxId, folder, messageId, useStore) => (dispatch, getState) => {
  useStore = useStore !== false

  useStore && dispatch({ type: GET_EMAIL, folder })

  return new Promise((res, rej) => {
    getMsaClient()(dispatch).then(async client => {
      try {
        let response = await client.api(`/me/messages/${messageId}`).get()
        console.log('getOutlookMessage > response', response)

        let email = extractOutlookMessage(response)

        if (response.hasAttachments) {
          getOutlookAttachments(messageId)(dispatch).then(attachments => {
            email.attachments = attachments
            attachments.forEach(a => {
              if (a.attachment_id && a.contentBytes) {
                email.html = email.html.replace(`src="cid:${a.attachment_id}"`, `src="data:${a.mimeType};base64, ${a.contentBytes}"`)
              }
            })
            useStore && dispatch({ type: GET_EMAIL_SUCCESS, payload: email, folder, mailboxId })
            return res({ email, response })
          })
        } else {
          useStore && dispatch({ type: GET_EMAIL_SUCCESS, payload: email, folder, mailboxId })
          return res({ email, response })
        }
      } catch (error) {
        console.error('error', error)
        useStore && dispatch({ type: GET_EMAIL_FAILURE, ID: messageId, folder })
        return rej(error)
      }
    })
  })
}

export const fetchOutlookMessages = (mailboxId, folder, query) => (dispatch, getState) => {
  dispatch({ type: GET_FOLDER_EMAILS, folder, query })

  return new Promise((res, rej) => {
    getMsaClient()(dispatch).then(async client => {
      try {
        let msFolders = {
          inbox: 'inbox',
          draft: 'drafts',
          spam: 'junkemail',
          sent: 'sentitems',
          trash: 'deleteditems',
        }
        let queryStr = query ? `${query}` : ''
        let msFolder = msFolders[folder] || folder
        let msEndpoint = query ? `/me/messages${queryStr}` : `/me/mailfolders/${msFolder}/messages`
        let response = await client.api(msEndpoint).get()

        console.log('fetchOutlookMessages > response', response)

        if (Array.isArray(response.value)) {
          let promises = response.value.map(message => {
            return new Promise((innerRes, innerRej) => {
              let email = extractOutlookMessage(message)

              if (message.hasAttachments) {
                return getOutlookAttachments(message.id)(dispatch).then(attachments => {
                  email.attachments = attachments
                  attachments.forEach(a => {
                    if (a.attachment_id && a.contentBytes) {
                      email.html = email.html.replace(`src="cid:${a.attachment_id}"`, `src="data:${a.mimeType};base64, ${a.contentBytes}"`)
                    }
                  })
                  return innerRes(email)
                })
              } else {
                return innerRes(email)
              }
            })
          })

          return Promise.all(promises)
            .then(emails => {
              console.log('fetchOutlookMessages > emails', emails)
              dispatch({ type: GET_FOLDER_EMAILS_SUCCESS, payload: emails, folder, mailboxId })
              return res(emails)
            })
            .catch(error => {
              console.error(error)
              dispatch({ type: GET_FOLDER_EMAILS_FAILURE, folder, error })
              return rej('Erro na integração com gmail')
            })
        }

        dispatch({ type: GET_FOLDER_EMAILS_SUCCESS, payload: [], folder, mailboxId })
        return res([])
      } catch (error) {
        console.error(error)
        dispatch({ type: GET_FOLDER_EMAILS_FAILURE, folder, mailboxId })
      }
    })
  })
}

export const saveOutlookDraft = (mailboxId, data) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    let rejected = false
    let requiredFields = ['fromName', 'subject', 'html', 'to']

    requiredFields.forEach(field => {
      if (!rejected && !data[field]) {
        rejected = true
        reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages[`email.fields.${field}`]) })
      }
    })

    const { id, from, fromName, subject, html, to, cc, bcc } = data
    const isNewDraft = !id

    console.log('saveOutlookDraft > data', data)

    to.split(',').forEach(e => {
      if (!validateEmail(extractEmailAddress(e))) {
        rejected = true
        return reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages['email.fields.to']) })
      }
    })

    if (rejected) return

    dispatch({ type: SAVE_MSA_DRAFT })

    let onError = err => {
      const message = err.message || err.error || err
      dispatch({ type: SAVE_MSA_DRAFT_FAILURE, error: message })
      reject({ error: langMessages[message] || message })
    }

    getMsaClient()(dispatch).then(async client => {
      const message = {
        subject,
        toRecipients: to.split(',').map(r => ({
          emailAddress: {
            address: extractEmailAddress(r),
          },
        })),
        body: {
          content: html,
          contentType: 'html',
        },
      }

      if (bcc.split(',').find(r => validateEmail(extractEmailAddress(r)))) {
        message.bccRecipients = bcc.split(',').map(r => ({
          emailAddress: {
            address: extractEmailAddress(r),
          },
        }))
      }
      if (cc.split(',').find(r => validateEmail(extractEmailAddress(r)))) {
        message.ccRecipients = cc.split(',').map(r => ({
          emailAddress: {
            address: extractEmailAddress(r),
          },
        }))
      }

      console.log('Draft > message', message)

      try {
        let response
        if (isNewDraft) {
          response = await client.api('/me/messages').post(message)
        } else {
          response = await client.api(`/me/messages/${id}`).update(message)
        }

        console.log('Draft > id', id)
        console.log('Draft > isNewDraft', isNewDraft)
        console.log('Draft > response', response)

        let email = extractOutlookMessage(
          {
            ...response,
            from: response.from || {
              emailAddress: {
                address: from,
                name: fromName,
              },
            },
          },
          data
        )

        dispatch({ type: SAVE_MSA_DRAFT_SUCCESS, payload: email })

        return resolve(email)
      } catch (error) {
        onError(error)
      }
    })
  })
}

export const sendOutlookMessage = (mailboxId, data) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    let rejected = false
    let requiredFields = ['fromName', 'subject', 'html', 'to']

    requiredFields.forEach(field => {
      if (!rejected && !data[field]) {
        rejected = true
        reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages[`email.fields.${field}`]) })
      }
    })

    const { from, fromName, subject, html, to, cc, bcc } = data

    console.log('sendOutlookMessage > data', data)

    to.split(',').forEach(e => {
      if (!validateEmail(extractEmailAddress(e))) {
        rejected = true
        return reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages['email.fields.to']) })
      }
    })

    if (rejected) return

    dispatch({ type: ON_SEND_EMAIL })

    let onError = err => {
      const message = err.message || err.error || err
      dispatch({ type: ON_SEND_EMAIL_FAILURE, error: message })
      reject({ error: langMessages[message] || message })
    }

    getMsaClient()(dispatch).then(async client => {
      const message = {
        subject,
        toRecipients: to.split(',').map(r => ({
          emailAddress: {
            address: extractEmailAddress(r),
          },
        })),
        body: {
          content: html,
          contentType: 'html',
        },
      }

      if (bcc.split(',').find(r => validateEmail(extractEmailAddress(r)))) {
        message.bccRecipients = bcc.split(',').map(r => ({
          emailAddress: {
            address: extractEmailAddress(r),
          },
        }))
      }
      if (cc.split(',').find(r => validateEmail(extractEmailAddress(r)))) {
        message.ccRecipients = cc.split(',').map(r => ({
          emailAddress: {
            address: extractEmailAddress(r),
          },
        }))
      }

      console.log('sent > message', message)

      try {
        let response = await client.api('/me/sendMail').post({ message })
        console.log('sent > response', response)

        dispatch({ type: ON_SEND_EMAIL_SUCCESS })
        dispatch({ type: EMAIL_SENT_SUCCESSFULLY })

        let email = extractOutlookMessage(
          {
            ...message,
            ...response,
            from: response.from || {
              emailAddress: {
                address: from,
                name: fromName,
              },
            },
          },
          data
        )

        console.log('sendOutlookMessage > sent > email', email)

        return resolve(email)
      } catch (error) {
        onError(error)
      }
    })
  })
}

export const sendOutlookDraft = (mailboxId, data) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    let rejected = false
    let requiredFields = ['fromName', 'subject', 'html', 'to']

    requiredFields.forEach(field => {
      if (!rejected && !data[field]) {
        rejected = true
        reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages[`email.fields.${field}`]) })
      }
    })

    const { id, from, fromName, subject, html, to, cc, bcc } = data
    const isDraft = !!id

    if (!isDraft) {
      return sendOutlookMessage(mailboxId, data)(dispatch)
    }

    console.log('sendOutlookDraft > data', data)

    to.split(',').forEach(e => {
      if (!validateEmail(extractEmailAddress(e))) {
        rejected = true
        return reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages['email.fields.to']) })
      }
    })

    if (rejected) return

    dispatch({ type: ON_SEND_EMAIL })

    let onError = err => {
      const message = err.message || err.error || err
      dispatch({ type: ON_SEND_EMAIL_FAILURE, error: message })
      reject({ error: langMessages[message] || message })
    }

    getMsaClient()(dispatch).then(async client => {
      const message = {
        subject,
        toRecipients: to.split(',').map(r => ({
          emailAddress: {
            address: extractEmailAddress(r),
          },
        })),
        body: {
          content: html,
          contentType: 'html',
        },
      }

      if (bcc.split(',').find(r => validateEmail(extractEmailAddress(r)))) {
        message.bccRecipients = bcc.split(',').map(r => ({
          emailAddress: {
            address: extractEmailAddress(r),
          },
        }))
      }
      if (cc.split(',').find(r => validateEmail(extractEmailAddress(r)))) {
        message.ccRecipients = cc.split(',').map(r => ({
          emailAddress: {
            address: extractEmailAddress(r),
          },
        }))
      }

      console.log('sent > message', message)

      try {
        let response = await client.api(`/me/messages/${id}/send`).post()
        console.log('sent > response', response)

        dispatch({ type: ON_SEND_EMAIL_SUCCESS })
        dispatch({ type: EMAIL_SENT_SUCCESSFULLY })

        let email = extractOutlookMessage(
          {
            ...message,
            ...response,
            from: response.from || {
              emailAddress: {
                address: from,
                name: fromName,
              },
            },
          },
          data
        )

        console.log('sendOutlookDraft > sent > email', email)

        return resolve(email)
      } catch (error) {
        sendOutlookMessage(
          mailboxId,
          data
        )(dispatch)
          .then(async email => {
            onDeleteOutlookMessage(mailboxId, 'draft', data)(dispatch)
            return resolve(email)
          })
          .catch(error => {
            onError(error)
          })
      }
    })
  })
}

export const onDeleteOutlookMessage = (mailboxId, folder, data) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    dispatch({ type: ON_DELETE_MAIL, folder, mailboxId })

    console.log('onDeleteOutlookMessage > data', data)

    let { mailId } = data

    if (!mailId) {
      return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: Message deletion 01` })
    }

    getMsaClient()(dispatch).then(async client => {
      try {
        let result = await client.api(`/me/messages/${mailId}`).delete()
        console.log('onDeleteOutlookMessage > result', result)

        dispatch({ type: ON_DELETE_MAIL_SUCCESS, folder, mailboxId, mailId })
        return resolve({ mailId })
      } catch (error) {
        dispatch({ type: ON_DELETE_MAIL_FAILURE, folder, mailboxId, mailId, error })
        return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: Message deletion 02` })
      }
    })
  })
}

export const moveOutlookMessage = (mailboxId, fromFolder, toFolder, userId, mailId) => (dispatch, getState) => {
  dispatch({ type: GET_MAILBOX_LABELS, mailboxId })

  return new Promise((resolve, reject) => {
    getMsaClient()(dispatch).then(async client => {
      let signinStatus = client

      if (!signinStatus) {
        dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER_FAILURE, mailboxId })
        return reject('Usuaário não autenticado no outlook')
      }

      dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER, fromFolder, toFolder, mailboxId })

      var request = client.outlook.users.messages.modify({
        userId,
        id: mailId,
        addLabelIds: [fromFolder.toUpperCase()],
        removeLabelIds: [toFolder.toUpperCase()],
      })

      request.then(response => {
        if (response.status === 200) {
          dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER_SUCCESS, fromFolder, toFolder, mailboxId, mailId })
          return resolve({ mailId })
        }

        dispatch({
          type: ON_EMAIL_MOVE_TO_FOLDER_FAILURE,
          fromFolder,
          toFolder,
          mailboxId,
          mailId,
          error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: move 02`,
        })
        return reject({ error: `${langMessages['errors.genericErrorMsg']}. Cód. Erro: move 02` })
      })
    })
  })
}

export const fetchOutlookFolders = mailboxId => (dispatch, getState) => {
  dispatch({ type: GET_MAILBOX_FOLDERS, mailboxId })

  return new Promise((res, rej) => {
    getMsaClient()(dispatch).then(async client => {
      try {
        let resp = await client.api('/me/mailFolders').get()
        console.log('resp', resp)

        let ignoreFolders = ['Caixa de Entrada', 'Caixa de Saída', 'Itens Enviados', 'Itens Excluídos', 'Lixo Eletrônico', 'Rascunhos']

        let mailboxFolders = (resp.value || [])
          .filter(
            folder =>
              !ignoreFolders.includes(folder.displayName) &&
              !folders.find(
                f =>
                  f.id.toLowerCase() === folder.displayName.toLowerCase() ||
                  (langMessages[`mailboxes.${f.id}`] || '').toLowerCase() === folder.displayName.toLowerCase()
              )
          )
          .map((f, k) => ({
            ...folderModel,
            ...f,
            pos: folders.length + k + 1,
            handle: f.id,
            type: 'inbox',
            icon: 'zmdi zmdi-folder',
            title: f.displayName,
          }))

        dispatch({ type: GET_MAILBOX_FOLDERS_SUCCESS, payload: mailboxFolders, mailboxId })

        FirestoreRef.collection(COLLECTIONS.MAILBOXES_COLLECTION_NAME).doc(mailboxId).update({ folders: mailboxFolders })

        return res(mailboxFolders)
      } catch (error) {
        console.error('error', error)
        dispatch({ type: GET_MAILBOX_FOLDERS_FAILURE, mailboxId })
        return rej(error)
      }
    })
  })
}

export const fetchOutlookLabels = mailboxId => (dispatch, getState) => {
  dispatch({ type: GET_MAILBOX_LABELS, mailboxId })

  return new Promise((res, rej) => {
    getMsaClient()(dispatch).then(async client => {
      try {
        let resp = await client.api('/me/outlook/masterCategories').get()
        console.log('resp', resp)

        let ignoreLabelss = []

        let labels = (resp.value || [])
          .filter(label => !ignoreLabelss.includes(label.displayName))
          .map(f => ({
            ...f,
            title: `mailboxes.labels.${f.displayName}` in langMessages ? langMessages[`mailboxes.labels.${f.displayName}`] : f.displayName,
          }))

        dispatch({ type: GET_MAILBOX_LABELS_SUCCESS, payload: labels, mailboxId })

        FirestoreRef.collection(COLLECTIONS.MAILBOXES_COLLECTION_NAME).doc(mailboxId).update({ labels })

        return res(labels)
      } catch (error) {
        console.error('error', error)
        dispatch({ type: GET_MAILBOX_LABELS_FAILURE, mailboxId })
        return rej(error)
      }
    })
  })
}

export const outlookRequest = path => (dispatch, getState) => {
  return new Promise((res, rej) => {
    getMsaClient()(dispatch).then(async client => {
      try {
        let resp = await client.api(path).get()
        return res(resp)
      } catch (error) {
        return rej(error)
      }
    })
  })
}
