import { GridCard, GridCardRow } from 'Components/index'
import { MOMENT_LOCAL } from 'Constants/AppConstants'
import { getTheDate } from 'Helpers'
import { langMessages } from 'Lang/index'
import React from 'react'
import { Badge } from 'reactstrap'

export const QuickMessagesCard = ({ quick_message, onEdit, onDelete, collection }) => {
    const { ID, message, title, createdAt, updatedAt, enabled, enabledOnAutomation, enabledOnQuickMessage } = quick_message

    return <GridCard
        id={ID}
        title={title || message}
        contents={[
            <GridCardRow
                childs={[
                    {
                        title: langMessages['quickMessages.enabledOnAutomation'],
                        subtitle: (
                            <Badge color={`${enabledOnAutomation ? 'success' : 'danger'}`}>
                                {langMessages[`${enabledOnAutomation ? 'quickMessages.yes' : 'quickMessages.no'}`]}
                            </Badge>
                        ),
                    },
                    {
                        title: langMessages['quickMessages.enabledOnQuickMessage'],
                        subtitle: (
                            <Badge color={`${enabledOnQuickMessage ? 'success' : 'danger'}`}>
                                {langMessages[`${enabledOnQuickMessage ? 'quickMessages.yes' : 'quickMessages.no'}`]}
                            </Badge>
                        ),
                    }
                ]}
            />,
            <GridCardRow
                childs={[
                    {
                        title: langMessages['widgets.dateCreated'],
                        subtitle: getTheDate(createdAt, MOMENT_LOCAL),
                    },
                    {
                        title: langMessages['dates.modified'],
                        subtitle: getTheDate(updatedAt, MOMENT_LOCAL),
                    }
                ]}
            />,
        ]}
        footer={{
            title: message,
            collection,
            //actions,
            onEdit,
            onDelete,
            deleteMessage: langMessages['shotx.dialogs.willBeDeleted'],
        }}
    />
}
