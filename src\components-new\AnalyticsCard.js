import { Card, CardContent, Typography, makeStyles } from '@material-ui/core';
import React from 'react';

import Tooltip from '@material-ui/core/Tooltip';
import HelpOutlineIcon from '@material-ui/icons/HelpOutline';
import { langMessages } from 'Lang/index';
const useStyles = makeStyles((theme) => ({
    card: {
        height: '100%',
        width: '100%',
        transition: 'transform 0.2s ease-in-out',
        '&:hover': {
            transform: 'translateY(-5px)',
        },
    },
    content: {
        padding: theme.spacing(2),
        display: 'flex',
        flexDirection: 'column',
        gap: theme.spacing(1),
        height: '100%',
        minHeight: '120px',
    },
    header: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    icon: {
        opacity: 0.8,
        fontSize: '1.2rem',
    },
    value: {
        marginTop: 'auto',
        fontSize: '1.8rem',
    },
}));

export const AnalyticsCard = ({
    title,
    value,
    icon,
    iconColor = 'rgb(25, 118, 210)'
}) => {
    const classes = useStyles();

    return (
        <Card className={classes.card}>
            <CardContent className={classes.content}>
                <div className={classes.header}>
                    <Typography variant="subtitle1" color="textSecondary" className={classes.header}>
                        {title}

                        {
                            title === langMessages['reports.delivered'] &&
                            <Tooltip title={langMessages["shotx.broadcast.instagram.delivered"]} size='small' className='ml-5'>
                                <HelpOutlineIcon />
                            </Tooltip>}
                    </Typography>
                    <div className={classes.icon} style={{ color: iconColor }}>
                        {icon}
                    </div>
                </div>
                <Typography variant="h4" className={classes.value}>
                    {value}
                </Typography>
            </CardContent>
        </Card>
    );
};
