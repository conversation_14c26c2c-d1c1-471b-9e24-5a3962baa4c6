import Axios from "axios";


export async function getCountries() {
    let countries = await Axios.get("https://servicodados.ibge.gov.br/api/v1/localidades/paises")
        .then(res => {
            return res.data
        })

    countries = countries.map(country => country.nome)

    return Array(countries)
}

export async function getBrazilianStates() {
    let states = await Axios.get("https://servicodados.ibge.gov.br/api/v1/localidades/estados")
        .then(res => {
            return res.data
        })

    states = states.map(state => {
        console.log(state)
        return {
            sigla: state.sigla,
            name: state.nome
        }
    }
    )

    return states
}
