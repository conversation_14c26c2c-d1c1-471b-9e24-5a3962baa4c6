import { Badge } from '@material-ui/core'
import { Loading } from 'Components/Widgets/components/Loading'
import { DialogDefault } from 'Components/dialogDefault/DialogDefault'
import { GridList, PageTitleBar } from 'Components/index'
import { SHOTX_QUICK_MESSAGES_COLLECTION_NAME } from 'Constants/AppCollections'
import { langMessages } from 'Lang/index'
import SearchBar from 'Routes/components/SearchBar'
import IntlMessages from 'Util/IntlMessages'
import React, { useEffect, useState } from 'react'
import { Helmet } from 'react-helmet'
import { useSelector } from "react-redux"
import { withRouter } from 'react-router-dom'
import { QuickMessagesService } from '../../../services/quickmessages'
import { QuickMessagesCard } from '../components/QuickMessagesCard'
import { finish } from '../steps'
import { createMessage } from './create'
import { editMessage } from './edit'

const initialValues = {
    title: '',
    message: '',
    enabled: true,
    loading: false,
    enabledOnQuickMessage: true,
    enabledOnAutomation: true,
}

const defaultResult = {
    error: false,
    message: '',
}

const QuickMessages = ({ match, history }) => {
    const { account } = useSelector(state => state.authReducer)
    const service = new QuickMessagesService(account.id)

    const [messages, setMessages] = useState([])
    const [listenners, setListeners] = useState({})
    const [filterText, setFilterText] = useState('')
    // Getters and Setters
    const items = (filterText ? messages.filter(m => m.message.toLowerCase().includes(filterText.toLowerCase())) : messages) || []

    const [openDialog, setOpenDialog] = useState(false)
    const [editing, setEditing] = useState(false)
    const [step, setStep] = useState(0)
    const [values, setValues] = useState(initialValues)
    const [stepResult, setStepResult] = useState(defaultResult)

    useEffect(() => {
        getItems()

        return () => {
            if (listenners.items) {
                listenners.items()
            }
        }
    }, [])

    const getItems = () => {
        const onSuccess = (items) => {
            setMessages(items)
            setValues({ ...values, loading: false })
        }

        const onError = (error) => {
            console.error(error)
        }

        setValues({ ...values, loading: true })
        const items = service.listen(onSuccess, onError)
        setListeners({ ...listenners, items })
    }

    const create = createMessage({
        values,
        onChange: setValues,
        onForward: () => {
            const { message, enabled, title, enabledOnAutomation, enabledOnQuickMessage } = values
            service.create({ message, enabled, title, enabledOnAutomation, enabledOnQuickMessage })
                .then(() => {
                    setStep(1)
                    setValues({ ...values, loading: false })
                    setStepResult({ error: false, message: langMessages['modules.quick_messages.added'] })
                })
                .catch((error) => {
                    setStepResult({ error: true, message: error.message })
                    console.error("Error Create Message", error)
                })
        }
    })

    const edit = editMessage({
        values,
        onChange: setValues,
        onForward: () => {
            const { message, enabled, id, title, enabledOnAutomation, enabledOnQuickMessage } = values
            service.update(values.id, { message, enabled, id, title, enabledOnAutomation, enabledOnQuickMessage })
                .then(() => {
                    setStep(1)
                    setValues({ ...values, loading: false })
                    setStepResult({ error: false, message: langMessages['modules.quick_messages.changed'] })
                })
                .catch((error) => {
                    setStepResult({ error: true, message: error.message })
                    console.error(error)
                })
        }
    })

    const end = finish({
        onLoad: () => {
            setTimeout(() => {
                if (openDialog) {
                    onDialogClose()
                }
            }, 2000)
        },
        result: stepResult,
        onForward: () => {
            onDialogClose()
        },
    })

    const onAddClick = () => {
        setEditing(false)
        setStep(0)
        setOpenDialog(true)
    }

    const onEditClick = (message) => {
        setEditing(true)
        setStep(0)
        setOpenDialog(true)

        setValues({ ...values, ...message })
    }

    const onDeleteClick = (message) => {
        setValues({ ...values, loading: true })
        service.delete(message.id)
            .then(() => {
                setValues({ ...values, loading: false })
                setStepResult({ error: false, message: langMessages['modules.quick_messages.removed'] })
            })
            .catch((error) => {
                setError({ error: true, message: error.message })
                console.error(error)
            })
    }

    const onDialogClose = () => {
        setOpenDialog(false)
        setEditing(false)
        setValues(initialValues)
        setStep(0)
        setStepResult(defaultResult)
    }

    const createSteps = [create, end]
    const editSteps = [edit, end]
    const currentStep = editing ? editSteps[step] : createSteps[step]

    useEffect(() => {
        if (currentStep.onLoad && !stepResult.error) {
            currentStep.onLoad()
        }
    }, [step])

    return (
        <div className="projects-wrapper">
            <Helmet>
                <title>{langMessages['app.appName'] + ' | ' + langMessages['quickMessages.module.title']}</title>
                <meta name="description" content="QIPlus" />
            </Helmet>
            <PageTitleBar
                title={
                    <div>
                        <Badge badgeContent={items.length} color="error">
                            <IntlMessages id={"quickMessages.module.title"} />{' '}
                        </Badge>
                    </div>
                }
                match={match}
                history={history}
                rightComponents={
                    <SearchBar
                        unWrap
                        reverse
                        filterText={filterText}
                        onChangeFilter={setFilterText}
                        addPostAction={e => setOpenDialog(true)}
                    />
                }
            />
            <div className="main-content">
                <Loading loading={values.loading}>
                    <GridList
                        items={items}
                        collectionName={SHOTX_QUICK_MESSAGES_COLLECTION_NAME}
                        addPostAction={onAddClick}
                        build={(message) => {
                            return <QuickMessagesCard
                                collection={SHOTX_QUICK_MESSAGES_COLLECTION_NAME}
                                quick_message={message}
                                onEdit={onEditClick.bind(this, message)}
                                onDelete={onDeleteClick.bind(this, message)}
                            />
                        }}
                    />
                </Loading>
            </div>
            <DialogDefault
                open={openDialog}
                title={langMessages[`${editing ? 'quickMessages.edit' : 'quickMessages.add'}`]}
                content={currentStep.content}
                isLoading={values.loading}
                onNext={currentStep.onForward}
                disableNext={currentStep.disableNext}
                onClose={onDialogClose}
                nextText={currentStep.buttonText}
                backText={currentStep.backText}
                showdialogactionbutton={false}
            />
        </div>
    )
}

export default withRouter(QuickMessages)
