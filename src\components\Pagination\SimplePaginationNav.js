import { langMessages } from 'Lang/index';
import React from 'react';
import { Pagination, PaginationItem, PaginationLink } from 'reactstrap';

// Componente de Paginação Simples
const SimplePaginationNav = ({ currentPage, totalItems, rowsPerPage, onChange }) => {
  const totalPages = Math.ceil(totalItems / rowsPerPage)
  const isLastPage = currentPage === totalPages;
  const isFirstPage = currentPage === 1;

  const onNext = (e) => {
    e.preventDefault();
    onChange(currentPage + 1);
  };

  const onPrevious = (e) => {
    e.preventDefault();
    onChange(currentPage - 1);
  };

  const makeSummaryText = (string) => {
    return string
      .replace('{from}', currentPage)
      .replace('{count}', totalPages);
  }

  return (
    <div className="d-flex justify-content-between align-items-center mb-0 px-10 py-10">
      <h4 className="text-muted mb-0 ">{makeSummaryText(langMessages['tables.summaryText'])}</h4>

      <Pagination>
        <PaginationItem key={`pagination_${currentPage - 1}`}>
          <PaginationLink
            onClick={onPrevious}
            disabled={isFirstPage}
          >
            {langMessages['tables.previousText']}
          </PaginationLink>
        </PaginationItem>
        <PaginationItem active={true} key={`pagination_${currentPage}`}>
          <PaginationLink
            disabled={true}
          >
            {currentPage}
          </PaginationLink>
        </PaginationItem>
        <PaginationItem key={`pagination_${currentPage + 1}`}>
          <PaginationLink
            onClick={onNext}
            disabled={isLastPage}
          >
            {langMessages['tables.nextText']}
          </PaginationLink>
        </PaginationItem>
      </Pagination>
    </div>
  );
};

export default SimplePaginationNav;
