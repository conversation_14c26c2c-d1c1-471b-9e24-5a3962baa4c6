import React from 'react'

import { FormControl, TextField } from '@material-ui/core'

const RangeWidget = props => {
  const { onChange, label } = props
  console.log(label)


  const handleChange = value => {
    if (!value) {
      onChange('')
      return
    }

    onChange(value)
  }

  return (
    <>
      <FormControl fullWidth className="mt-5">
        <TextField
          variant='outlined'
          className="force-small"
          label={label}
          value={props.value || props.defaultValue}
          required
          onChange={event => {
            event.stopPropagation()

            handleChange(event.target.value)

          }}
        />
      </FormControl>
    </>
  )
}
export default RangeWidget
