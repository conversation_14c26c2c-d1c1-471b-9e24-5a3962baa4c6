import { FirebaseRepository } from "FirebaseRef/repository"
import moment from "moment"

export class QuickMessagesService {

    constructor(accountId) {
        this.accountId = accountId
        this.repository = new FirebaseRepository()
        this.path = `shotx/${accountId}/quick_messages`
    }

    listen = (onSuccess, onError) => {
        return this.repository.listenCollection(this.path, onSuccess, onError)
    }

    create = async (message) => {

        message.createdAt = moment().unix()
        message.updatedAt = moment().unix()
        message.accountId = this.accountId
        message.enabled = true

        return this.repository.addDoc(this.path, message)
            .then(result => {
                const { id } = result
                this.update(id, { id })
                    .then(() => {
                        return { ...message, id }
                    })
                    .catch(err => {
                        console.error(err)
                        return message
                    })
            })
            .catch(err => {
                console.error(err)
                return message
            })
    }

    update = async (id, message) => {
        if (!id) {
            console.error('message id is required')
            return
        }

        message.updatedAt = moment().unix()

        return this.repository.setDoc(`${this.path}/${id}`, message)
    }

    delete = async (id) => {
        return this.repository.deleteDoc(`${this.path}/${id}`)
    }
}
