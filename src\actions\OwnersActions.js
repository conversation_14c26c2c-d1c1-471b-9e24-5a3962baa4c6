/**
 * Owners Actions
 */
import { FirestoreRef } from 'FirebaseRef'

import { langMessages } from '../lang'

import {
  FIRESTORE_ERROR,
  GET_OWNERS,
  GET_OWNERS_SUCCESS,
  GET_OWNERS_FAILURE,
  GET_OWNER,
  GET_OWNER_SUCCESS,
  GET_OWNER_FAILURE,
  GET_QIUSERS_SUCCESS,
} from './types'

import { DOC_DOESNT_EXIST } from '../constants/AppConstants'

import { QIUSERS_COLLECTION_NAME } from '../constants/AppCollections'

import { OWNER_ROLE } from '../constants/UsersRoles'

const collectionName = QIUSERS_COLLECTION_NAME

/**
 * Redux Action Get Owners
 */
export const getOwners = (queries, isListener) => (dispatch, getState) => {
  dispatch({ type: GET_OWNERS })
  return new Promise((resolve, reject) => {
    let QueryRef = FirestoreRef.collection(collectionName).where('roles', 'array-contains', OWNER_ROLE)
    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

    const resolver = snapshot => {
      const posts = snapshot.docs.map(doc => doc.data())
      dispatch({ type: GET_OWNERS_SUCCESS, payload: posts })
      dispatch({ type: GET_QIUSERS_SUCCESS, payload: posts, query: { where: queries, params: undefined, roles: [OWNER_ROLE] } })
      return resolve(posts)
    }

    const onError = error => {
      console.error(error)
      dispatch({ type: GET_OWNERS_FAILURE })
      dispatch({ type: FIRESTORE_ERROR, error })
      return reject({ message: langMessages['errors.dbErrorMsg'] })
    }

    if (isListener) {
      QueryRef.onSnapshot(resolver, onError)
    } else {
      QueryRef.get().then(resolver).catch(onError)
    }
  })
}

/**
 * Redux Action Get Owner
 */
export const getOwner = ownerId => (dispatch, getState) => {
  dispatch({ type: GET_OWNER })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${ownerId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          dispatch({ type: GET_OWNER_FAILURE })
          return reject({ message: langMessages[`${collectionName}.inexistsMsg`], type: DOC_DOESNT_EXIST })
        }
        dispatch({ type: GET_OWNER_SUCCESS, payload: doc.data() })
        return resolve(doc.data())
      })
      .catch(error => {
        dispatch({ type: GET_OWNER_FAILURE })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject()
      })
  })
}
