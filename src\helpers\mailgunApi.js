
import FormData from 'form-data';
import fetch from 'node-fetch';

function validURL(str) {
  var pattern = new RegExp('^(https?:\\/\\/)?' + // protocol
    '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
    '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
    '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
    '(\\#[-a-z\\d_]*)?$', 'i'); // fragment locator
  return !!pattern.test(str);
}


export async function CreateDomain(domain) {

  if (!validURL(domain)) {
    return false
  }

  const form = new FormData();
  form.append('name', domain);

  const username = "api"
  const password = process.env.REACT_APP_MAILGUN_API
  const url = process.env.REACT_APP_MAILGUN_API_URL

  const resp = await fetch(
    url,
    {
      method: 'POST',
      headers: {
        Authorization: 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
      },
      body: form
    }
  );

  const data = await resp.json();

  return resp.ok ? data : { error: true, message: data.message }
}

export async function getDetail(domain) {

  const username = "api"
  const password = process.env.REACT_APP_MAILGUN_API
  const url = process.env.REACT_APP_MAILGUN_API_URL


  const name = domain;
  const resp = await fetch(
    `${url}/${name}`,
    {
      method: 'GET',
      headers: {
        Authorization: 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
      }
    }
  );

  const data = await resp.json();
  return data
}


export async function deleteDomain(domain) {

  const username = "api"
  const password = process.env.REACT_APP_MAILGUN_API

  const resp = await fetch(
    `https://api.mailgun.net/v3/domains/${domain}`,
    {
      method: 'DELETE',
      headers: {
        Authorization: 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
      }
    }
  );

  const data = await resp.json();
  return resp.ok ? data : { error: true, message: data.message }
}

export async function checkMail(domain) {

  const username = "api"
  const password = process.env.REACT_APP_MAILGUN_API
  const url = process.env.REACT_APP_MAILGUN_API_URL

  const resp = await fetch(
    `${url}/${domain}/verify`,
    {
      method: 'PUT',
      headers: {
        Authorization: 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
      }
    }
  );

  const data = await resp.json();
  return data
}


export async function getAllDomain() {

  const username = "api"
  const password = process.env.REACT_APP_MAILGUN_API
  const url = process.env.REACT_APP_MAILGUN_API_URL

  const resp = await fetch(
    `https://api.mailgun.net/v4/domains`,
    {
      method: 'GET',
      headers: {
        Authorization: 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
      }
    }
  );

  const data = await resp.text();
  return data
}
