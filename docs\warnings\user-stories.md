# User Stories - Funcionalidade de Warnings

## Epic: Gerenciamento de Avisos no Sistema QIPlus

Como webmaster do sistema QIPlus, eu quero poder gerenciar avisos através de uma interface web para comunicar informações importantes aos usuários tanto por email quanto através do aplicativo.

---

## US001 - Acessar Menu de Avisos

**Como** webmaster  
**Eu quero** acessar o menu de avisos através do menu "Planos QIPlus"  
**Para que** eu possa gerenciar os avisos do sistema  

### Critérios de Aceitação
- [ ] O menu "Avisos" deve estar visível apenas para usuários com nível webmaster
- [ ] O menu deve estar localizado em "Planos QIPlus > Avisos"
- [ ] O ícone do menu deve ser consistente com o design system (ti-announcement)
- [ ] Ao clicar no menu, devo ser redirecionado para a listagem de avisos

### Definição de Pronto
- Menu configurado no NavLinks.js
- Permissões de acesso implementadas
- Rota configurada corretamente
- Traduções adicionadas (PT/EN)

---

## US002 - Visualizar Lista de Avisos

**Como** webmaster  
**Eu quero** visualizar todos os avisos cadastrados em uma lista  
**Para que** eu possa ter uma visão geral dos avisos existentes  

### Critérios de Aceitação
- [ ] A listagem deve exibir avisos em formato de cards
- [ ] Cada card deve mostrar: título, conteúdo do app (resumido), data de criação, data de modificação, autor e status
- [ ] Deve haver indicador visual para avisos ativos/inativos
- [ ] Deve haver botão "Novo Aviso" para criar avisos
- [ ] Cada card deve ter botões "Visualizar" e "Editar"
- [ ] A interface deve ser responsiva

### Definição de Pronto
- Componente WarningsList implementado
- Cards seguindo padrão visual do sistema
- Dados mockados funcionando
- Interface responsiva testada

---

## US003 - Criar Novo Aviso

**Como** webmaster  
**Eu quero** criar um novo aviso  
**Para que** eu possa comunicar informações importantes aos usuários  

### Critérios de Aceitação
- [ ] Deve haver formulário com campos obrigatórios: título, conteúdo para email, conteúdo para app
- [ ] Deve haver campo opcional para definir status ativo/inativo
- [ ] O autor deve ser preenchido automaticamente com o usuário logado
- [ ] Deve haver validação de campos obrigatórios
- [ ] Deve haver feedback visual de sucesso/erro
- [ ] Após salvar com sucesso, devo ser redirecionado para a listagem

### Definição de Pronto
- Componente WarningDetail implementado para criação
- Validação de formulário funcionando
- Feedback visual implementado
- Redirecionamento após salvamento

---

## US004 - Editar Aviso Existente

**Como** webmaster  
**Eu quero** editar um aviso existente  
**Para que** eu possa atualizar informações ou corrigir erros  

### Critérios de Aceitação
- [ ] Ao clicar em "Editar" na listagem, devo ser redirecionado para o formulário de edição
- [ ] O formulário deve vir preenchido com os dados atuais do aviso
- [ ] Deve mostrar data de criação e última modificação (somente leitura)
- [ ] Deve permitir alterar todos os campos editáveis
- [ ] A data de modificação deve ser atualizada automaticamente ao salvar
- [ ] Deve haver validação de campos obrigatórios

### Definição de Pronto
- Rota de edição configurada
- Carregamento de dados para edição
- Atualização de data de modificação
- Validação funcionando

---

## US005 - Visualizar Detalhes do Aviso

**Como** webmaster  
**Eu quero** visualizar todos os detalhes de um aviso  
**Para que** eu possa revisar o conteúdo completo antes de tomar ações  

### Critérios de Aceitação
- [ ] Ao clicar em "Visualizar" na listagem, devo ver todos os detalhes do aviso
- [ ] Deve mostrar título, conteúdo completo para email e app
- [ ] Deve mostrar metadados: autor, datas de criação/modificação, status
- [ ] Deve haver botão para editar o aviso
- [ ] Deve haver botão para voltar à listagem

### Definição de Pronto
- Modo visualização implementado
- Todos os campos sendo exibidos
- Navegação entre visualização e edição
- Botão de retorno funcionando

---

## US006 - Ativar/Desativar Aviso

**Como** webmaster  
**Eu quero** ativar ou desativar um aviso  
**Para que** eu possa controlar quais avisos estão sendo exibidos aos usuários  

### Critérios de Aceitação
- [ ] Deve haver checkbox "Aviso ativo" no formulário de criação/edição
- [ ] Na listagem, avisos inativos devem ter indicação visual diferenciada
- [ ] O status deve ser salvo corretamente
- [ ] Deve haver feedback visual do status atual

### Definição de Pronto
- Campo active implementado no modelo
- Checkbox funcionando no formulário
- Indicação visual na listagem
- Persistência do status

---

## US007 - Validar Campos Obrigatórios

**Como** webmaster  
**Eu quero** receber feedback quando não preencher campos obrigatórios  
**Para que** eu possa corrigir os dados antes de salvar  

### Critérios de Aceitação
- [ ] Campos obrigatórios: título, conteúdo para email, conteúdo para app
- [ ] Deve haver indicação visual de campos obrigatórios (*)
- [ ] Ao tentar salvar sem preencher campos obrigatórios, deve mostrar mensagens de erro
- [ ] As mensagens de erro devem ser claras e específicas
- [ ] Os erros devem desaparecer quando o campo for preenchido

### Definição de Pronto
- Validação implementada para todos os campos obrigatórios
- Mensagens de erro específicas
- Feedback visual adequado
- Limpeza de erros funcionando

---

## US008 - Diferenciar Conteúdo Email vs App

**Como** webmaster  
**Eu quero** criar conteúdos diferentes para email e aplicativo  
**Para que** eu possa adaptar a mensagem para cada canal de comunicação  

### Critérios de Aceitação
- [ ] Deve haver campo específico "Conteúdo para Email" (pode ser mais detalhado)
- [ ] Deve haver campo específico "Conteúdo para App" (deve ser mais conciso)
- [ ] Deve haver orientação sobre a diferença entre os campos
- [ ] Na listagem, deve mostrar apenas o conteúdo do app (resumido)

### Definição de Pronto
- Dois campos distintos implementados
- Orientações/placeholders adequados
- Listagem mostrando conteúdo correto
- Diferenciação clara na interface

---

## US009 - Responsividade da Interface

**Como** webmaster  
**Eu quero** que a interface funcione bem em diferentes tamanhos de tela  
**Para que** eu possa gerenciar avisos de qualquer dispositivo  

### Critérios de Aceitação
- [ ] A listagem deve se adaptar a telas pequenas (mobile)
- [ ] Os cards devem se reorganizar adequadamente
- [ ] O formulário deve ser usável em tablets
- [ ] Botões e links devem ter tamanho adequado para touch
- [ ] Não deve haver scroll horizontal desnecessário

### Definição de Pronto
- Interface testada em diferentes resoluções
- Cards responsivos funcionando
- Formulário adaptável
- Navegação touch-friendly

---

## US010 - Feedback Visual de Ações

**Como** webmaster  
**Eu quero** receber feedback visual das minhas ações  
**Para que** eu saiba se as operações foram realizadas com sucesso  

### Critérios de Aceitação
- [ ] Ao salvar um aviso, deve mostrar mensagem de sucesso
- [ ] Durante operações de carregamento, deve mostrar indicador de progresso
- [ ] Durante salvamento, botões devem ficar desabilitados
- [ ] Mensagens de sucesso devem desaparecer automaticamente
- [ ] Deve haver feedback para operações de erro

### Definição de Pronto
- LinearProgress implementado para carregamento
- Alert de sucesso funcionando
- Estados de loading nos botões
- Auto-dismiss de mensagens
- Tratamento de erros

---

## Critérios de Aceitação Gerais

### Funcionalidade
- [x] Tela acessível através do menu Planos QIPlus > Avisos
- [x] Listagem exibe: title, createdAt, updatedAt, status active
- [x] Utiliza cards padrões do sistema
- [x] Botão para criar novos avisos funcional
- [x] Interface responsiva e consistente
- [x] Dados mockados funcionando corretamente

### Técnico
- [x] Componentes implementados como function components
- [x] Uso adequado de hooks (useState, useEffect)
- [x] Integração com Redux para dados do usuário
- [x] Roteamento configurado corretamente
- [x] Traduções implementadas (PT/EN)
- [x] Permissões de acesso implementadas

### UX/UI
- [x] Interface consistente com o design system
- [x] Feedback visual adequado
- [x] Navegação intuitiva
- [x] Responsividade implementada
- [x] Acessibilidade básica

---

## Histórias Futuras (Backlog)

### US011 - Integração com Firebase
**Como** webmaster, eu quero que os avisos sejam persistidos no Firebase para que os dados sejam mantidos entre sessões.

### US012 - Sistema de Notificações
**Como** usuário do sistema, eu quero receber notificações automáticas quando novos avisos forem criados.

### US013 - Agendamento de Avisos
**Como** webmaster, eu quero agendar avisos para serem ativados em datas específicas.

### US014 - Templates de Avisos
**Como** webmaster, eu quero usar templates pré-definidos para criar avisos mais rapidamente.

### US015 - Histórico de Alterações
**Como** webmaster, eu quero ver o histórico de alterações de cada aviso para auditoria.

### US016 - Filtros e Busca
**Como** webmaster, eu quero filtrar e buscar avisos na listagem para encontrar informações específicas.

### US017 - Preview de Conteúdo
**Como** webmaster, eu quero visualizar como o aviso ficará no email e no app antes de salvar.

### US018 - Editor Rich Text
**Como** webmaster, eu quero usar um editor rich text para formatar o conteúdo dos avisos.

### US019 - Upload de Imagens
**Como** webmaster, eu quero adicionar imagens aos avisos para torná-los mais atrativos.

### US020 - Categorização de Avisos
**Como** webmaster, eu quero categorizar avisos (manutenção, novidades, alertas) para melhor organização.
