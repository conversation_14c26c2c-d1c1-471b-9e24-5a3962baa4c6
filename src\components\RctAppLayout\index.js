/**
 * App Routes
 */
import React, { Component } from 'react'
import { connect } from 'react-redux'
import { with<PERSON>out<PERSON> } from 'react-router-dom'
import Sidebar from 'react-sidebar'

// actions
import {
  collapsedSidebarAction,
  deleteDeskNotification,
  listenChatMessages,
  listenChatUsers,
  listenDeskNotifications,
  listenMyChatMessages,
  onToggleDeskNotifications,
  startUserTour,
  updateDeskNotification,
  updateDeskNotifications,
} from '../../actions'

import { createTestDeskNotifications } from '../../actions/DeskNotificationsActions'


// langMessages
import { langMessages } from '../../lang'

// app config
import AppConfig from 'Constants/AppConfig'

import classnames from 'classnames'
import $ from 'jquery'
import { Scrollbars } from 'react-custom-scrollbars'

// Components
import Footer from 'Components/Footer/Footer'
import Header from 'Components/Header/Header'
import SidebarContent from 'Components/Sidebar'
import ThemeOptions from 'Components/ThemeOptions/ThemeOptions'
import UserBlock from '../Sidebar/UserBlock'

// preload Components
import PreloadHeader from 'Components/PreloadLayout/PreloadHeader'
import PreloadSidebar from 'Components/PreloadLayout/PreloadSidebar'

import DeskNotificationsPopper from 'Widgets/DeskNotificationsPopper'

import { Alert } from 'reactstrap'

const desktopBreakpoint = 1280
const mobileContentWidth = `width=${desktopBreakpoint}, height=800, maximum-scale=1.0, user-scalable=1, shrink-to-fit=no`
const defaultContentWidth = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no'

class MainApp extends Component {
  state = {
    loadingHeader: true,
    loadingSidebar: true,
    windowWidth: $(window).width(),
    windowHeight: $(window).height(),
    mobileViewportDetected: sessionStorage.getItem('mobileViewportDetected'),
    layoutMsg: '',
  }

  componentDidMount() {
    this.updateDimensions()
    const { user } = this.props
    const { windowWidth } = this.state
    window.addEventListener('resize', this.updateDimensions)
    if (AppConfig.enableUserTour && windowWidth > 600) {
      setTimeout(() => {
        this.props.startUserTour()
      }, 2000)
    }
    setTimeout(() => {
      this.setState({ loadingHeader: false, loadingSidebar: false })
    }, 114)
    this.props.listenDeskNotifications()

    // Expose test function to window for testing purposes
    // Usage: window.createTestAlerts() in browser console
    window.createTestAlerts = () => {
      this.props.createTestDeskNotifications()
        .then(() => console.log('Test alerts created successfully!'))
        .catch(err => console.error('Error creating test alerts:', err))
    }

    this.props.listenMyChatMessages()
    this.props.listenChatUsers(user.ID, chats => {
      chats
        .filter(c => c.ID !== user.ID)
        .forEach(chat => {
          this.props.listenChatMessages(chat.id, chat.isGroup)
        })
    })
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.updateDimensions)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { windowWidth } = this.state
    if (nextProps.location !== this.props.location) {
      if (windowWidth <= 1199) {
        this.props.collapsedSidebarAction(false)
      }
    }
  }

  updateDimensions = () => {
    this.setState({ windowWidth: $(window).width(), windowHeight: $(window).height() })

    if (window.innerWidth < desktopBreakpoint) {
      $('meta[name="viewport"]').attr('content', mobileContentWidth)
    } else {
      $('meta[name="viewport"]').attr('content', defaultContentWidth)
    }
  }

  componentDidUpdate(prevProps) {
    if (this.props.location.pathname !== prevProps.location.pathname) {
      window.scrollTo(0, 0)
    }
    if (!this.state.layoutMsg && !this.state.mobileViewportDetected && window.innerWidth < desktopBreakpoint) {
      this.setState({ layoutMsg: langMessages['alerts.mobileViewportDetected'], mobileViewportDetected: 1 })
    }
  }

  renderPage() {
    const { pathname } = this.props.location
    const { children } = this.props
    if (!pathname.startsWith('/mailing') && (pathname === '/chat' || pathname.startsWith('/mail') || pathname === '/todo')) {
      return <div className="rct-page-content p-0">{children}</div>
    }
    return (
      <Scrollbars className="rct-scroll" id="main-scroller" autoHide autoHideDuration={100} style={this.getScrollBarStyle()}>
        <div className="rct-page-content">
          {
            <Alert
              color="warning"
              className="bg-gradient-rss text-white p-15 pr-30"
              isOpen={!!this.state.layoutMsg}
              toggle={() => {
                this.setState({ layoutMsg: '' })
                sessionStorage.setItem('mobileViewportDetected', 1)
              }}
              style={{ maxWidth: window.outerWidth - $('.rct-sidebar').outerWidth() }}
            >
              <div className="d-flex justify-content-between">
                <span className="alert-addon pr-15">
                  <i className="zmdi zmdi-alert-triangle"></i>
                </span>
                <p>{this.state.layoutMsg}</p>
              </div>
            </Alert>
          }
          {children}
          <Footer />
        </div>
      </Scrollbars>
    )
  }

  // render header
  renderHeader() {
    const { loadingHeader } = this.state
    if (loadingHeader) {
      return <PreloadHeader />
    }
    return <Header />
  }

  //render Sidebar
  renderSidebar() {
    const { loadingSidebar } = this.state
    if (loadingSidebar) {
      return <PreloadSidebar />
    }
    return <SidebarContent />
  }

  //Scrollbar height
  getScrollBarStyle() {
    const headerHeight = $('#app-header').outerHeight() || 0
    return {
      height: `calc(100vh - ${headerHeight}px)`,
    }
  }

  // function to change the state of collapsed sidebar
  onToggleNavCollapsed = event => {
    const { windowWidth } = this.state
    if (windowWidth <= 992) {
      return
    }
    const collapsed = !this.props.settings.navCollapsed
    this.props.collapsedSidebarAction(collapsed)
  }

  render() {
    const { navCollapsed, rtlLayout, miniSidebar } = this.props.settings
    const { windowWidth } = this.state
    return (
      <div className="app">
        <div className="app-main-container">
          {/* <Tour /> */}
          <Sidebar
            sidebar={this.renderSidebar()}
            open={miniSidebar ? false : windowWidth <= 1199 ? navCollapsed : false}
            docked={miniSidebar ? true : windowWidth > 1199 ? !navCollapsed : false}
            // open={windowWidth <= 1199 ? navCollapsed : false}
            // docked={windowWidth > 1199 ? !navCollapsed : false}
            pullRight={rtlLayout}
            onSetOpen={() => this.props.collapsedSidebarAction(false)}
            styles={{ content: { overflowY: '' } }}
            contentClassName={classnames({ 'app-container-wrapper': miniSidebar })}
          >
            <div className="app-container">
              <div className="rct-app-content">
                {/*
                                <div className="responsive-sidebar-toggler" onClick={(e) => this.onToggleNavCollapsed(e)}>
                                    <Tooltip title={langMessages["sidebar.toggle"]} placement="bottom">
                                        <IconButton color="inherit" mini="true" aria-label="Menu" className="humburger p-0">
                                            <MenuIcon />
                                        </IconButton>
                                    </Tooltip>
                                </div>
                                */}
                {/*
                                <div className="app-header">
                                    {this.renderHeader()}
                                </div>
                                */}
                <div className="rct-page" id="page">
                  {this.renderPage()}
                </div>
              </div>
            </div>
          </Sidebar>
          <DeskNotificationsPopper {...this.props} />
          <UserBlock />
          <ThemeOptions {...this.props} />
        </div>
      </div>
    )
  }
}

// map state to props
const mapStateToProps = ({ settings, authReducer: { user }, sidebarReducer: { deskNotifications, notificationsToggle } }) => {
  return { settings, user, deskNotifications, notificationsToggle }
}

export default withRouter(
  connect(mapStateToProps, {
    collapsedSidebarAction,
    listenChatUsers,
    listenMyChatMessages,
    listenChatMessages,
    listenDeskNotifications,
    updateDeskNotification,
    updateDeskNotifications,
    deleteDeskNotification,
    onToggleDeskNotifications,
    startUserTour,
    createTestDeskNotifications,
  })(MainApp)
)
