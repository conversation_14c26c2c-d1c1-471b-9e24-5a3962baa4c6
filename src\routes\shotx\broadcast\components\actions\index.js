import { MOMENT_ISO } from "Constants";
import { SHOTX_CRON_COLLECTION_NAME } from "Constants/AppCollections";
import moment from "moment";
import { FirestoreRef } from '../../../../../firebase/index';
export function deleteBroadcast(id) {

    updateBroadcast(id, { status: "canceled", modified_date: moment().format(MOMENT_ISO) });
}

export function updateBroadcast(id, schedule) {
    FirestoreRef.collection(SHOTX_CRON_COLLECTION_NAME)
        .doc(id)
        .update(schedule)
        .then(() => {
            return true
        })
        .catch(function (error) {
            setSnackbar({
                message: langMessages['messagesBroadcast.error'],
                severity: 'success',
            });
            console.log('error', error);
        })
}
