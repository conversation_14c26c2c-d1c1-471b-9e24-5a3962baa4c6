import { <PERSON><PERSON>, ButtonBase, Card, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, IconButton, Menu, MenuItem, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { MoreHoriz } from '@material-ui/icons';
import { langMessages } from 'Lang/index';
import { Bar<PERSON>hart, BookCheck, Clock, Database, Eye, MessageSquare, Pencil, User } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import { TimezoneUtil } from '../../../util/country/timezone';
import { deleteBroadcast } from '../broadcast/components/actions';
import { GradientIcon } from './GradientIcon';

// const dark = DARK_MODE;
const useStyles = makeStyles((theme) => ({
    card: {
        border: '1px solid #e2e8f0',
        padding: theme.spacing(3),
        transition: 'all 0.3s ease',
        '&:hover': {
            borderColor: '#e9177c',
        },
    },
    title: {
        fontSize: '1.125rem',
        fontWeight: 600,
        marginBottom: theme.spacing(2),
    },
    titleComponets: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    infoContainer: {
        display: 'flex',
        flexDirection: 'column',
        gap: theme.spacing(2),
        marginBottom: theme.spacing(2),
    },
    infoItem: {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(1.5),
    },
    infoText: {
        '& > div:first-child': {
            fontSize: '0.875rem',
        },
        '& > div:last-child': {
            // color: '#1e293b',
            // '.dark &': {
            //     color: '#fff',
            // },
            fontSize: '1.0rem',
        },
    },
    actions: {
        display: 'flex',
        justifyContent: 'center',
        gap: theme.spacing(6),
        paddingTop: theme.spacing(2),
        borderTop: '1px solid #e2e8f0',
        '.dark &': {
            borderTop: '1px solid rgba(107, 114, 128, 0.5)',
        }
    },
    actionButton: {
        color: '#3B82F6',
        '&:hover': {
            backgroundColor: '#f1f5f9',
        },
        '.dark &': {
            '&:hover': {
                backgroundColor: '#1A1F2C',
            },
        }
    },
    icon: {
        width: 16,
        height: 16,
        color: '#64748b',
        '.dark &': {
            color: 'white',
        }
    },
}));

export const MessageCard = ({ message }) => {
    const history = useHistory();
    const classes = useStyles();

    const scheduledAt = TimezoneUtil.formatDate(TimezoneUtil.convertToUserTimezone(message.scheduled_date), langMessages['format.date.full.short.year']);
    const createdAt = TimezoneUtil.formatDate(TimezoneUtil.convertToUserTimezone(message.createdAt), langMessages['format.date.full.short.year']);

    const [anchorEl, setAnchorEl] = useState(null);
    const [openDialog, setOpenDialog] = useState(false);
    const [disabledCancel, setDisabledCancel] = useState(false);
    // const [status, setStatus] = useState(message.status);
    const [statusName, setStatusName] = useState();
    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const cancelBroadcast = () => {
        setAnchorEl(null);
        deleteBroadcast(message.id);
        handleCloseDialog();
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
    };

    const handleCancel = () => {
        setOpenDialog(true);
        handleClose();
    };

    useEffect(() => {
        switch (message.status) {
            case 'canceled':
                setDisabledCancel(true);
                setStatusName(langMessages['widgets.cancelled'])
                break;
            case 'published':
                setStatusName(langMessages['statuses.publish'])
                break
            case 'sent':
                setDisabledCancel(true);
                setStatusName(langMessages['shotx.broadcast.status.sent'])
                break;
            default:
                setStatusName(langMessages['statuses.publish'])
                break;
        }
    })

    const checkBroadcast = (typeChecked) => {
        typeChecked.view === true ? history.push(`/shotx/broadcast/view/${message.id}`, { message: message, type: typeChecked }) :
            history.push(`/shotx/broadcast/edit/${message.id}`, { message: message, type: typeChecked })
    }

    const handleRedirectToAnalytics = () => {
        history.push(`/shotx/broadcast/analytics/${message.id}`, { message: message })
    }

    return (
        <Card className={classes.card} >
            <div id='title' className={classes.titleComponets}>
                <Typography className={classes.title}>
                    {message.type.toUpperCase()} - {scheduledAt}
                </Typography>
                <IconButton className='mb-3'
                    onClick={handleClick}
                >
                    <MoreHoriz />
                </IconButton>
                <Menu
                    anchorEl={anchorEl}
                    keepMounted
                    open={Boolean(anchorEl)}
                    onClose={handleClose}
                >
                    <MenuItem onClick={handleCancel}
                        disabled={disabledCancel}
                    >
                        <ButtonBase>
                            {
                                langMessages['shotx.broadcast.cancel']
                            }
                        </ButtonBase>
                    </MenuItem>
                </Menu>

            </div>

            <div className={classes.infoContainer}>
                <div className={classes.infoItem}>
                    <GradientIcon>
                        <User className="w-4 h-4" />
                    </GradientIcon>
                    <div className={classes.infoText}>
                        <div>{langMessages['shotx.broadcast.leadsQtd']}</div>
                        <div>{message.contacts.length || 0}</div>
                    </div>
                </div>

                <div className={classes.infoItem}>
                    <GradientIcon>
                        <Database className="w-4 h-4" />
                    </GradientIcon>
                    <div className={classes.infoText}>
                        <div>{langMessages['shotx.broadcast.segmetationsQtd']}</div>
                        <div>{message?.segmentations?.length || 0}</div>
                    </div>
                </div>

                <div className={classes.infoItem}>
                    <GradientIcon>
                        <MessageSquare className="w-4 h-4" />
                    </GradientIcon>
                    <div className={classes.infoText}>
                        <div>{langMessages['shotx.whatsapp.instance.body.title']}</div>
                        <div>{message.instance.title}</div>
                    </div>
                </div>

                <div className={classes.infoItem}>
                    <GradientIcon>
                        <Clock className="w-4 h-4" />
                    </GradientIcon>
                    <div className={classes.infoText}>
                        <div>{langMessages['createdAt']}</div>
                        <div>{createdAt}</div>
                    </div>
                </div>
                <div className={classes.infoItem}>
                    <GradientIcon>
                        <BookCheck className="w-4 h-4" />
                    </GradientIcon>
                    <div className={classes.infoText}>
                        <div>{langMessages['widgets.status']}</div>
                        <div>{statusName}</div>
                    </div>
                </div>
            </div>

            <div className={classes.actions}>
                {
                    message.status === 'sent' &&
                    <Button
                        className={classes.actionButton}
                        startIcon={<BarChart className="w-3 h-3" />}
                        onClick={() => handleRedirectToAnalytics()}
                    >
                        Analytics
                    </Button>
                }
                {message.status === 'published' ? (
                    <Button
                        className={classes.actionButton}
                        startIcon={<Pencil className="w-3 h-3" />}
                        onClick={() => checkBroadcast({ edit: true, view: false })}
                    >
                        {langMessages['button.edit']}
                    </Button>
                ) : (
                    < Button
                        className={classes.actionButton}
                        startIcon={<Eye className="w-3 h-3" />}
                        onClick={() => checkBroadcast({ view: true, edit: false })}
                    >
                        {langMessages['button.view']}
                    </Button>
                )
                }
            </div>
            {/* Diálogo de Confirmação */}
            <Dialog
                open={openDialog}
                onClose={handleCloseDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">{langMessages['shotx.broadcast.cancel.dialog.title']} </DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        {langMessages['shotx.broadcast.cancel.dialog.description']}
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog} color="primary">
                        {langMessages['quickMessages.no']}
                    </Button>
                    <Button onClick={cancelBroadcast} autoFocus>
                        {langMessages['quickMessages.yes']}
                    </Button>
                </DialogActions>
            </Dialog>
        </Card >
    );
};

