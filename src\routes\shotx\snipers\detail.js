import { Snac<PERSON><PERSON>, Tab, Tabs } from '@material-ui/core'
import { Alert } from '@material-ui/lab'
import RctSectionLoader from 'Components/RctSectionLoader/RctSectionLoader'
import { PageTitleBar } from 'Components/index'
import { langMessages } from 'Lang/index'
import SavePostNavBar from 'Routes/components/SavePostNavBar'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { withRouter } from 'react-router-dom'
import { SniperService } from '../../../services/sniperService'
import { Settings } from './components/Settings'

const SniperDetailComponent = ({ match, history }) => {

  const { params: { id } } = match || {}
  const newPost = !id

  const { account } = useSelector(state => state.authReducer)
  const service = new SniperService(account.id)

  const [savingPost, setSavingPost] = useState(false)
  const [errorSavingPost, setErrorSavingPost] = useState(false)
  const [fetchingPosts, setFetchingPosts] = useState(false)
  const [snackBarMessage, setSnackBarMessage] = useState(null)

  const urlSearchParams = new URLSearchParams(window.location.search);
  const params = Object.fromEntries(urlSearchParams.entries());

  // params:

  // {
  //   public_id: 'meu-sniper-mjhivzc',
  //     name: 'Meu sniper',
  //       key: '6wjWfZ4y7a5IGP1z0FiyikCR'
  // }


  const postModel = {
    id: id || '',
    title: params?.name || '',
    publicSniper: params?.public_id || '',
    triggerTypes: 'all',
    triggerOperator: '',
    triggerValue: '',
    keywordFinish: '',
    apiKey: params?.key || '',
    unknownMessage: '',
    leadVarsAssociation: [],
    updateLead: false,
  }

  const [post, setPost] = useState(postModel)

  const activeTab = 'settings'
  const tabs = {
    settings: langMessages['components.settings'],
  }

  // function useQuery() {
  //   const { search } = useLocation();

  //   return React.useMemo(() => new URLSearchParams(search), [search]);
  // }

  useEffect(() => {
    if (newPost) {

      // const urlSearchParams = new URLSearchParams(window.location.search);
      // const params = Object.fromEntries(urlSearchParams.entries());

      // console.log("params ", params)

      setPost(postModel)
    } else {
      getPost(id)
    }
  }, [])

  const getPost = (id) => {
    setFetchingPosts(true)
    setSavingPost(true)

    const onSuccess = (items) => {
      setSavingPost(false)
      if (items.length > 0) {
        setPost(items[0])
        setFetchingPosts(false)
      }
    }

    const onError = (error) => {
      console.error(error)
      setFetchingPosts(false)
    }

    service.get(id, onSuccess, onError)
  }

  const handleValidatePost = (values) => {

    console.log('handleValidatePost', values)
    const date = moment().format('DD/MM HH:mm:ss')

    if (!values.title) {
      const value = `Sniper - ${date}`
      handleUpdateTitle(value)
    }

    if (!values.triggerTypes) {
      setPost({ ...post, triggerTypes: "all" })
    }

    if (!values.publicSniper) {
      setSnackBarMessage({
        message: langMessages['shotx.snipers.form.withoutTitle'],
        severity: 'warning',
      })
      return false
    }

    if ((!values.apiKey || values.apiKey.length <= 0) && values.updateLead) {
      setSnackBarMessage({
        message: langMessages['shotx.snipers.form.withoutApiKey'],
        severity: 'warning',
      })
      return false
    }


    if (values.updateLead) {
      if (!values.leadVarsAssociation || values.leadVarsAssociation.length <= 0) {
        setSnackBarMessage({
          message: 'Por favor selecione pelo menos uma variável ou desative a atualização de leads',
          severity: 'warning',
        })
        return false
      }
      let check = true
      values.leadVarsAssociation.forEach((item) => {
        if (Object.values(item.qiplus).length === 0 || Object.values(item.sniper).length === 0) {
          setSnackBarMessage({
            message: 'Por favor preencha todas as variáveis ou desative a atualização de leads',
            severity: 'warning',
          })
          check = false
        }
      })
      return check
    }
    setSnackBarMessage(null)
    return true
  }

  const handleCancelPost = () => {
    history.push(`/shotx/`)
  }

  const handleUpdateTitle = (value) => {
    setPost({ ...post, title: value })
  }

  const handleSavePost = (status) => {
    if (status === 'draft') {
      return handleCancelPost()
    }

    if (!handleValidatePost(post)) return

    setSavingPost(true)

    if (newPost) {
      service.create(post).then((result) => {
        if (result?.id) {
          history.push(`/shotx/snipers/${result.id}`)
        }
        setPost(result)
        setSavingPost(false)
      })
    } else {
      const values = { ...post, id }
      service.update(id, values).then(() => {
        setSavingPost(false)
      }).catch(err => {
        console.error(err)
        setSavingPost(false)
        setErrorSavingPost(true)
      })
    }
  }

  return (
    <>
      <div>

        <Snackbar
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          open={!!snackBarMessage}
          onClose={() => setSnackBarMessage(null)}
          autoHideDuration={5000}
        >
          <Alert onClose={() => setSnackBarMessage(null)} severity={snackBarMessage?.severity}>
            {snackBarMessage?.message}
          </Alert>
        </Snackbar>
      </div>


      <div className="formelements-wrapper">
        <PageTitleBar
          id="title"
          editable
          title={post?.title || ''}
          placeholder={langMessages['shotx.snipers.nomeSniper']}
          match={match}

          onBackClick={e => handleCancelPost()}
          onUpdate={value => handleUpdateTitle(value)}
          savingPost={savingPost}
          errorSavingPost={errorSavingPost}
          rightComponents={
            <SavePostNavBar
              status={'publish'}
              post={post}
              saveDraftLabel={langMessages['button.cancel']}
              savingPost={savingPost}
              errorSavingPost={errorSavingPost}
              onSavePost={status => handleSavePost(status)}
            />
          }
        />
        <Tabs indicatorColor="primary" value={activeTab} onChange={(e, value) => this.handleTabs(value)}>
          {Object.keys(tabs).map((key, index) => (
            <Tab key={index} value={key} label={tabs[key]} />
          ))}
        </Tabs>
        {fetchingPosts ? (
          <RctSectionLoader />
        ) : activeTab === 'settings' ? (
          <Settings
            state={post}
            setState={setPost}
          />
        ) : (
          <div className="row">
            <div className="col-sm-12 col-md-12 col-xl-12"></div>
          </div>
        )}
      </div>
    </>
  )

}

export const SniperDetail = withRouter(SniperDetailComponent)
