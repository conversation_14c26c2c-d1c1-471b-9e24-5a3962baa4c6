/**
 * LandingPages Actions
 */
import { FirestoreRef } from 'FirebaseRef'

import {
  UPDATE_POST,
  VERIFY_USER_SUCCESS,
  SAVE_QIUSER_SUCCESS,
  ADD_LANDING_PAGE,
  ADD_LANDING_PAGE_SUCCESS,
  UPDATE_LANDING_PAGE,
  UPDATE_LANDING_PAGE_SUCCESS,
} from 'Actions/types'

import { qiAjax, localJSON } from 'Helpers/helpers'
import { LANDING_PAGES_COLLECTION_NAME, QIUSERS_COLLECTION_NAME } from 'Constants'

const collectionName = LANDING_PAGES_COLLECTION_NAME

/**
 * Redux Action Get LandingPages
 */
export const addLandingPage = post => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  dispatch({ type: ADD_LANDING_PAGE })

  return new Promise((resolve, reject) => {
    qiAjax({
      data: {
        action: 'landing_page_from_firebase',
        user,
        post,
      },
      success: response => {
        console.log('success > response', response)
        if (response.status === true) {
          const { edit_url, permalink, post_id, wpuser } = response
          const newData = {
            ...post,
            permalink,
            post_id,
          }
          if (wpuser && user.wpuser !== wpuser) {
            FirestoreRef.collection(QIUSERS_COLLECTION_NAME)
              .doc(user.ID)
              .update({ wpuser })
              .then(() => {
                dispatch({ type: VERIFY_USER_SUCCESS, payload: { ...user, wpuser } })
                dispatch({ type: SAVE_QIUSER_SUCCESS, payload: { ...user, wpuser } })
              })
          }
          return FirestoreRef.collection(collectionName)
            .doc(post.ID)
            .update({ edit_url, permalink, post_id: `${post_id}` })
            .then(() => {
              const payload = newData
              dispatch({ type: UPDATE_POST, collection: collectionName, payload })
              dispatch({ type: ADD_LANDING_PAGE_SUCCESS, payload })
              //console.log('landing > resolve > 1');
              return resolve(newData)
            })
            .catch(function (error) {
              //console.log('landing > reject > 2');
              return reject(error)
            })
        } else {
          //console.log('landing > reject > 3');
          return reject({ message: response.error })
        }
      },
      error: err => {
        console.log('error > err', err)
        //console.log('landing > reject > 4');
        return reject({ message: err.statusText })
      },
    })
  })
}

export const updateLandingPage = post => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  dispatch({ type: UPDATE_LANDING_PAGE })

  return new Promise((resolve, reject) => {
    qiAjax({
      data: {
        action: 'landing_page_from_firebase',
        update: true,
        user,
        post,
      },
      success: response => {
        console.log('success > response', response)
        if (response.status === true) {
          const { edit_url, permalink, post_id, wpuser } = response
          const newData = {
            ...post,
            permalink,
            post_id,
          }
          if (wpuser && user.wpuser !== wpuser) {
            FirestoreRef.collection(QIUSERS_COLLECTION_NAME)
              .doc(user.ID)
              .update({ wpuser })
              .then(() => {
                dispatch({ type: VERIFY_USER_SUCCESS, payload: { ...user, wpuser } })
                dispatch({ type: SAVE_QIUSER_SUCCESS, payload: { ...user, wpuser } })
              })
          }
          if (post.edit_url !== edit_url || post.permalink !== permalink || post.post_id !== post_id) {
            return FirestoreRef.collection(collectionName)
              .doc(post.ID)
              .update({ edit_url, permalink, post_id: `${post_id}` })
              .then(() => {
                const payload = newData
                dispatch({ type: UPDATE_POST, collection: collectionName, payload })
                dispatch({ type: UPDATE_LANDING_PAGE_SUCCESS, payload: newData })
                //console.log('landing > resolve > 5');
                return resolve(newData)
              })
              .catch(function (error) {
                //console.log('landing > reject > 6');
                return reject(error)
              })
          }
          dispatch({ type: UPDATE_LANDING_PAGE_SUCCESS, payload: newData })
          //console.log('landing > resolve > 7');
          return resolve(newData)
        } else {
          //console.log('landing > reject > 8');
          return reject({ message: response.error })
        }
      },
      error: err => {
        console.log('error > err', err)
        //console.log('landing > reject > 9');
        return reject({ message: err.statusText })
      },
    })
  })
}

export const cloneLandingPage = post => (dispatch, getState) => {
  const user = localJSON.get('user', false)

  dispatch({ type: ADD_LANDING_PAGE })

  return new Promise((resolve, reject) => {
    if (!post.post_id) return resolve(post)

    qiAjax({
      data: {
        action: 'clone_landing_page',
        update: true,
        user,
        post,
      },
      success: response => {
        console.log('success > response', response)
        if (response.status === true) {
          const { edit_url, permalink, post_id, wpuser } = response
          const newData = {
            ...post,
            permalink,
            post_id,
          }
          if (wpuser && user.wpuser !== wpuser) {
            FirestoreRef.collection(QIUSERS_COLLECTION_NAME)
              .doc(user.ID)
              .update({ wpuser })
              .then(() => {
                dispatch({ type: VERIFY_USER_SUCCESS, payload: { ...user, wpuser } })
                dispatch({ type: SAVE_QIUSER_SUCCESS, payload: { ...user, wpuser } })
              })
          }
          return FirestoreRef.collection(collectionName)
            .doc(post.ID)
            .update({ edit_url: edit_url || '', permalink: permalink || '', post_id: `${post_id || ''}` })
            .then(() => {
              const payload = newData
              dispatch({ type: UPDATE_POST, collection: collectionName, payload })
              dispatch({ type: ADD_LANDING_PAGE_SUCCESS, payload: newData })
              //console.log('landing > resolve > 5');
              return resolve(newData)
            })
            .catch(function (error) {
              //console.log('landing > reject > 6');
              return reject(error)
            })
        } else {
          //console.log('landing > reject > 8');
          return reject({ message: response.error })
        }
      },
      error: err => {
        console.log('error > err', err)
        //console.log('landing > reject > 9');
        return reject({ message: err.statusText })
      },
    })
  })
}
