/**
 * Dasboard Routes
 */
import React from 'react'
import { Route, Switch } from 'react-router-dom'
import { SniperDetail } from './detail'
// async components

export const SniperRoute = ({ match }) => (
  <Switch>
    {/* <Route exact path={`${match.url}/snipers`}>
      <Snipers match={match} />
    </Route> */}
    <Route exact path={`${match.url}/snipers/add`}>
      <SniperDetail match={match} isNewPost={true} />
    </Route>
    <Route exact path={`${match.url}/snipers/:id`}>
      <SniperDetail match={match} isNewPost={false} />
    </Route>

  </Switch>
)
