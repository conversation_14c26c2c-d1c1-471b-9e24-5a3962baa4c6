QIPLUS

Boa tarde! Sou o desenvolvedor responsável pela conta QIPLUS. Tenho algumas dúvidas sobre os splits de pagamentos.

Chat iniciado
Luiz | Pagar.me

entrou no chat
Luiz | Pagar.me

Ol<PERSON>, boa tarde! Tudo bem?
QIPLUS
tudo bem e você?

Luiz | Pagar.me
Vou bem, obrigado por perguntar!

Com quem eu falo, por gentileza?
QIPLUS
Marcelo


Luiz | Pagar.me
Marcel<PERSON>, em que posso lhe ajudar?
QIPLUS
a primeira dúvida é sobre como obter o recipient_id para informar nos split_rules


Luiz | Pagar.me
Você já possui os recebedores cadastrados?
QIPLUS
não. Isso é outra dúvida que tenho

porque o dono da conta, quando contratou o pagarme, havia me informado que era possivel fazer o split apenas com os dados de pagamento, sem que fosse preciso que os recebedores tivessem conta no pagarme


Luiz | Pagar.me
Entendi. É porque há a necessidade de criação desses recebedores para que o split seja efetivo. Os recebedores eles não terão acesso a sua conta, que é um marketplace, tudo será organizado por você, por exemplo, se um recebedor quiser um saque de um saldo disponível, é você quem vai realizar para ele.
QIPLUS
ok, entendi


Luiz | Pagar.me
Temos uma documentação da API que vem com um passo a passo sobre isso, chegaram a te informar alguma vez?
QIPLUS
e o cadastro de recebedores se faz pelo dashboard ou é possível fazer por api também?


Luiz | Pagar.me
Pela API você consegue realizar por aqui: https://docs.pagar.me/v3/reference#criando-um-recebedor
QIPLUS
eu estava vendo a documentação da api para crir assinaturas com split_rules, mas não cheguei a ver essa parte de recebedores


Luiz | Pagar.me
Tem esse passo a passo na documentação.  Inclusive, dá pra ser feito nessa própria página, só usar a chave de API da conta.
QIPLUS
ok, perfeito


Luiz | Pagar.me
Agora pra matar sua primeira pergunta. Pra pegar o recipient_id é preciso o recebedor criado.
QIPLUS
e outra dúvida que tenho é se é possível numa assinatura que o split seja diferente para a primeira transação e outro para as recorrências

ah, desculpe, continue


Luiz | Pagar.me
Daí você consegue ir na dashboard em: Ver minha conta > Recebedores. Lá terão todos os recebedores criados e seus respectivos Ids.

Nada! Só queria matar pra que você não ficasse com dúvidas, rs.
QIPLUS
ok, perfeito

então, a outra dúvida que tenho é se é possível numa assinatura que o split seja diferente para a primeira transação e outro para as recorrências


Luiz | Pagar.me
Quanto a outra dúvida, só pra ficar alinhado, é preciso criar o recebedor primeiro, depois criar um plano e por último fazer o split pra quando for criada a assinatura, o recebedor receba pelo plano também.
QIPLUS
ok, entendo. Mas ainda não ficou claro pra mim se é possível, quando crio a assinatura com split_rules, se é possível informar uma porcentagem para o primeiro pagamento e outra porcentagem para os pagamentos recorrentes


Luiz | Pagar.me
E não é possível configurar que o split seja diferente. A recorrência fica repetindo a primeira transação configurada.
QIPLUS
entendi, então não é possível programar antecipadamente para que seja diferente, mas depois de criada a assinatura é possível alterar as configurações de split por api?


Luiz | Pagar.me
Depois de criada a assinatura ela vai seguir o mesmo plano da primeira configuração. Só é possível alterar se você fizer um novo plano.
QIPLUS
ok. É um pouco limitador, pois se quero que meu afiliado receba uma porcentagem maior ou menor, tenho que alterar o plano do meu cliente, certo?

Luiz | Pagar.me
Isso mesmo, Marcelo! Você configura o plano e o split dessa assinatura, a assinatura não tem como ser alterada. Caso queira efetuar uma troca, é realmente preciso fazer um novo plano.