import { Snac<PERSON>bar } from "@material-ui/core";
import CompareItens from "Components/CompareItens";
import { langMessages } from "Lang/index";
import React, { useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Alert } from "reactstrap";
import { ApiKeyRepository } from '../../../services/apiKeyRepository';
import { ModuleContext } from '../contexts/ModuleContext';

const Config = () => {

    const {
        setAttribute,
        setOnSavePost,
    } = useContext(ModuleContext)

    const { account } = useSelector(state => state.authReducer)
    const service = new ApiKeyRepository(account.id)

    const [sniperToken, setSniperToken] = useState([])
    const [snackBarMessage, setSnackBarMessage] = useState(null)

    useEffect(() => {
        setOnSavePost(() => (status) => saveConfig(status))
    }, [sniperToken])


    useEffect(() => {
        setAttribute({ title: "Configurações", showSaveButton: true, showSearchBar: false })
        getApiKeys()

    }, [])

    const saveConfig = () => {

        const invalid = validateTokens()
        if (invalid === true) {
            setSnackBarMessage({
                severity: 'warning',
                message: langMessages['errors.emptyInput'],
            })
        } else {
            setSnackBarMessage({
                severity: 'success',
                message: langMessages['shotx.apiKeys.saveApiKeysSucessfully'],
            })
            service.update(sniperToken)
        }
    }

    const validateTokens = () => {
        const validation = sniperToken.map((t, i) => {

            if (t.description.length === 0 || t.token.length === 0) {
                return true
            }

            return false
        })
        return validation.includes(true)
    }

    const getApiKeys = async () => {
        const onSuccess = (document) => {
            if (document.sniperTokens) {
                setSniperToken(document.sniperTokens)
            } else {
                setSniperToken([{
                    description: "",
                    token: ""
                }])
            }

        }

        const onError = (error) => {
            console.log('ONERROR', error)
        }
        return await service.getAll(onSuccess, onError)
    }

    return (

        <span>

            <div>

                {snackBarMessage && <Snackbar
                    anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                    open={!!snackBarMessage}
                    onClose={() => setSnackBarMessage(null)}
                    autoHideDuration={5000}
                >
                    <Alert onClose={() => setSnackBarMessage(null)} severity="warning">
                        {snackBarMessage.message}
                    </Alert>
                </Snackbar>}
            </div>
            <div className="projects-wrapper" style={{ flexGrow: 1 }}>
                <h2 style={{ margin: "15px" }}>{langMessages['shotx.add.apiKeys']}</h2>

                <CompareItens
                    state={sniperToken}
                    setState={setSniperToken}
                    associatedFieldName={'description'}
                    fieldToAssociateName={langMessages['texts.token'].toLowerCase()}
                    associatedFieldTitle={langMessages['widgets.description']}
                    fieldToAssociateTitle={langMessages['texts.token']}
                />
            </div>
        </span>

    )
}

export default Config
