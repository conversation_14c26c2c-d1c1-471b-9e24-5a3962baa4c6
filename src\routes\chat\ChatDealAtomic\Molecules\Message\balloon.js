import React from 'react'
import { MessageDetails } from '../MessageDetails/MessageDetails'
import { ChatItem, Message } from './balloon.style'

export const MessageBalloon = ({
  mykey,
  message,
  theme,
  onResendMessage,
  dateFormat,
  children
}) => {
  const { sender } = message
  return (
    <ChatItem key={mykey}>
      {/* Corpo da Mensagem */}
      <Message sender={`${sender}`} receiver={`${!sender}`} theme={theme}>
        {children}
        {/* Detalhes da Mensagem */}
        <MessageDetails message={message} handleResendMessage={onResendMessage} messageDateFormat={dateFormat} />
      </Message>
    </ChatItem>
  )
}
