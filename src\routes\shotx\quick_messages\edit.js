import { insertTextAtCursor } from 'Helpers/input';
import React from 'react';
import { QuickMessagesAccordion } from "../components/QuickMessagesAccordion";
import { config } from "./config";

const { langMessages } = require("Lang/index");

export const editMessage = ({ onForward, values, onChange, canBack = false }) => {
  const { vars } = config
  return {
    content: {
      title: langMessages['quickMessages.module.title'],
      description: langMessages['quickMessages.module.tooltip'],

      inputs: [
        {
          type: 'text',
          name: 'title',
          label: langMessages['components.title'],
          value: values.title,
          onChange: (e) => onChange({ ...values, title: e.target.value })
        },
        {
          type: 'textArea',
          name: 'message',
          label: langMessages['quickMessages.message'],
          value: values.message,
          onChange: (e) => onChange({ ...values, message: e.target.value }),
        },
        {
          type: 'checkboxGroup',
          label: langMessages['quickMessages.releaseOn'],
          values: [
            {
              type: 'checkbox',
              name: 'enabledOnQuickMessage',
              tooltips: langMessages['quickMessages.tooltipShotx'],
              label: langMessages['quickMessages.enabledOnQuickMessage'],
              enabled: !values.message?.trim(),
              checked: values.enabledOnQuickMessage,
              onChecked: (e) => onChange({ ...values, enabledOnQuickMessage: e.target.checked }),
            }, {
              type: 'checkbox',
              name: 'enabledOnAutomation',
              tooltips: langMessages['quickMessages.tooltipAutomation'],
              label: langMessages['quickMessages.enabledOnAutomation'],
              checked: values.enabledOnAutomation,
              onChecked: (e) => onChange({ ...values, enabledOnAutomation: e.target.checked }),
            },
          ]
        },
      ],
      children:
        (
          <QuickMessagesAccordion
            onClick={(value) => {
              const { shortcode } = value
              const el = document.getElementById('message')
              const result = insertTextAtCursor(shortcode, el)
              onChange({ ...values, message: result })
            }}
          />
        )

    },
    backText: langMessages['quickMessages.cancel'],
    buttonText: langMessages['button.save'],
    disableNext: !values.message?.trim(),
    onForward,
    canBack,
  }

}
